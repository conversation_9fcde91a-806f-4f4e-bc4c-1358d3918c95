FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

ARG DB_HOST
ARG DB_NAME
ARG DB_USER
ARG DB_PORT
ARG BITBUCKET_VAPAR_REPO_TOKEN
ARG BASE_URL
ARG CLIENT_URL

# Install system dependencies in one layer
RUN set -ex && \
    apt-get update -y && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    pango1.0-tests \
    gdal-bin \
    libgdk-pixbuf2.0-0 \
    libcairo2-dev \
    libpango1.0-0 \
    binutils \
    libproj-dev \
    postgresql-client \
    libpangocairo-1.0-0 \
    shared-mime-info \
    python3-pip \
    python3-cffi \
    python3-brotli \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    wget \
    curl \
    make \
    git \
    apt-transport-https ca-certificates gnupg2 software-properties-common \
    && apt-get update --fix-missing && \
    rm -rf /var/lib/apt/lists/*

# Install docker (for building deployment images with docker-in-docker)
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | apt-key add - \
    && add-apt-repository "deb https://download.docker.com/linux/debian $(lsb_release -cs) stable" \
    && add-apt-repository "deb https://download.docker.com/linux/debian $(lsb_release -cs) stable" \
    && apt-get update -y \
    && apt-get install -y docker-ce docker-ce-cli



# Add any static environment variables needed by Django or your settings file here:
ENV DJANGO_SETTINGS_MODULE=config.settings
