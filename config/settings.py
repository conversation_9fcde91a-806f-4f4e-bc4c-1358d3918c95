"""
Django settings for vapar project.

Generated by 'django-admin startproject' using Django 4.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

# pylint: disable=line-too-long
import os
import sys
from pathlib import Path
from glob import glob
from datetime import timedelta
from dotenv import load_dotenv
from vapar.lib.logging import LoggingSettings, build_logging_config

from api.common.enums import StatusEnum

load_dotenv()


def _getenv_bool(var_name: str, default: bool = False) -> bool:
    """Get a boolean value from an environment variable."""
    value = os.getenv(var_name)
    if value is None:
        return default
    return value.lower() == "true"


TESTING = "pytest" in sys.modules or "test" in sys.argv
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

ROOT_URLCONF = "config.urls"
WSGI_APPLICATION = "config.wsgi.application"

GEOS_LIBRARY_PATH = None
GDAL_LIBRARY_PATH = None

try:
    gdal_path = glob("/usr/lib/libgdal.so.*")
    geos_path = glob("/usr/lib/libgeos_c.so.*")
    if len(gdal_path) > 0:
        GDAL_LIBRARY_PATH = glob("/usr/lib/libgdal.so.*")[0]
    if len(geos_path) > 0:
        GEOS_LIBRARY_PATH = glob("/usr/lib/libgeos_c.so.*")[0]
    if GDAL_LIBRARY_PATH is None:
        raise ImportError
except ImportError:
    GDAL_LIBRARY_PATH = os.environ.get("GDAL_LIBRARY_PATH")
    GEOS_LIBRARY_PATH = os.environ.get("GEOS_LIBRARY_PATH")


VERSION = os.getenv("VERSION", "3.10.2")

# Note: To use with Azurite storage emulator, use the url: "http://127.0.0.1:10000/devstoreaccount1/"
# (The trailing slash is needed for urls to be built correctly)
PLATFORM_BLOB_STORAGE_REGION_URLS = {
    "AU": os.getenv("BLOB_STORAGE_URL_AU", "https://vapardevstorage.blob.core.windows.net/"),
    "US": os.getenv("BLOB_STORAGE_URL_AU", "https://vapardevstorage.blob.core.windows.net/"),
    "GB": os.getenv("BLOB_STORAGE_URL_UK", "https://vapardevstorage.blob.core.windows.net/"),
    "NZ": os.getenv("BLOB_STORAGE_URL_AU", "https://vapardevstorage.blob.core.windows.net/"),
    "default": os.getenv("BLOB_STORAGE_URL_AU", "https://vapardevstorage.blob.core.windows.net/"),
}

PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS = {
    "AU": os.getenv("PLATFORM_STORAGE_AU"),  # AU is the default region
    "US": os.getenv("PLATFORM_STORAGE_AU"),
    "GB": os.getenv("PLATFORM_STORAGE_UK"),
    "NZ": os.getenv("PLATFORM_STORAGE_AU"),
    "default": os.getenv("PLATFORM_STORAGE_AU"),
}

# Region specific storage settings (video uploads)
PROCESSING_BLOB_STORAGE_REGION_URLS = {
    "AU": os.getenv("BACKEND_STORAGE_URL_AU", "https://aibackend.blob.core.windows.net/"),
    "US": os.getenv("BACKEND_STORAGE_URL_AU", "https://aibackend.blob.core.windows.net/"),
    "GB": os.getenv("BACKEND_STORAGE_URL_UK", "https://ukbackend.blob.core.windows.net/"),
    "NZ": os.getenv("BACKEND_STORAGE_URL_AU", "https://aibackend.blob.core.windows.net/"),
    "default": os.getenv("BACKEND_STORAGE_URL_AU", "https://aibackend.blob.core.windows.net/"),
}

PROCESSING_STORAGE_ACCOUNT_REGION_CONN_STRS = {
    "AU": os.getenv("BACKEND_STORAGE_AU"),
    "US": os.getenv("BACKEND_STORAGE_AU"),
    "GB": os.getenv("BACKEND_STORAGE_UK"),
    "NZ": os.getenv("BACKEND_STORAGE_AU"),
    "default": os.getenv("BACKEND_STORAGE_AU"),
}

# Blob storage container names
BLOB_STORAGE_RESULTS_CONTAINER = "resultsfiles"
BLOB_STORAGE_FRAMES_CONTAINER = "videoframefiles"
BLOB_STORAGE_VIDEOS_CONTAINER = "uploadedvideofiles"
BLOB_STORAGE_LOGOS_CONTAINER = "orglogos"
BLOB_STORAGE_ORGFILES_CONTAINER = "orgfiles"
BLOB_STORAGE_CSV_CONTAINER = "csvfiles"
BLOB_STORAGE_XML_CONTAINER = "xmlfiles"
BLOB_STORAGE_PDF_CONTAINER = "pdffiles"
BLOB_STORAGE_DRT_CONTAINER = "drtfiles"
BLOB_STORAGE_IMPORTS_CONTAINER = "import-files"

VIDEO_DOMAIN = os.getenv("VIDEO_DOMAIN", "https://vaparsolutionk8s.azurefd.net/api/vids/")
VIDEO_PLAY_DOMAIN = os.getenv("VIDEO_PLAY_DOMAIN", "https://vaparsolutionk8s.azurefd.net/api/playurl/")
PDF_DOMAIN = os.getenv("PDF_DOMAIN", "https://vaparsolutionk8s.azurefd.net/api/assetpdf/")
CDN_DOMAIN = os.getenv("CDN_DOMAIN", "https://vaparcdn.azureedge.net/")

VIDEO_PROCESSING_AZURE_STORAGE_QUEUE_NAME = os.getenv(
    "VIDEO_PROCESSING_AZURE_STORAGE_QUEUE_NAME", "uploadedvideofiles-items"
)

EXPORTS_AZURE_STORAGE_QUEUE_NAME = os.getenv("EXPORTS_AZURE_STORAGE_QUEUE_NAME", "exports")
IMPORTS_AZURE_STORAGE_QUEUE_NAME = os.getenv("IMPORTS_AZURE_STORAGE_QUEUE_NAME", "imports")

LOGIN_URL = "rest_framework:login"
LOGOUT_URL = "rest_framework:logout"

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-y29)r5&3*)biqcm+0)=m&-!#1@w&#(76^in51vxx&b1jfhs%dt"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = _getenv_bool("DEBUG")

RUN_TOOLBAR = _getenv_bool("RUN_TOOLBAR")

ALLOWED_HOSTS = ["*"]

VAPAR_WEB_URL = os.environ.get("VAPAR_WEB_URL")
VAPAR_SERVER_URL = os.environ.get("VAPAR_SERVER_URL")
VAPAR_RESET_PASSWORD_URL = os.environ.get("VAPAR_RESET_PASSWORD_URL")
VAPAR_ACTIVATE_SUCCESS_URL = os.environ.get("VAPAR_ACTIVATE_SUCCESS_URL")

VAPAR_ACTIVATION_URL = os.environ.get("VAPAR_ACTIVATION_URL", "http://localhost:3000/activate")
VAPAR_RESET_URL = os.environ.get("VAPAR_RESET_URL")

# Application definition

DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_countries",
    "treebeard",
    "corsheaders",
]

THIRD_PARTY_APPS = [
    "debug_toolbar",
    "rest_framework",
    "rest_framework.authtoken",
    "rest_framework_api_key",
    "drf_spectacular",
    "django_password_validators",
]

VAPAR_APPS = [
    "api.actions.apps.ActionsConfig",
    "api.base.apps.BaseConfig",
    "api.exports.apps.ExportsConfig",
    "api.defects.apps.DefectsConfig",
    "api.inspections.apps.InspectionsConfig",
    "api.files.apps.FilesConfig",
    "api.organisations.apps.OrganisationsConfig",
    "api.recommendations.apps.RecommendationsConfig",
    "api.users.apps.UsersConfig",
    "api.external.apps.ExternalConfig",
    "api.imports.apps.ImportsConfig",
    "rest_framework_swagger",
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + VAPAR_APPS

MIDDLEWARE = [
    "django.middleware.gzip.GZipMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "api.middleware.version_middleware.VersionHeaderMiddleware",
]

DEBUG_TOOLBAR_PANELS = [
    "debug_toolbar.panels.history.HistoryPanel",
    "debug_toolbar.panels.versions.VersionsPanel",
    "debug_toolbar.panels.timer.TimerPanel",
    "debug_toolbar.panels.settings.SettingsPanel",
    "debug_toolbar.panels.headers.HeadersPanel",
    "debug_toolbar.panels.request.RequestPanel",
    "debug_toolbar.panels.sql.SQLPanel",
    "debug_toolbar.panels.staticfiles.StaticFilesPanel",
    "debug_toolbar.panels.templates.TemplatesPanel",
    "debug_toolbar.panels.cache.CachePanel",
    "debug_toolbar.panels.signals.SignalsPanel",
    "debug_toolbar.panels.redirects.RedirectsPanel",
    "debug_toolbar.panels.profiling.ProfilingPanel",
]

DEBUG_TOOLBAR_CONFIG = {}


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "static/templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ]
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.contrib.gis.db.backends.postgis",
        "HOST": os.environ.get("DB_HOST"),
        "NAME": os.environ.get("DB_NAME", "ix"),
        "USER": os.environ.get("DB_USER"),
        "PASSWORD": os.environ.get("DB_PASSWORD"),
        "PORT": os.environ.get("DB_PORT"),
        "TEST": {
            "NAME": "vapartestdb",
        },
        "OPTIONS": {},
    },
    # "test": {
    #     "ENGINE": "django.contrib.gis.db.backends.postgis",
    #     "HOST": os.environ.get("DB_HOST", "127.0.0.1"),
    #     "NAME": os.environ.get("DB_NAME", "vapartestdb"),
    #     "USER": os.environ.get("DB_USER", ""),
    #     "PASSWORD": os.environ.get("DB_PASSWORD", ""),
    #     "PORT": os.environ.get("DB_PORT", 5432),
    #     "OPTIONS": {},
    # },
}

if DB_USE_SSL := _getenv_bool("DB_USE_SSL"):
    DATABASES["default"]["OPTIONS"] = {
        "sslmode": "verify-full",
        "sslrootcert": os.path.join(BASE_DIR, "ssl", "ms-rsa-root-CA-2017.pem"),  # Path to the certificate
    }

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.environ.get("EMAIL_HOST")
EMAIL_PORT = os.environ.get("EMAIL_PORT")
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_HOST_PASSWORD")

if os.environ.get("EMAIL_USE_SSL", None):
    EMAIL_USE_SSL = os.environ.get("EMAIL_USE_SSL")
else:
    EMAIL_USE_TLS = os.environ.get("EMAIL_USE_TLS", True)


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django_password_validators.password_character_requirements.password_validation.PasswordCharacterValidator",
        "OPTIONS": {
            "min_length_digit": 1,
            "min_length_alpha": 1,
            "min_length_special": 1,
            "min_length_lower": 1,
            "min_length_upper": 1,
            "special_characters": "-!@$%^&*().?<>'",
        },
    },
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

if DEBUG and RUN_TOOLBAR:
    import socket  # only if you haven't already imported this

    hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
    INTERNAL_IPS = [ip[:-1] + "1" for ip in ips] + ["127.0.0.1", "********"]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/
STATIC_URL = os.path.join(BASE_DIR, "static/")
STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]
# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        "api.common.authentication.ApiKeyAuthentication",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 100,
    "EXCEPTION_HANDLER": "api.common.exceptions.custom_exception_handler",
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

SPECTACULAR_SETTINGS = {
    "TITLE": "VAPAR API",
    "VERSION": "v3",
    "DESCRIPTION": "VAPAR API",
    "CONTACT": {
        "email": "<EMAIL>",
    },
    "LICENSE": {
        "name": "BSD License",
    },
    "TOS": "https://www.google.com/policies/terms/",
    "POSTPROCESSING_HOOKS": [
        "drf_spectacular.hooks.postprocess_schema_enums",
        "drf_spectacular.contrib.djangorestframework_camel_case.camelize_serializer_fields",
        # "config.register_schema.custom_components_postprocessing_hook",
    ],
    "COMPONENT_SPLIT_REQUEST": True,
    "SCHEMA_PATH_PREFIX": "/api/v3",
    "SERVE_PUBLIC": DEBUG,
    "ENUM_NAME_OVERRIDES": {
        "ProcessingStatusEnum": "vapar.constants.processing.ProcessingStatusEnum",
        "ProcessingStatusReasonEnum": "vapar.constants.processing.ProcessingStatusReasonEnum",
        "InspectionStatusEnum": "api.common.enums.StatusEnum",  # The variation generated for some endpoints
        "InspectionStatusNamedEnum": [(v.value, v.value) for v in StatusEnum],  # The variation used in the DB model
        "ExportTypeEnum": "vapar.constants.exports.ExportType",
        "ExportFormatEnum": "vapar.constants.exports.ExportFormat",
        "ExportStatusEnum": "vapar.constants.exports.ExportStatus",
        "ExportStatusReasonEnum": "vapar.constants.exports.ExportStatusReason",
        "ImportTypeEnum": "vapar.constants.imports.ImportTypeEnum",
        "ImportStatusEnum": "vapar.constants.imports.ImportStatusEnum",
        "ImportStatusReasonEnum": "vapar.constants.imports.ImportStatusReasonEnum",
        "ImportValidationStatusEnum": "vapar.constants.imports.ImportValidationStatusEnum",
        "ImportFileValidationStatusEnum": "vapar.constants.imports.ImportFileValidationStatusEnum",
        "TextTabularFormatEnum": ["CSV", "XML"],
    },
}

AUTH_USER_MODEL = "users.CustomUser"

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=int(os.environ.get("ACCESS_TOKEN_LIFETIME", 720))),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "JWK_URL": None,
    "LEEWAY": 0,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",
    "JTI_CLAIM": "jti",
    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=7),
}

MAX_CHAINAGE = 9999

# Image URL encryption
IMAGE_SECRET_KEY = b"vaparultimatekey@0528"
SALT = b'\rp\x98\xad\xfamk\xcb"\xb7\xe58\xcb\xe4\x93\xc8'

# REDIS
REDIS_URL = "redis://{host}:{port}/1".format(
    host=os.getenv("REDIS_HOST", "redis-service"), port=os.getenv("REDIS_PORT", "6379")
)

# Caching
DEFAULT_SAS_CACHE_TIMEOUT = int(os.getenv("SAS_CACHE_TIMEOUT", 60 * 60 * 4))  # 4 hours
DEFAULT_ENTITY_CACHE_TIMEOUT = int(os.getenv("ENTITY_CACHE_TIMEOUT", 60 * 60 * 24 * 7))  # 7 days
VIEW_CACHE_TIMEOUT = int(os.getenv("VIEW_CACHE_TIMEOUT", 60 * 60 * 24 * 7))  # 7 days

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "sas-tokens",
        "OPTIONS": {
            "TIMEOUT": DEFAULT_SAS_CACHE_TIMEOUT,
        },
    },
    "inspections": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "MAX_ENTRIES": 100000,
            "TIMEOUT": DEFAULT_ENTITY_CACHE_TIMEOUT,
        },
    },
    "assets": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "MAX_ENTRIES": 100000,
            "TIMEOUT": DEFAULT_ENTITY_CACHE_TIMEOUT,
        },
    },
    "view-cache": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "MAX_ENTRIES": 1_000,
            "TIMEOUT": VIEW_CACHE_TIMEOUT,
            "KEY_PREFIX": "view-cache",  # To avoid collisions with other caches using the same Redis instance
        },
    },
}

USE_EVENTHUB_ANALYTICS = _getenv_bool("USE_EVENTHUB_ANALYTICS")
REPAIR_RECOMMENDATION_CREATED_EVENTHUB_NAME = os.getenv("EVENTHUB_REPAIR_RECOMMENDATIONS_CREATED_NAME", "")
REPAIR_RECOMMENDATION_UPDATED_EVENTHUB_NAME = os.getenv("EVENTHUB_REPAIR_RECOMMENDATIONS_UPDATED_NAME", "")
REPAIR_RECOMMENDATION_APPLIED_EVENTHUB_NAME = os.getenv("EVENTHUB_REPAIR_RECOMMENDATIONS_APPLIED_NAME", "")
DEFECT_UPDATED_EVENTHUB_NAME = os.getenv("EVENTHUB_DEFECT_UPDATED_NAME", "")
ASSET_EVENTHUB_NAME = os.getenv("EVENTHUB_ASSET_NAME", "")
INSPECTION_EVENTHUB_NAME = os.getenv("EVENTHUB_INSPECTION_NAME", "")

LOGO_URL = os.getenv("LOGO_URL", "https://www.vapar.solutions/static/media/logo.bed463ae.svg")

API_KEY_CUSTOM_HEADER = "HTTP_X_API_KEY"

DISABLE_SYNC_EXPORT_SLEEP = False
EXTERNAL_INSPECTIONS_XML_VIEW_TIMEOUT_SECS = 60
EXTERNAL_INSPECTIONS_PDF_VIEW_TIMEOUT_SECS = 120

UPLOAD_COMPLETE_POLL_TIMEOUT_SECS = 30
UPLOAD_COMPLETE_POLL_INTERVAL_SECS = 2

ASSET_LIST_MAX_PAGE_SIZE = 100
ASSET_LIST_DEFAULT_PAGE_SIZE = 25

CODE_VARIANTS_LIST_CACHE_TTL_SECS = int(os.getenv("CODE_VARIANTS_LIST_CACHE_TTL_SECS", 60 * 15))
FEATURES_LIST_CACHE_TTL_SECS = int(os.getenv("FEATURES_LIST_CACHE_TTL_SECS", 60 * 15))

NASSCO_VALIDATOR_CLIENT_EXECUTABLE_PATH = os.environ.get(
    "NASSCO_VALIDATOR_CLIENT_EXECUTABLE_PATH", "resources/NASSCO/NasscoConsoleValidator-1.8.1-linux-x64"
)

IMPORTS_MAX_UPLOAD_SIZE = int(os.getenv("IMPORTS_MAX_UPLOAD_SIZE", 20_000_000))  # Default 20MB

if not TESTING:  # Let pytest control logging when running tests
    logging_settings = LoggingSettings()
    if "level" not in logging_settings.model_fields_set:
        logging_settings.level = "ERROR"  # Used when not set in env

    logging_config = build_logging_config(logging_settings)
    logging_config.setdefault("loggers", {}).update(
        # Override the loggers django configures by default
        {
            "django": {},
            "django.server": {},
        }
    )
    LOGGING = logging_config

FALLBACK_FRAME_IMAGE_URL = "videoframefiles/fallback.png"
