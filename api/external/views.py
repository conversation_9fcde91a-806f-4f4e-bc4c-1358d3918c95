import os
import xml.etree.ElementTree as ET

import requests
from django.contrib.auth import get_user_model
from django.core.cache import caches
from django.db.transaction import atomic
from django.forms import model_to_dict
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from djangorestframework_camel_case.parser import CamelC<PERSON>J<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiRequest
from rest_framework import status
from rest_framework.exceptions import ParseError, ValidationError
from rest_framework.generics import (
    RetrieveUpdateAPIView,
)
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from rest_framework.views import APIView
from treebeard.mp_tree import MP_Node
from vapar.constants.exports import ExportFormat
from vapar.constants.processing import ProcessingStatusEnum
from vapar.core.exports import DefectExportPayload

from api.actions.models import AuditList
from api.common import storage
from api.common.json_util import MapPointLinkJSON, VideoFrameJSON
from api.common.permissions import IsStandardUser, IsAuthenticated, OrgCanUpload, IsAssetOwnerOrg
from api.common.storage import (
    get_platform_storage_sas_token,
    should_use_processing_storage,
    get_platform_blob_url_with_sas,
)
from api.defects.models import Standard, StandardHeader
from api.exports.models import Export, ExportOutput
from api.exports.serializers import ExportSerializer
from api.exports.sync import run_exports_synchronously
import api.exports.views as exports_views
from api.external import schemas
from api.files.models import (
    FileList,
    JobsTree,
    ProcessingList,
    ResultsFile,
    VideoFrames,
)
from api.files.serializers import FramesListSerializer
from api.inspections.gradings import update_gradings
from api.inspections.models import (
    AssetValue,
    Inspection,
    InspectionValue,
    MapPointList,
)
from api.inspections.pydantic_models.inspection_model import InspectionModel, get_inspection_representation
from api.inspections.serializers.asset_serializers import AssetValueSerializer
from api.inspections.serializers.inspection_serializers import (
    InspectionSerializer,
    InspectionValueSerializer,
)
from api.inspections.utilities.validate_inspections import validate_standard_value
from api.inspections.views.inspection_views import BaseInspection, InspectionList2, handle_nz_direction
from api.organisations.models import Contractors, Organisations
from api.users.models import CustomUser

User = get_user_model()

sas_cache = caches["default"]

VIDEO_IDS_PARAM = OpenApiParameter(
    "video_ids",
    OpenApiTypes.INT,
    description="Comma separated list of video ids",
    required=True,
    many=True,
    explode=False,
)


def map_xml_to_inspection(file):
    section_map = {
        "PCDStartNode": "start_node",
        "PCDEndNode": "end_node",
        "PCDPipeMaterial": "material",
        "PCDPipeDimension1": "diameter",
        "PCDStartStreetName": "name",
        # "PCDMunicipal": "location_town",
    }
    sectioninsp_map = {
        "PIDDirection": "direction",
        "PIDDate": "date_captured",
        "PIDRemarks": "inspection_notes",
        "PIDLength": "chainage",
    }

    tree = ET.parse(file)
    root = tree.getroot()

    sec_section = []

    try:
        sec_section = [part for part in root if part.tag == "section"][0]
    except Exception:
        raise ValidationError("Could not find section")

    asset_obj = {}
    inspection_obj = {}

    # Build the object
    for child in sec_section:
        if child.tag in section_map:
            asset_obj[section_map[child.tag]] = child.text
        if child.tag == "sectioninspection":
            for si in child:
                if si.tag in sectioninsp_map:
                    if sectioninsp_map[si.tag] == "direction":
                        attributes = si.attrib
                        inspection_obj[sectioninsp_map[si.tag]] = attributes["codetext"]
                    else:
                        inspection_obj[sectioninsp_map[si.tag]] = si.text

                if si.tag == "sectionobservation":
                    try:
                        _ = [part for part in si if part.tag == "podmovie"][0]
                        break
                    except Exception:
                        raise ValidationError("Could not find podmovie")

    return _, asset_obj, inspection_obj


def map_inspection_values(inspection_values, asset_values, inspection_obj, asset_obj):
    inspection_point = {}
    asset_point = {}
    for inspection_value in inspection_values:
        if inspection_value.standard_header.header.mapped_mpl_field in inspection_obj:
            inspection_point[inspection_value.standard_header.header.mapped_mpl_field] = inspection_value.value
    for asset_value in asset_values:
        if asset_value.standard_header.header.mapped_mpl_field in asset_obj:
            asset_point[asset_value.standard_header.header.mapped_mpl_field] = asset_value.value

    if inspection_obj["direction"] == "Upstream":
        asset_point["upstream_node"] = asset_obj["start_node"]
        asset_point["downstream_node"] = asset_obj["end_node"]
    else:
        asset_point["upstream_node"] = asset_obj["end_node"]
        asset_point["downstream_node"] = asset_obj["start_node"]

    return asset_point, inspection_point


def validate_job_id(job_id, org_id):
    org = Organisations.objects.none()
    root_node = JobsTree.objects.none()
    try:
        org = Organisations.objects.filter(id=org_id).first()
        root_node: MP_Node = JobsTree.objects.get(primary_org_id=org_id)
    except Exception:
        return None

    try:
        job_id = int(job_id)
    except Exception:
        job_id = None

    if job_id is None:
        unalloc_job = root_node.get_descendants().filter(job_name="Unallocated").first()
        if unalloc_job:
            job_id = unalloc_job.pk
        else:
            job_id = None
    else:
        job_existed = root_node.get_descendants().filter(id=job_id).exists()
        if not job_existed and org.is_contractor:
            check_job = JobsTree.objects.filter(id=job_id)
            if check_job.exists():
                job_existed = check_job.first().get_ancestors().filter(secondary_org=org).exists()
        if not job_existed:
            job_id = None
    return job_id


def write_new_video_to_db(
    filename_dict,
    target_org_id,
    upload_org_id,
    upload_user: CustomUser,
    file_size,
    job_id,
    results_endpoint="",
    manual_qa=None,
    sewer_data=None,
    standard_key=None,
    results_file_id=None,
):
    target_org = Organisations.objects.get(pk=int(target_org_id))
    job = JobsTree.objects.get(pk=int(job_id))

    if manual_qa is None:
        manual_qa = target_org.manual_qa_required
    if sewer_data is None:
        sewer_data = target_org.sewer_data
    if standard_key is None:
        standard_key = job.standard_key
    else:
        standard_key = Standard.objects.get(pk=int(standard_key))
    if results_file_id is not None:
        results_file_id = ResultsFile.objects.get(pk=results_file_id)

    file_listing = {}
    for file_name, url in filename_dict.items():
        fname_only = os.path.split(file_name)[1]
        video = FileList(
            filename=fname_only,
            file_type="",
            file_size=file_size,
            url=url.replace("#", "%23"),
            target_org=target_org,
            upload_org=Organisations.objects.get(pk=int(upload_org_id)),
            upload_user=upload_user.full_name,
            uploaded_by=upload_user,
            job_tree=job,
            results_file=results_file_id,
            request_endpoint=results_endpoint,
        )
        video.save()

        file_listing[file_name] = {"Storage Path": url, "Tracking ID": video.pk}

        proc_file = ProcessingList(
            filename=fname_only,
            file_size=file_size,
            status=ProcessingStatusEnum.UPLOADING.value,
            created_time=str(timezone.now()),
            upload_user=upload_user.full_name,
            target_org=target_org,
            associated_file=video,
            request_endpoint=results_endpoint,
            manual_qa_required=manual_qa,
            sewer_data=sewer_data,
            standard_key=standard_key,
            # job_id=job_id
        )
        proc_file.save()
        processing_id = proc_file.id

    return file_listing, processing_id


class ExternalStandardsListView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(responses={HTTP_200_OK: list[dict]})
    def get(self, request):
        now = timezone.now()
        AuditList.objects.create(
            event_type="Get",
            table="Standard",
            description="External API get scoring standards list",
            date_of_modification=now,
            user=request.user,
        )

        return Response(Standard.objects.all().values(), status=status.HTTP_200_OK)


class ExternalJobsListView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=None, responses={HTTP_200_OK: list[dict]})
    def get(self, request):
        target_org_id = request.organisation.id
        is_contractor = Contractors.objects.filter(org__id=target_org_id).exists()

        root_node = JobsTree.objects.get(primary_org__id=target_org_id)
        my_jobs = root_node.get_descendants().filter(numchild=0)

        if is_contractor:
            ao_jobs = JobsTree.objects.filter(secondary_org__id=target_org_id)
            for ao_job in ao_jobs:
                my_jobs |= ao_job.get_descendants().filter(numchild=0)

        data = []
        for job in my_jobs:
            ancs = job.get_ancestors()
            # if the secondary org is not myself, dont include this
            if ancs.exclude(secondary_org=None).exclude(secondary_org__id=target_org_id).exists():
                continue
            job_path = [anc.job_name + " > " for anc in ancs]
            job_data = model_to_dict(job)
            job_data["job_name"] = "".join(job_path) + job_data["job_name"]
            data.append(job_data)

        sorted_data = sorted(data, key=lambda d: d["job_name"])

        now = timezone.now()
        AuditList.objects.create(
            event_type="Get",
            table="JobsTree",
            description="External API get jobs list",
            date_of_modification=now,
            user=request.user,
        )

        return Response(sorted_data, status=status.HTTP_200_OK)


class ExternalVideoResultsView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(parameters=[VIDEO_IDS_PARAM], responses={HTTP_200_OK: dict})
    def get(self, request):
        """
        Get validation results for a list of video ids
        """
        if "video_ids" not in request.query_params:
            raise ParseError("video_ids is a required query parameter")
        video_ids = request.query_params.get("video_ids").split(",")
        target_org = request.organisation

        results_dict = {}
        for video_id in video_ids:
            inspection = (
                Inspection.objects.filter(file__pk=video_id, file__target_org=target_org)
                .select_related("file")
                .select_related("file__job_tree")
                .select_related("file__job_tree__standard_key")
                .select_related("file__upload_org")
                .select_related("folder")
                .select_related("folder__standard_key")
                .select_related("created_by")
                .select_related("asset")
                .prefetch_related(
                    "inspectionvalue_set",
                    "asset__assetvalue_set",
                    "inspectionvalue_set__standard_header__header",
                    "asset__assetvalue_set__standard_header__header",
                )
                .first()
            )

            if inspection:
                frame_data = []
                frame_objs = (
                    VideoFrames.objects.prefetch_related("parent_video", "defect_scores")
                    .filter(parent_video__pk=video_id)
                    .filter(is_hidden=False)
                    .exclude(defect_scores__is_shown=False)
                )

                for frame in frame_objs:
                    frame_data.append(VideoFrameJSON(frame))

                results_dict[video_id] = {
                    "asset": MapPointLinkJSON(inspection, target_org),
                    "frames": frame_data,
                }
            else:
                results_dict[video_id] = "Not Found"

        return Response(results_dict, status.HTTP_200_OK)


class ExternalVideoImagesView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(parameters=[VIDEO_IDS_PARAM], responses={HTTP_200_OK: dict})
    def get(self, request):
        """
        Get validation results for a list of video ids
        """
        if "video_ids" not in request.query_params:
            raise ParseError("video_ids is a required query parameter")
        video_ids = request.query_params["video_ids"].split(",")

        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        sas_token = storage.get_platform_storage_sas_token(container)

        results_dict = {}
        for video_id in video_ids:
            frame_data = []
            not_shown = [
                "Title",
                "Textbox",
                "No_defect",
                "Liner Healthy",
                "No Defect",
                "Liner - Healthy liner",
                "Healthy Liner",
                "Lining Defective - Healthy liner",
                "Lifting_hole - Lifting_hole",
            ]
            frame_objs = (
                VideoFrames.objects.filter(parent_video__pk=video_id)
                .filter(is_hidden=False)
                .exclude(class_label__in=not_shown)
            )
            for frame in frame_objs:
                frame_data.append(
                    {
                        "frame_id": frame.id,
                        "url": get_platform_blob_url_with_sas(frame.image_location, sas_token=sas_token),
                    }
                )
            results_dict[video_id] = frame_data

        return Response(results_dict, status.HTTP_200_OK)


class ExternalSASView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "location",
                OpenApiTypes.STR,
                required=True,
                description="Location to store or retrieve file",
            ),
        ],
        responses={HTTP_200_OK: str},
    )
    def get(self, request):
        """
        Get validation results for a location
        """
        if "location" not in request.query_params:
            raise ParseError("location is a required query parameter")
        location = request.query_params["location"]
        container = location

        if "/" in location:
            container = location[: location.find("/")]

        if should_use_processing_storage(container, write_access=True):
            sas_write_token = storage.get_processing_storage_sas_token(
                container_name=container,
                write_access=True,
                region=request.organisation.country,
            )
        else:
            sas_write_token = storage.get_platform_storage_sas_token(
                container_name=container,
                write_access=True,
            )

        return Response(sas_write_token, status.HTTP_200_OK)


class ExternalInspectionsXMLView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(parameters=[VIDEO_IDS_PARAM], responses={HTTP_200_OK: dict[str, str]})
    def get(self, request):
        if "video_ids" not in request.query_params:
            raise ParseError("video_ids is a required query parameter")
        video_ids = request.query_params.get("video_ids").split(",")

        inspections = Inspection.objects.filter(file__in=video_ids, asset__organisation=request.organisation)
        if len(inspections) != len(video_ids):
            raise ValidationError("Videos not found for video_ids", status.HTTP_404_NOT_FOUND)

        export_payloads = [
            {
                "payload": DefectExportPayload(
                    inspection_ids=[insp.uuid],
                    format=ExportFormat.XML,
                ).model_dump(mode="json", by_alias=True),
            }
            for insp in inspections
        ]

        serializer = ExportSerializer(
            data=export_payloads,
            many=True,
            context={"request": request, "user": request.user, "is_initiated_by_user": False},
        )
        serializer.is_valid(raise_exception=True)
        exports: list[Export] = serializer.save()

        AuditList.objects.create(
            event_type="Export",
            table="Export",
            column="Multi",
            description=f"{len(exports)} exports created for organisation {request.organisation.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        successful_exports, failed_exports = run_exports_synchronously(
            exports=exports,
            org=request.organisation,
            max_poll_time_secs=settings.EXTERNAL_INSPECTIONS_XML_VIEW_TIMEOUT_SECS,
        )
        if failed_exports:
            return Response("Failed to create all exports", status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        inspections_by_id = {insp.uuid: insp for insp in inspections}
        exports_by_id = {exp.id: exp for exp in successful_exports}

        vid_ids_to_url = {}

        export_outputs = ExportOutput.objects.filter(export__in=exports_by_id.keys())
        for output in export_outputs:
            export = exports_by_id[output.export_id]
            inspection = inspections_by_id[export.parsed_payload.root.inspection_ids[0]]
            sas_url = get_platform_blob_url_with_sas(output.blob_url)
            video_id = str(inspection.file_id)
            vid_ids_to_url[video_id] = sas_url

        return Response(vid_ids_to_url, status.HTTP_200_OK)


class ExternalSubmitJobsView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, OrgCanUpload]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=OpenApiRequest(schemas.file_names_request_schema), responses={HTTP_200_OK: dict[str, dict]})
    def post(self, request):
        """
        Get validation results for a list of file names
        """
        if "file_names" not in request.data:
            raise ParseError("file_names is a required query parameter")

        files = request.data.get("file_names")
        userobj = request.user
        target_org_id = request.organisation.id

        # additional optional settings (otherwise we will just go with what's in the org settings)
        response_url = request.data.get("response_url", "")
        manual_qa = str(request.data.get("manual_qa", "false")).lower() == "true"
        sewer_data = str(request.data.get("sewer_data", "false")).lower() == "true"
        standard_key = request.data.get("standard_key", None)
        results_file_id = request.data.get("results_file_id", None)
        job_id = request.data.get("job_id", None)

        upload_org_id = target_org_id
        job_id = validate_job_id(job_id, target_org_id)
        if job_id is None:
            return Response({"Error": "Invalid Job ID"}, status=status.HTTP_400_BAD_REQUEST)

        container = settings.BLOB_STORAGE_VIDEOS_CONTAINER
        upload_timestamp = timezone.now().strftime("%d_%m_%Y_%H_%M_%S_%f")

        filename_dict = {}
        for file_name in files:
            name_only = os.path.split(file_name)[1]
            url = container + "/" + upload_timestamp + "-" + name_only
            filename_dict[file_name] = url

        file_listing, _ = write_new_video_to_db(
            filename_dict,
            target_org_id,
            upload_org_id,
            userobj,
            0,
            job_id,
            results_endpoint=response_url,
            manual_qa=manual_qa,
            sewer_data=sewer_data,
            standard_key=standard_key,
            results_file_id=results_file_id,
        )

        sas_write_token = storage.get_processing_storage_sas_token(
            container_name=container,
            write_access=True,
            region=request.organisation.country,
        )

        for _, value in file_listing.items():
            value["Storage Path"] = storage.get_processing_blob_url_with_sas(
                blob_path=value["Storage Path"],
                region=request.organisation.country,
                sas_token=sas_write_token,
            )

        return Response(file_listing, status=status.HTTP_201_CREATED)


class ExternalSubmitJobView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        request=OpenApiRequest(schemas.file_name_request_schema),
        responses={200: dict[str, dict]},
    )
    def post(self, request):
        """
        Get validation results for a file name and download url
        """

        if "file_name" not in request.data:
            raise ParseError("file_name is a required query parameter")
        if "download_url" not in request.data:
            raise ParseError("download_url is a required query parameter")

        file_name = request.data.get("file_name")
        download_url = request.data.get("download_url")

        userobj = request.user
        target_org_id = request.organisation.id

        # additional optional settings (otherwise we will just go with what's in the org settings)
        response_url = request.data.get("response_url", "")
        manual_qa = str(request.data.get("manual_qa")).lower() == "true"
        sewer_data = str(request.data.get("sewer_data")).lower() == "true"
        standard_key = request.data.get("standard_key", None)
        results_file_id = request.data.get("results_file_id", None)
        job_id = request.data.get("job_id", None)

        upload_org_id = target_org_id

        job_id = validate_job_id(job_id, target_org_id)
        if job_id is None:
            return Response({"Error": "Invalid Job ID"}, status=status.HTTP_400_BAD_REQUEST)

        container = settings.BLOB_STORAGE_VIDEOS_CONTAINER
        upload_timestamp = timezone.now().strftime("%d_%m_%Y_%H_%M_%S_%f")

        upload_name = upload_timestamp + "-" + file_name
        url = container + "/" + upload_name
        filename_dict = {file_name: url}

        file_listing, _ = write_new_video_to_db(
            filename_dict,
            target_org_id,
            upload_org_id,
            userobj,
            0,
            job_id,
            results_endpoint=response_url,
            manual_qa=manual_qa,
            sewer_data=sewer_data,
            standard_key=standard_key,
            results_file_id=results_file_id,
        )

        container = settings.BLOB_STORAGE_VIDEOS_CONTAINER
        blob_client = storage.get_processing_blob_client(upload_name, container, request.organisation.country)
        blob_client.start_copy_from_url(download_url)

        return Response(file_listing, status=status.HTTP_201_CREATED)


class ExternalUploadProgressView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            VIDEO_IDS_PARAM,
            OpenApiParameter(
                "ping_endpoint", OpenApiTypes.BOOL, required=False, description="Ping the endpoint for response"
            ),
        ],
        responses={HTTP_200_OK: dict},
    )
    def get(self, request):
        """
        Get validation results for a list of video ids
        """
        if "video_ids" not in request.query_params:
            raise ParseError("video_ids is a required query parameter")
        video_ids = request.query_params.get("video_ids").split(",")
        target_org = request.organisation
        ping_endpoint = request.query_params.get("ping_endpoint", "false").lower() == "true"

        progress_dict = {}
        for video_id in video_ids:
            processing_obj = ProcessingList.objects.filter(target_org=target_org).filter(associated_file__pk=video_id)
            if processing_obj:
                progress_dict[video_id] = processing_obj[0].status
            else:
                inspection = Inspection.objects.filter(file__pk=video_id, file__target_org=target_org).first()
                if inspection:
                    progress_dict[video_id] = "Complete"
                else:
                    progress_dict[video_id] = "Not Found"

        if ping_endpoint is True:
            for vid_id, progress in progress_dict.items():
                vid_obj = FileList.objects.filter(pk=vid_id).first()

                if not vid_obj or vid_obj.request_endpoint == "":
                    continue
                inspection = (
                    Inspection.objects.filter(file__pk=vid_id, file__target_org=target_org)
                    .select_related("file")
                    .select_related("file__job_tree")
                    .select_related("file__job_tree__standard_key")
                    .select_related("file__upload_org")
                    .select_related("folder")
                    .select_related("folder__standard_key")
                    .select_related("created_by")
                    .select_related("asset")
                    .prefetch_related(
                        "inspectionvalue_set",
                        "asset__assetvalue_set",
                        "inspectionvalue_set__standard_header__header",
                        "asset__assetvalue_set__standard_header__header",
                    )
                    .first()
                )

                if not inspection:
                    continue

                frame_objs = (
                    VideoFrames.objects.filter(parent_video__pk=vid_id)
                    .filter(is_hidden=False)
                    .exclude(defect_scores__is_shown=False)
                    .prefetch_related("parent_video", "defect_scores")
                )

                if progress == "Not Found":
                    requests.get(
                        vid_obj.request_endpoint,
                        params={"video_id": vid_id, "progress": "FAILED"},
                    )
                elif progress == "Awaiting manual review":
                    requests.get(
                        vid_obj.request_endpoint,
                        params={
                            "video_id": vid_id,
                            "progress": "PARTIAL",
                            "results": {
                                "Asset Data": MapPointLinkJSON(inspection, target_org),
                                "Frame Data": [VideoFrameJSON(frame) for frame in frame_objs],
                            },
                        },
                    )
                elif progress == "Complete":
                    requests.get(
                        vid_obj.request_endpoint,
                        params={
                            "video_id": vid_id,
                            "progress": "COMPLETE",
                            "results": {
                                "Asset Data": MapPointLinkJSON(inspection, target_org),
                                "Frame Data": [VideoFrameJSON(frame) for frame in frame_objs],
                            },
                        },
                    )
                else:
                    requests.get(
                        vid_obj.request_endpoint,
                        params={"video_id": vid_id, "progress": "INCOMPLETE"},
                    )

        return Response(progress_dict, status=status.HTTP_200_OK)


class ExternalSubmitXmlJobView(BaseInspection, RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [MultiPartParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionSerializer
    queryset = (
        Inspection.objects.select_related("file")
        .select_related("file__job_tree")
        .select_related("file__job_tree__standard_key")
        .select_related("file__upload_org")
        .select_related("file__target_org")
        .select_related("folder")
        .select_related("folder__standard_key")
        .select_related("created_by")
        .select_related("asset")
        .prefetch_related(
            "inspectionvalue_set",
            "asset__assetvalue_set",
            "inspectionvalue_set__standard_header__header",
            "asset__assetvalue_set__standard_header__header",
        )
        .all()
    )

    @extend_schema(
        request=OpenApiRequest(schemas.xml_create_job_request_schema),
    )
    def get_asset_value_by_key(self, asset_id, key):
        return AssetValue.objects.filter(asset_id=asset_id, standard_header__header__mapped_mpl_field=key).first()

    def get_inspection_value_by_key(self, inspection_id, key):
        return InspectionValue.objects.filter(
            inspection_id=inspection_id, standard_header__header__mapped_mpl_field=key
        ).first()

    def get_standard_header_by_key(self, standard_id, key):
        return StandardHeader.objects.get(header__mapped_mpl_field=key, standard_id=standard_id)

    def get_object(self):
        file_id = self.request.data.get("file_id")
        target_org = self.request.organisation

        filter_kwargs = {
            "file_id": file_id,
            "file__target_org": target_org,
        }

        obj = get_object_or_404(self.queryset, **filter_kwargs)

        return obj

    @atomic
    def post(self, request, *args, **kwargs):
        data = request.data
        file_id = data.get("file_id", None)
        file = request.FILES["file"]

        if not file_id:
            raise ValidationError("Must provide valid file")

        if not file or file.content_type != "application/xml":
            raise ValidationError("Must provide a xml file")

        inspection = self.get_object()

        if not inspection:
            print("No inspection retrieved!")
            raise FileNotFoundError()

        # get inspection representation
        initial_inspection = get_inspection_representation(inspection=inspection, skip_cache=True)
        # get values for asset and inspection
        inspection_values = inspection.inspectionvalue_set.all()
        asset_values = inspection.asset.assetvalue_set.all()
        # parse the XML file to asset and inspection dictionaries
        _, asset_obj, inspection_obj = map_xml_to_inspection(file)
        asset_point, inspection_point = map_inspection_values(
            inspection_values, asset_values, inspection_obj, asset_obj
        )
        # handle direction for NZ standards
        handle_nz_direction(inspection_obj, initial_inspection)

        # iterate each inspection point and update the related inspection value
        for key, value in inspection_point.items():
            inspection_value = self.get_inspection_value_by_key(inspection.uuid, key)

            if not inspection_value:
                if inspection.asset is not None:
                    try:
                        standard_header = self.get_standard_header_by_key(inspection.asset.standard, key)
                        inspection_value = inspection.create_inspection_value(
                            standard_header=standard_header, value=value
                        )
                    except StandardHeader.DoesNotExist:
                        raise ValidationError(f"{key} is an invalid standard header")
                else:
                    raise ValidationError("Cannot update Inspections without Assets")

            serializer = InspectionValueSerializer(
                instance=inspection_value,
                data={"value": value},
                partial=True,
                context={"standard_header": inspection_value.standard_header},
            )
            serializer.is_valid(raise_exception=True)
            instance = InspectionValue(**serializer.validated_data)
            validation_errors = validate_standard_value(instance)

            if len(validation_errors) > 0:
                raise ValidationError(validation_errors)

            serializer.save()

        # update gradings if chainage changed
        if "chainage" in inspection_point and int(initial_inspection.chainage) != int(inspection_point["chainage"]):
            update_gradings(inspection)

        # sanitise the the asset point
        for key, value in asset_obj.items():
            if key == "start_node" or key == "end_node":
                continue
            asset_point[key] = value

        for key, value in asset_point.items():
            if key == "standard":
                continue
            asset_value = self.get_asset_value_by_key(inspection.asset.uuid, key)
            if not asset_value:
                standard_header = self.get_standard_header_by_key(inspection.asset.standard, key)
                if not standard_header:
                    raise ValidationError(f"Unable to create asset value for {key}")

                data = {
                    "asset": inspection.asset.uuid,
                    "standard_header": standard_header.uuid,
                    "value": value,
                }
                asset_value = AssetValueSerializer(data=data)

                if asset_value.is_valid(raise_exception=True):
                    asset_value.save()
                continue

            instance = AssetValueSerializer(
                instance=asset_value,
                data={"value": value, "standard_header": asset_value.standard_header.uuid},
                partial=True,
            )
            if instance.is_valid(raise_exception=True):
                instance.save()

        # get updated inspection
        inspection = self.get_object()
        details = get_inspection_representation(inspection=inspection, skip_cache=True)

        return Response(details.model_dump(), status=status.HTTP_202_ACCEPTED)


class ExternalFrameView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer

    @extend_schema(
        parameters=[
            OpenApiParameter("t", OpenApiTypes.STR, required=True, description="Time reference"),
        ]
    )
    def get(self, request, id):
        t = request.query_params.get("t", None)
        inspection = get_object_or_404(MapPointList, pk=id)

        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{id}:{container}"
        sas_token = sas_cache.get_or_set(
            cache_key,
            get_platform_storage_sas_token(container_name=container),
            settings.DEFAULT_SAS_CACHE_TIMEOUT,
        )

        def time_reference_to_seconds(time_reference):
            hours, minutes, seconds = map(float, time_reference.split(":"))
            return hours * 3600 + minutes * 60 + seconds

        frame = (
            VideoFrames.objects.filter(parent_video=inspection.associated_file, time_reference=t)
            .with_defect_score_severity()
            .first()
        )

        if not frame:
            t_seconds = time_reference_to_seconds(t)
            lower_bound = max(0, t_seconds - 5)
            upper_bound = t_seconds + 5

            frames = (
                VideoFrames.objects.filter(parent_video=inspection.associated_file).with_defect_score_severity().all()
            )

            frames_list = []
            for frame in frames:
                frame.time_in_seconds = time_reference_to_seconds(frame.time_reference)
                frames_list.append(frame)

            frames_list = [frame for frame in frames_list if lower_bound <= frame.time_in_seconds <= upper_bound]

            serializer = self.serializer_class(frames_list, context={"sas_url": sas_token}, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        serializer = self.serializer_class(frame, context={"sas_url": sas_token})
        return Response(serializer.data, status=status.HTTP_200_OK)


class ExternalInspectionListView(InspectionList2):
    pagination_class = None

    @extend_schema(
        parameters=[
            OpenApiParameter("ordering", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("search", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page_size", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter(
                "status",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
            ),
            OpenApiParameter("date_captured", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("asset_id", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("folder_id", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
        ],
        request=None,
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def get(self, request, *args, **kwargs):
        if not request.GET._mutable:
            request.GET._mutable = True
        request.GET["use_inspection_filters"] = False  # Always false from external API
        request.GET["use_header_names"] = True  # Always true for external API
        request.GET["page_size"] = 100
        request.GET._mutable = False
        response = super().get(request, *args, **kwargs)  # Only return the inspection data
        return Response(response.data["results"])


class ExternalExportCreateView(exports_views.ExportListCreateView):
    http_method_names = ["post"]  # Create only

    def get_permissions(self):
        return super().get_permissions() + [IsAssetOwnerOrg()]


class ExternalExportRetrieveView(exports_views.ExportRetrieveUpdateView):
    http_method_names = ["get"]  # Retrieve only

    def get_permissions(self):
        return super().get_permissions() + [IsAssetOwnerOrg()]


class ExternalExportOutputListView(exports_views.ExportOutputListCreateView):
    http_method_names = ["get"]  # List outputs only

    def get_permissions(self):
        return super().get_permissions() + [IsAssetOwnerOrg()]
