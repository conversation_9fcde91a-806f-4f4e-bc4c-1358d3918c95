import csv
import tempfile
import time
from datetime import datetime, timedelta
import subprocess
from itertools import islice
from pathlib import Path
from typing import Iterable, TypeVar
from urllib.parse import unquote

from azure.core.exceptions import HttpResponseError
from azure.storage.blob import BlobServiceClient, generate_account_sas, ContainerClient
from django.conf import settings
from django.core.management import BaseCommand, CommandError
from yarl import URL

from api.exports.models import ExportOutput
from api.files.models import FileList, VideoFrames
from api.organisations.models import Organisations


AZCOPY_BATCH_SIZE = 20_000
AZ_SDK_BATCH_SIZE = 256

T = TypeVar("T")


def _batched(iterable: Iterable[T], n: int) -> Iterable[list[T]]:
    """
    Yield n-sized chunks from sequence
    """
    it = iter(iterable)
    while chunk := list(islice(it, n)):
        yield chunk


def make_container_url(storage_account: BlobServiceClient, sas_token: str, container_name: str):
    return f"{storage_account.url}{container_name}?{sas_token}"


def strip_container(file_path: str):
    return file_path.split("/", 1)[-1]


def azcopy_delete_files_by_name(
    url: str,
    region: str,
    container: str,
    file_paths: Iterable[str],
    csv_writer,
    dry_run: bool = False,
):
    """
    Write the file paths to a temporary file and use azcopy to bulk delete them from the container.
    Also writes out the deleted files to a csv file.
    """

    stripped = (strip_container(file_path) for file_path in file_paths)
    unquoted = [unquote(file_path) for file_path in stripped]

    with tempfile.NamedTemporaryFile(mode="w+") as f:
        f.writelines(file_path + "\n" for file_path in unquoted)
        f.flush()
        cmd = ["azcopy", "rm", url, "--list-of-files", str(f.name), "--log-level", "NONE"]
        if dry_run:
            cmd.append("--dry-run")
        proc = subprocess.run(cmd)  # Block but don't capture stdout or raise on error
        return_code = proc.returncode
        print(f"Return code: {return_code}")

    csv_writer.writerows((region, container, file_path) for file_path in unquoted)


def delete_files_by_name(container_client: ContainerClient, region: str, file_paths: Iterable[str], csv_writer):
    """
    Delete files by name using the Azure SDK.
    """

    stripped = (strip_container(file_path) for file_path in file_paths)
    unquoted = [unquote(file_path) for file_path in stripped]

    to_delete = list(unquoted)

    while to_delete:
        try:
            responses = list(container_client.delete_blobs(*to_delete))
        except HttpResponseError as e:
            responses = e.parts

        to_delete = []
        deleted = []
        is_throttled = False
        for resp in responses:
            blob_name = URL(resp.request.url).name
            if resp.status_code == 429:
                is_throttled = True
            if resp.status_code >= 400 and resp.status_code != 404:
                to_delete.append(blob_name)  # Try again
            else:
                deleted.append(blob_name)  # Success

        if deleted:
            print(f"Deleted {len(deleted)} files")
            csv_writer.writerows((region, container_client.container_name, file_path) for file_path in deleted)
        if is_throttled:
            print("Throttled, waiting...")
            time.sleep(1)


def confirmation_prompt(file_kind, storage_account_name, org_region, org_region_not_in):
    if org_region:
        prompt_msg = (
            f"This will delete files of type '{file_kind}' from the storage account '{storage_account_name}', "
            f"that belong to {org_region} organisations. Continue? (y/n) "
        )
    else:
        prompt_msg = (
            f"This will delete files of type '{file_kind}' from the storage account '{storage_account_name}', "
            f"that do NOT belong to {org_region_not_in} organisations. Continue? (y/n) "
        )
    prompt = input(prompt_msg)
    should_continue = prompt.strip().lower() in ["y", "yes"]
    if not should_continue:
        print("Exiting")
        exit()


class Command(BaseCommand):
    help = """
        Delete all blobs that are marked as belonging to a specific region. Requires the azcopy tool. 
        For example, `python3 manage.py delete_blobs_in_region --container_region AU --org_region_not_in AU` will delete
        all files from Australian storage that belong to non Australian organisations.
        """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.kind = None
        self.container_region = None
        self.org_region = None
        self.org_region_not_in = None
        self.csv_log_file = None
        self.is_dry_run = False
        self.storage_account = None
        self.sas_token = None

    def add_arguments(self, parser):
        parser.add_argument(
            "--kind",
            type=str,
            help="The kind of files to delete",
            required=True,
            choices=["logos", "risk-docs", "export-outputs", "videos", "video-plays", "frames", "all"],
        )
        parser.add_argument(
            "--container_region",
            type=str,
            help="The region of the blob containers we will be deleting files from",
            required=True,
        )
        parser.add_argument("--org_region", type=str, help="The labelled region of the files to delete")
        parser.add_argument("--org_region_not_in", type=str, help="The labelled region of the files to keep")
        parser.add_argument(
            "--log_filename",
            type=Path,
            help="The csv file to write the deleted files' names to",
            default=Path("deleted_files.csv"),
        )
        parser.add_argument("--dry_run", default=False, action="store_true", help="Dry run mode")

    def handle(self, *args, **options):
        self.kind = options["kind"]
        self.container_region = options["container_region"]
        self.org_region = options.get("org_region")
        self.org_region_not_in = options.get("org_region_not_in")
        self.csv_log_file = options["log_filename"]
        self.is_dry_run = options["dry_run"]

        if self.org_region and self.org_region_not_in or (not self.org_region and not self.org_region_not_in):
            raise CommandError("You must provide either org_region or org_region_not_in, but not both")

        if not self.csv_log_file.is_absolute():
            self.csv_log_file = settings.BASE_DIR / self.csv_log_file

        self.storage_account = BlobServiceClient.from_connection_string(
            settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS[self.container_region]
        )

        confirmation_prompt(self.kind, self.storage_account.primary_hostname, self.org_region, self.org_region_not_in)

        self.sas_token = generate_account_sas(
            account_name=self.storage_account.account_name,
            account_key=self.storage_account.credential.account_key,
            resource_types="sco",
            start=datetime.now(),
            expiry=datetime.now() + timedelta(days=14),
            permission="rwld",
            protocol="https",
        )

        with self.csv_log_file.open("a") as f:
            csv_writer = csv.writer(f)

            # Specify the organisations we are deleting files for
            if self.org_region:
                orgs_qs = Organisations.objects.filter(country=self.org_region)
            else:
                orgs_qs = Organisations.objects.exclude(country=self.org_region_not_in)

            self.run_deletion_process(orgs_qs, csv_writer)

    def azcopy_bulk_delete_from_container(
        self,
        container_name: str,
        file_paths: Iterable[str],
        csv_writer,
    ):
        container_url = make_container_url(self.storage_account, self.sas_token, container_name)
        azcopy_delete_files_by_name(
            container_url, self.container_region, container_name, file_paths, csv_writer, self.is_dry_run
        )

    def azcopy_batched_bulk_delete_from_container(
        self,
        container_name: str,
        file_paths: Iterable[str],
        csv_writer,
    ):
        container_url = make_container_url(self.storage_account, self.sas_token, container_name)
        for chunk in _batched(file_paths, AZCOPY_BATCH_SIZE):
            print(f"Deleting batch of {len(chunk)} files...")
            azcopy_delete_files_by_name(
                container_url, self.container_region, container_name, chunk, csv_writer, self.is_dry_run
            )

    def sdk_bulk_delete_from_container(
        self,
        container_name: str,
        file_paths: Iterable[str],
        csv_writer,
    ):
        container_client = self.storage_account.get_container_client(container_name)
        for chunk in _batched(file_paths, AZ_SDK_BATCH_SIZE):
            print(f"Deleting batch of {len(chunk)} files...")
            delete_files_by_name(container_client, self.container_region, chunk, csv_writer)

    def run_deletion_process(self, orgs_qs, csv_writer):
        if self.kind in ("logos", "all"):
            print("Deleting org logos...")
            logos_qs = (
                orgs_qs.filter(logo_path__isnull=False)
                .exclude(logo_path="")
                .order_by("id")
                .values_list("logo_path", flat=True)
            )
            self.sdk_bulk_delete_from_container(
                container_name="orglogos",
                file_paths=logos_qs,
                csv_writer=csv_writer,
            )

        if self.kind in ("risk-docs", "all"):
            print("Deleting risk docs...")
            risk_doc_qs = (
                orgs_qs.filter(risk_doc_url__isnull=False)
                .exclude(risk_doc_url="")
                .order_by("id")
                .values_list("risk_doc_url", flat=True)
            )
            self.sdk_bulk_delete_from_container(
                container_name="orgfiles",
                file_paths=risk_doc_qs,
                csv_writer=csv_writer,
            )

        if self.kind in ("export-outputs", "all"):
            print("Deleting export outputs...")
            export_files_qs = (
                ExportOutput.objects.filter(blob_url__isnull=False)
                .exclude(blob_url="")
                .filter(export__target_org__in=orgs_qs)
                .order_by("id")
                .values_list("blob_url", flat=True)
            )
            self.sdk_bulk_delete_from_container(
                container_name="export-outputs",
                file_paths=export_files_qs,
                csv_writer=csv_writer,
            )

        # The following files are deleted in batches because of their larger volume

        if self.kind in ("videos", "all"):
            print("Deleting video files...")
            video_files_qs = (
                FileList.objects.filter(url__isnull=False)
                .exclude(url="")
                .filter(target_org__in=orgs_qs)
                .order_by("id")
                .values_list("url", flat=True)
                .iterator()  # Prevent caching this queryset
            )
            self.sdk_bulk_delete_from_container(
                container_name="uploadedvideofiles",
                file_paths=video_files_qs,
                csv_writer=csv_writer,
            )

        if self.kind in ("video-plays", "all"):
            print("Deleting video play files...")
            video_play_files_qs = (
                FileList.objects.filter(url__isnull=False)
                .exclude(url="")
                .filter(target_org__in=orgs_qs)
                .order_by("id")
                .values_list("url", flat=True)
                .iterator()  # Prevent caching this queryset
            )
            self.sdk_bulk_delete_from_container(
                container_name="uploadedvideofiles",
                file_paths=video_play_files_qs,
                csv_writer=csv_writer,
            )

        # Delete frame files - This is the problematic one
        if self.kind in ("frames", "all"):
            print("Deleting frame files...")
            frame_files_qs = (
                VideoFrames.objects.filter(image_location__isnull=False)
                .exclude(image_location="")
                .exclude(image_location__startswith="https://")
                .filter(parent_video__target_org__in=orgs_qs)
                .order_by("id")
                .values_list("image_location", flat=True)
                .iterator()  # Prevent caching this queryset
            )
            self.sdk_bulk_delete_from_container(
                container_name="videoframefiles",
                file_paths=frame_files_qs,
                csv_writer=csv_writer,
            )
