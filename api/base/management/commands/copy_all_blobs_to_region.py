import subprocess
from datetime import datetime, timedelta

from azure.storage.blob import generate_account_sas
from azure.storage.blob.aio import BlobServiceClient
from django.conf import settings
from django.core.management import BaseCommand

from api.files.models import FileList

CONTAINERS_TO_COPY = [
    "orglogos",
    "orgfiles",
    "export-outputs",
    "uploadedvideofiles",
    "videoframefiles",
]


class Command(BaseCommand):
    help = "Copy all platform blob files from one regions' storage account to another. Requires the azcopy tool."

    def add_arguments(self, parser):
        parser.add_argument("src_region", type=str, help="The country code of the source region. eg. AU")
        parser.add_argument("dest_region", type=str, help="The country code of the destination region. eg. GB")
        parser.add_argument("--dry_run", default=False, action="store_true")
        parser.add_argument(
            "--update_video_country",
            default=False,
            action="store_true",
            help="Update the country of all video files belonging to orgs in the destination region.",
        )

    def copy_files(self, src_region: str, dest_region: str, is_dry_run: bool):
        if is_dry_run:
            print("Dry run mode enabled. No files will be copied.")
        src_account = BlobServiceClient.from_connection_string(
            settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS[src_region]
        )
        dest_account = BlobServiceClient.from_connection_string(
            settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS[dest_region]
        )

        src_account_sas = generate_account_sas(
            account_name=src_account.account_name,
            account_key=src_account.credential.account_key,
            resource_types="sco",
            start=datetime.now(),
            expiry=datetime.now() + timedelta(days=7),
            permission="rwl",
            protocol="https",
        )
        dest_account_sas = generate_account_sas(
            account_name=dest_account.account_name,
            account_key=dest_account.credential.account_key,
            resource_types="sco",
            start=datetime.now(),
            expiry=datetime.now() + timedelta(days=7),
            permission="rwl",
            protocol="https",
        )

        for container_name in CONTAINERS_TO_COPY:
            print(f"Copying {container_name}")
            cmd = [
                "azcopy",
                "cp",
                f"{src_account.url}{container_name}?{src_account_sas}",
                f"{dest_account.url}{container_name}?{dest_account_sas}",
                "--recursive",
                "--from-to=BlobBlob",
                "--overwrite=ifSourceNewer",
            ]
            if is_dry_run:
                cmd.append("--dry-run")

            proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            for line in proc.stdout:
                print(line, end="")
            return_code = proc.wait()
            print(f"Return code: {return_code}")

    def handle(self, *args, **options):
        is_dry_run = options["dry_run"]
        src_region = options["src_region"]
        dest_region = options["dest_region"]
        should_update_video_country = options["update_video_country"]

        self.copy_files(src_region, dest_region, is_dry_run)

        if should_update_video_country:
            FileList.objects.filter(target_org__country=dest_region).exclude(storage_region=dest_region).update(
                storage_region=dest_region
            )
