from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.management import BaseCommand, CommandError
from django.db import connection
from tqdm import tqdm

from api.actions.models import AuditList
from api.exports.models import ExportOutput, Export
from api.files.models import (
    ResultsFile,
    ResultsFileAssets,
    ResultsFileDefects,
    Jobs,
    ProcessingList,
)
from api.inspections.models import ImportedInspectionFile
from api.organisations.models import Organisations


def confirmation_prompt(orgs_count: int, org_region: str | None, org_region_not_in: str | None):
    db_name = f"{settings.DATABASES['default']['HOST']}/{settings.DATABASES['default']['NAME']}"
    if org_region:
        prompt_msg = (
            f"This will delete {orgs_count} orgs from DB '{db_name}' that belong to country {org_region}. "
            "Continue? (y/n) "
        )
    else:
        prompt_msg = (
            f"This will delete {orgs_count} orgs from DB '{db_name}' that DO NOT belong to country "
            f"{org_region_not_in}. Continue? (y/n) "
        )
    prompt = input(prompt_msg)
    should_continue = prompt.strip().lower() in ["y", "yes"]
    if not should_continue:
        print("Exiting")
        exit()


class Command(BaseCommand):
    help = "Delete all organisation data (users, assets, etc) belonging to a particular country."

    def add_arguments(self, parser):
        parser.add_argument("--org_region", type=str, help="Country code of the organisations to delete")
        parser.add_argument("--org_region_not_in", type=str, help="Country code of organisations to keep")

    def handle(self, *args, **options):
        org_region = options.get("org_region")
        org_region_not_in = options.get("org_region_not_in")

        if (org_region and org_region_not_in) or not (org_region or org_region_not_in):
            raise CommandError("Exactly one of org_region or org_region_not_in must be provided")

        if org_region:
            orgs_qs = Organisations.objects.filter(country=org_region)
        else:
            orgs_qs = Organisations.objects.exclude(country=org_region_not_in)

        # Do not delete orgs that have any staff or service users
        orgs_qs = orgs_qs.exclude(customuser__is_staff=True).exclude(customuser__is_service_user=True)

        orgs_count = orgs_qs.count()

        confirmation_prompt(orgs_count, org_region, org_region_not_in)

        with tqdm(orgs_qs, total=orgs_count) as progress_bar:
            for org in progress_bar:
                progress_bar.set_description(f"Org: {org.short_name}")
                self.delete_org_data(org, progress_bar)

    def delete_org_data(self, org: Organisations, progress_bar: tqdm):
        progress_bar.write(f"Deleting data for organisation {org.short_name}")

        progress_bar.write("Deleting contractor and asset owner records...")
        try:
            org.contractors.delete()
        except ObjectDoesNotExist:
            pass  # Not a contractor
        try:
            org.assetowners.delete()
        except ObjectDoesNotExist:
            pass  # Not an asset

        # Drop down to raw queries in order to prevent signals and cascading fetch+delete queries
        with connection.cursor() as cursor:
            # Delete repair recommendations
            progress_bar.write("Deleting repair recommendation records...")
            cursor.execute(
                """
                DELETE FROM service_custom_repair_values
                USING service_custom_repair_types
                WHERE service_custom_repair_values."custom_Repair_Type_id" = service_custom_repair_types.id
                AND service_custom_repair_types.organisations_id = %s
                """,
                [org.id],
            )
            cursor.execute(
                """
                DELETE FROM service_custom_repair_types
                WHERE service_custom_repair_types.organisations_id = %s
                """,
                [org.id],
            )
            cursor.execute(
                """
                DELETE FROM service_repairrecommendation
                USING service_mappointlist, service_filelist
                WHERE service_repairrecommendation.target_id = service_mappointlist.id
                AND service_mappointlist.associated_file_id = service_filelist.id
                AND service_filelist.target_org_id = %s
                """,
                [org.id],
            )

            # Delete MPL records
            progress_bar.write("Deleting map point list records...")
            # Delete by matching file
            cursor.execute(
                """
                DELETE FROM service_mappointlist
                USING service_filelist
                WHERE service_mappointlist.associated_file_id = service_filelist.id
                AND service_filelist.target_org_id = %s
                """,
                [org.id],
            )
            # Delete by matching inspection - some records have an inspection but no file
            cursor.execute(
                """
                DELETE FROM service_mappointlist
                USING inspections_inspection, inspections_asset
                WHERE service_mappointlist.inspection_id = inspections_inspection.uuid
                AND inspections_inspection.asset_id = inspections_asset.uuid
                AND inspections_asset.organisation_id = %s
                """,
                [org.id],
            )

            # Delete inspections and values
            progress_bar.write("Deleting inspection values...")
            cursor.execute(
                """
                DELETE FROM inspections_inspectionvalue
                USING inspections_inspection, inspections_asset
                WHERE inspections_inspectionvalue.inspection_id = inspections_inspection.uuid
                AND inspections_inspection.asset_id = inspections_asset.uuid
                AND inspections_asset.organisation_id = %s
                """,
                [org.id],
            )

            progress_bar.write("Deleting inspections...")
            cursor.execute(
                """
                DELETE FROM inspections_inspection
                USING inspections_asset
                WHERE inspections_inspection.asset_id = inspections_asset.uuid
                AND inspections_asset.organisation_id = %s
                """,
                [org.id],
            )

            # Delete assets and values
            progress_bar.write("Deleting asset values...")
            cursor.execute(
                """
                DELETE FROM inspections_assetvalue
                USING inspections_asset
                WHERE inspections_assetvalue.asset_id = inspections_asset.uuid
                AND inspections_asset.organisation_id = %s
                """,
                [org.id],
            )

            progress_bar.write("Deleting assets...")
            cursor.execute(
                """
                DELETE FROM inspections_asset
                WHERE inspections_asset.organisation_id = %s
                """,
                [org.id],
            )

        ResultsFileDefects.objects.filter(results_file_asset__results_file__organisation=org).delete()
        ResultsFileAssets.objects.filter(results_file__organisation=org).delete()
        ResultsFile.objects.filter(organisation=org).delete()

        ImportedInspectionFile.objects.filter(organisation=org).delete()

        # Delete Processing files
        progress_bar.write("Deleting processing files...")
        org.processinglist_set.all().delete()  # Delete by matching org
        ProcessingList.objects.filter(associated_file__target_org=org).delete()  # Delete by matching file

        # Delete video files and related objects
        with connection.cursor() as cursor:
            progress_bar.write("Deleting video frames...")
            cursor.execute(
                """
                DELETE FROM service_videoframes
                USING service_filelist
                WHERE service_videoframes.parent_video_id = service_filelist.id
                AND service_filelist.target_org_id = %s
                """,
                [org.id],
            )

            progress_bar.write("Deleting video files...")
            cursor.execute(
                """
                DELETE FROM service_filelist
                WHERE service_filelist.target_org_id = %s
                """,
                [org.id],
            )

        # Delete folder tree
        progress_bar.write("Deleting folder tree...")
        Jobs.objects.filter(Organisation=org).delete()
        for root_folder in org.root_folders.all():  # Delete from root folders on down
            root_folder.delete()
        for folder in org.secondary_org.all():  # Delete any contractor folders
            folder.delete()

        # Detach from contractor folders

        # Delete exports
        progress_bar.write("Deleting exports records...")
        ExportOutput.objects.filter(export__target_org=org).delete()
        Export.objects.filter(target_org=org).delete()

        progress_bar.write("Deleting users...")
        AuditList.objects.filter(user__in=org.customuser_set.all()).update(user=None)
        with connection.cursor() as cursor:
            # Note: These otp tables do not exist in the django migrations / schema, but they reference users so
            # they still need to be cleared
            cursor.execute(
                """
                DELETE FROM otp_totp_totpdevice
                USING service_customuser
                WHERE otp_totp_totpdevice.user_id = service_customuser.id
                AND service_customuser.organisation_id = %s
                """,
                [org.id],
            )
            cursor.execute(
                """
                DELETE FROM otp_static_statictoken
                USING otp_static_staticdevice, service_customuser
                WHERE otp_static_statictoken.device_id = otp_static_staticdevice.id
                AND otp_static_staticdevice.user_id = service_customuser.id
                AND service_customuser.organisation_id = %s
                """,
                [org.id],
            )
            cursor.execute(
                """
                DELETE FROM otp_static_staticdevice
                USING service_customuser
                WHERE otp_static_staticdevice.user_id = service_customuser.id
                AND service_customuser.organisation_id = %s
                """,
                [org.id],
            )
        org.customuser_set.all().delete()

        org.delete()
