from django.core.exceptions import ObjectDoesNotExist
from django.db import models
from django_countries.fields import CountryField

from api.common.enums import OrganisationTypeEnum
from api.defects.models import Standard


class Organisations(models.Model):
    TYPE_CHOICES = [
        ("Asset_Owner", "Asset_Owner"),
        ("Contractor", "Contractor"),
    ]

    default_repair_param = {
        "Minimum roots class": 15,
        "Debris build up class": 15,
        "Debris build up length m": 1,
        "Debris single instance class": 20,
        "Patch if score over length >=": 50,
        "Patch length for scoring m": 1,
        "Maximum number of patches over distance": 2,
        "Maximum distance for patch": 30,
        "Maximum number of patches in total": 3,
    }

    full_name = models.CharField(max_length=200, blank=True)
    short_name = models.CharField(max_length=8, blank=True)
    email_domain = models.CharField(max_length=50, blank=True)
    sewer_data = models.BooleanField(default=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.CASCADE)
    subscription_type = models.IntegerField(default=3)
    manual_qa_required = models.BooleanField(default=False)
    org_type = models.CharField(max_length=20, blank=True, null=True, choices=TYPE_CHOICES, default="Asset_Owner")
    org_can_upload = models.BooleanField(blank=True, null=True, default=True)
    country = CountryField(blank=True, null=True, default="AU")
    logo_path = models.CharField(max_length=1000, null=True, blank=True)
    require_tfa = models.BooleanField(blank=False, null=False, default=False)
    repair_param = models.TextField(blank=True, null=True, default=default_repair_param)
    risk_doc_url = models.CharField(max_length=1000, null=True, blank=True)
    # Interval between frames sample from videos, in milliseconds.
    frame_sample_rate = models.IntegerField(null=False, default=1500)

    @property
    def is_asset_owner(self) -> bool:
        return self.org_type == OrganisationTypeEnum.ASSET_OWNER

    @property
    def is_contractor(self) -> bool:
        return self.org_type == OrganisationTypeEnum.CONTRACTOR

    def is_contractor_for_org(self, target_org: "Organisations") -> bool:
        """
        Check if this organisation is a contractor for the given target organisation.
        """
        if not self.is_contractor or not target_org.is_asset_owner:
            return False
        return AssetOwners.objects.filter(org=target_org, contractor__org=self).exists()

    def change_type(self, new_org_type: OrganisationTypeEnum):
        if new_org_type == self.org_type:
            return

        self.org_type = new_org_type
        if new_org_type == OrganisationTypeEnum.ASSET_OWNER:
            try:
                contractor_obj = self.contractors
                contractor_obj.delete()
            except ObjectDoesNotExist:
                pass
            AssetOwners.objects.get_or_create(org=self)
        else:  # Changing to contractor
            try:
                asset_owner_obj = self.assetowners
                asset_owner_obj.delete()
            except ObjectDoesNotExist:
                pass
            Contractors.objects.get_or_create(org=self)

        self.save()

    class Meta:
        db_table = "service_organisations"


class Contractors(models.Model):
    org = models.OneToOneField(Organisations, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        db_table = "service_contractors"


class AssetOwners(models.Model):
    contractor = models.ManyToManyField(Contractors, blank=True)
    org = models.OneToOneField(Organisations, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return self.org.full_name

    class Meta:
        db_table = "service_assetowners"
