from django.db import transaction
from django.http import Http404
from django.utils import timezone
from django.db.models import Q
from django.db.transaction import atomic
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import CamelCaseJSONParser, MultiPartParser
from django.core.exceptions import ValidationError as DjangoValidationError
from djangorestframework_camel_case.render import CamelCaseJ<PERSON>NRenderer
from drf_spectacular.utils import extend_schema, OpenApiRequest
from rest_framework import generics
from rest_framework import status
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.exceptions import (
    NotFound,
    ParseError,
    ValidationError,
)
from rest_framework.generics import RetrieveUpdateDestroyAPIView
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED
from rest_framework.views import APIView

from api.common.storage import put_to_platform_blob_storage
from api.organisations.models import Organisations, AssetOwners, Contractors
from api.organisations.serializers import (
    OrganisationSerializer,
    EditOrganisationSerializer,
)
from api.common.pagination import StandardResultsSetPagination
from api.files.models import JobsTree
from api.inspections.models import MapPointList
from api.common.permissions import HasOrganisationAccess, IsProductOwner, IsAuthenticated
from api.users.models import CustomUser
import time


class OrganisationDetail(RetrieveUpdateDestroyAPIView):
    """
    Retrieve users organisation and linked organisations

    Update organisation details

    Delete organisation
    """

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = EditOrganisationSerializer
    queryset = Organisations.objects.all()
    lookup_field = "id"
    http_method_names = ["get", "patch", "delete"]

    def get_permissions(self):
        if self.request.method == "GET":
            return [IsAuthenticated(), HasOrganisationAccess()]
        else:
            return [IsAuthenticated(), HasOrganisationAccess(), IsProductOwner()]

    @transaction.atomic
    def patch(self, request, id: int) -> Response:
        organisation = self.get_object()
        serializer = EditOrganisationSerializer(organisation, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        updated_org = serializer.save()

        response = OrganisationSerializer(updated_org).data
        return Response(response)

    @transaction.atomic
    def delete(self, request: Request, id: int) -> Response:
        """
        Delete organisation

        :param request: request object
        :param id: organisation id to delete
        """

        organisation = self.get_object()

        # if any inspections exist for this organisation, don't delete
        query_filter = (
            Q(associated_file__target_org=organisation)
            | Q(associated_file__upload_org=organisation)
            | Q(inspection__asset__organisation=organisation)
        )

        inspections_exist = MapPointList.objects.filter(query_filter).exists()
        if inspections_exist:
            raise ValidationError("Cannot delete organisation. Inspections exist for this organisation")

        users_exist = CustomUser.objects.filter(organisation=organisation).exists()
        if users_exist:
            raise ValidationError("Cannot delete organisation. Users exist for this organisation")

        # delete contractor/asset owner record
        if organisation.is_asset_owner:
            AssetOwners.objects.filter(org=organisation).delete()
        else:
            Contractors.objects.filter(org=organisation).delete()

        organisation.delete()
        return Response("organisation has been deleted", status=status.HTTP_204_NO_CONTENT)


class OrganisationList(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = OrganisationSerializer
    filter_backends = [SearchFilter, DjangoFilterBackend, OrderingFilter]
    pagination_class = StandardResultsSetPagination
    ordering_fields = ["full_name", "org_type", "country", "standard_key__name", "id", "short_name"]
    search_fields = ["full_name", "org_type", "country", "standard_key__name", "id", "short_name"]
    filterset_fields = ["id", "org_type", "country", "standard_key__name", "short_name"]
    queryset = Organisations.objects.all().order_by("-id")

    def create_initial_folders(self, organisation):
        now = timezone.now()
        job_name = organisation.full_name
        job = JobsTree.add_root(
            job_name=job_name,
            primary_org=organisation,
            created_date=now,
            pipe_type_sewer=organisation.sewer_data,
            standard_key=organisation.standard_key,
        )

        node = JobsTree.objects.get(pk=job.pk)
        node.add_child(
            job_name="Unallocated",
            created_date=now,
            pipe_type_sewer=organisation.sewer_data,
            standard_key=organisation.standard_key,
        )

    def created_asset_owner_contractor(self, organisation):
        if organisation.is_asset_owner:
            AssetOwners.objects.create(org=organisation)
        else:
            Contractors.objects.create(org=organisation)

    def get(self, request, *args, **kwargs):
        pagination = request.query_params.get("pagination", True)

        if str(pagination).lower() == "false":
            self.pagination_class = None

        return super().list(request, *args, **kwargs)

    @atomic
    def post(self, request):
        data = request.data

        if not data.get("full_name"):
            raise ParseError("Organisation full name is required")

        if not data.get("org_type"):
            raise ParseError("Organisation type is required")

        if not data.get("country"):
            raise ParseError("Country is required")

        if not data.get("password"):
            raise ParseError("Password for vapar account is required")

        if not data.get("short_name"):
            raise ParseError("Short name is required for vapar account")

        serializer = self.serializer_class(data=data)

        if serializer.is_valid():
            instance = serializer.save()
            short_name = data.get("short_name", None)

            if short_name:
                password = data.get("password")
                user_email = short_name.lower() + "@vapar.co"
                try:
                    CustomUser.objects.create_vapar_user(
                        email=user_email,
                        first_name="VAPAR",
                        last_name="Admin",
                        full_name="VAPAR Admin",
                        is_active=True,
                        is_staff=False,
                        organisation=instance,
                        is_member=True,
                        password=password,
                        group=2,
                    )
                except DjangoValidationError as error:
                    raise ParseError(error.message_dict)

            self.created_asset_owner_contractor(instance)
            self.create_initial_folders(instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LinkContractorAssetOwner(APIView):
    """
    Link asset owner to contractor
    """

    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def create_linked_folders(self, contractor, asset_owner):
        root = JobsTree.objects.filter(primary_org=asset_owner).first()

        if not root:
            raise NotFound("Cannot create linked folders. Root folder not found for linked asset owner")

        contractor_folder_name = contractor.full_name

        if root.get_children().filter(job_name=contractor_folder_name).exists():
            contractor_folder_name = contractor_folder_name + "_"

        sub_root = root.add_child(
            job_name=contractor_folder_name,
            created_date=timezone.now(),
            pipe_type_sewer=asset_owner.sewer_data,
            standard_key=root.standard_key,
            secondary_org=contractor,
        )

        sub_root.add_child(
            job_name="Job Folder",
            created_date=timezone.now(),
            pipe_type_sewer=asset_owner.sewer_data,
            standard_key=root.standard_key,
        )

    @extend_schema(request=None, responses={status.HTTP_201_CREATED: None})
    @atomic
    def post(self, request, contractor_id, asset_owner_id):
        contractor_org = Organisations.objects.filter(id=contractor_id).first()
        asset_owner_org = Organisations.objects.filter(id=asset_owner_id).first()

        contractor = Contractors.objects.filter(org=contractor_id).first()
        asset_owner = AssetOwners.objects.filter(org=asset_owner_id).first()

        if not contractor_org or not contractor:
            raise NotFound("Contractor not found")

        if not asset_owner or not asset_owner_org:
            raise NotFound("Asset owner not found")

        # check if contractor is already linked to asset owner
        if asset_owner.contractor.filter(id=contractor.id).exists():
            raise ValidationError("Contractor is already linked to asset owner")

        asset_owner.contractor.add(contractor)

        # create folders for linked asset owners
        self.create_linked_folders(contractor=contractor_org, asset_owner=asset_owner_org)

        return Response(status=status.HTTP_200_OK)


class OrganisationLogo(APIView):
    """
    Upload organisation logo
    """

    permission_classes = [IsAuthenticated, HasOrganisationAccess, IsProductOwner]
    parser_classes = [MultiPartParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, id):
        try:
            return Organisations.objects.get(id=id)
        except Organisations.DoesNotExist:
            raise Http404

    @extend_schema(
        request=OpenApiRequest({"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}),
        responses={HTTP_201_CREATED: None},
    )
    @atomic
    def post(self, request, id):
        organisation = self.get_object(id)
        file = request.FILES.get("file")

        if not organisation:
            raise NotFound("Organisation not found")

        if not file:
            raise ParseError("File is required")

        blob_name = f"{organisation.id}_{organisation.full_name}_{time.time()}.png"
        container_name = settings.BLOB_STORAGE_LOGOS_CONTAINER

        with file.open() as file_stream:
            put_to_platform_blob_storage(
                file=file_stream, blob_name=blob_name, container_name=container_name, overwrite=True
            )

        file_path_in_azure = f"{container_name}/{blob_name}"

        organisation.logo_path = file_path_in_azure
        organisation.save()

        return Response(status=status.HTTP_200_OK)
