from django.db.transaction import atomic
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status
from rest_framework.generics import CreateAPIView, RetrieveAPIView, GenericAPIView
from rest_framework.response import Response

from api.actions.models import AuditList
from api.common.permissions import IsAuthenticated, IsServiceUser, IsOrgScopedRequest
from api.inspections.models import Footage
from api.inspections.permissions import IsOrgOwnerOfFootage
from api.inspections.serializers.footage_serializers import (
    FootageSerializer,
    FootageDetailSerializer,
    KeyframeSerializer,
)


class FootageCreateView(CreateAPIView):
    serializer_class = FootageSerializer
    permission_classes = [IsAuthenticated, IsServiceUser, IsOrgScopedRequest]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]

    def post(self, request, *args, **kwargs):
        """
        Create a new footage record with no keyframes
        """

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        footage = serializer.save()
        AuditList.objects.create(
            event_type="Create",
            table="Footage",
            column="Multi",
            description=f"Created footage {footage.id}",
            user=request.user,
        )
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class FootageKeyframesCreateDestroyView(GenericAPIView):
    serializer_class = KeyframeSerializer
    permission_classes = [IsAuthenticated, IsServiceUser, IsOrgScopedRequest]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    queryset = Footage.objects.all().select_related("video_file").prefetch_related("video_file__videoframes_set")

    lookup_url_kwarg = "footage_id"

    pagination_class = None

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["footage"] = self.get_object()
        return ctx

    @extend_schema(
        request=KeyframeSerializer(many=True),
        responses={status.HTTP_201_CREATED: KeyframeSerializer(many=True)},
    )
    @atomic
    def put(self, request, *args, **kwargs):
        """
        Create a sequence of keyframes for given footage. Replaces any existing keyframes.
        """

        footage = self.get_object()
        footage.clear_keyframes()

        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save(footage_id=footage.id)
        AuditList.objects.create(
            event_type="Create",
            table="Keyframe",
            column="Multi",
            description=f"Replaced keyframes for footage {footage.id} with {len(serializer.data)} new keyframes",
            user=request.user,
        )
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(
        responses={status.HTTP_204_NO_CONTENT: None},
    )
    @atomic
    def delete(self, request, *args, **kwargs):
        """
        Delete all keyframes for given footage.
        """
        footage = self.get_object()
        footage.clear_keyframes()

        AuditList.objects.create(
            event_type="Delete",
            table="Keyframe",
            column="Multi",
            description=f"Deleted all keyframes from footage {footage.id}",
            user=request.user,
        )
        return Response(status=status.HTTP_204_NO_CONTENT)


class FootageDetailView(RetrieveAPIView):
    queryset = Footage.objects.prefetch_related("keyframes")

    lookup_url_kwarg = "footage_id"

    serializer_class = FootageDetailSerializer
    permission_classes = [IsAuthenticated, IsOrgOwnerOfFootage]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().for_org(org)
        if self.get_with_video_frame_details_param():
            qs = qs.prefetch_keyframe_video_frames()
        return qs

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["with_video_frame_details"] = self.get_with_video_frame_details_param()
        return ctx

    def get_with_video_frame_details_param(self) -> bool:
        return self.request.query_params.get("with_video_frame_details", "false").lower() == "true"

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="with_video_frame_details",
                default=False,
                type=bool,
                description="Include detailed video frame information under 'videoFrameDetails' field of each keyframe",
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get footage details, including keyframes
        """
        return super().get(request, *args, **kwargs)
