import logging
from functools import reduce
from typing import Set

from django.http import Http404
from django.utils import timezone
from djangorestframework_camel_case.parser import Camel<PERSON>ase<PERSON><PERSON><PERSON>arser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status
from rest_framework.exceptions import ParseError, NotFound, PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView
from treebeard.mp_tree import MP_NodeManager, MP_Node

from api.actions.models import AuditList
from api.common.permissions import IsA<PERSON>enticated, IsStandardUser, HasOrganisationAccess
from api.defects.models import Standard
from api.files.models import JobsTree
from api.inspections.serializers.inspection_serializers import FolderSerializer
from api.organisations.models import Organisations

log = logging.getLogger(__name__)


class FolderList(APIView):
    """
    Retrieve the folder tree for or add folder to the selected organisation.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJ<PERSON>NRenderer]
    serializer_class = FolderSerializer

    def _construct_folder_tree(self, filtered_queryset: MP_NodeManager):
        def get_anscestors_descendants():
            nodeset: Set[MP_Node] = set()
            queries: Set[str] = set()
            for node in filtered_queryset:
                node: MP_Node = node
                nodeset.add(node)
                if node.depth > 1:
                    ancs: MP_NodeManager = node.get_ancestors()  # type: ignore
                    if str(ancs.query) not in queries:
                        nodeset.update(ancs)
                    queries.add(str(ancs.query))
                if node.depth < 4:
                    desc: MP_NodeManager = node.get_descendants()  # type: ignore
                    if desc:
                        if str(desc.query) not in queries:
                            nodeset.update(desc)
                        queries.add(str(desc.query))
            return nodeset

        nodeset = get_anscestors_descendants()
        if len(nodeset) == 0:
            return []

        nodes = sorted(list(nodeset), key=lambda x: x.path)
        # obtain a list of path parts by dividing each node path into subsections of 4
        # as each row in jobs tree has a path like 000A00010001.
        # these become [[000A, 0001, 0001], ...]
        paths = [list(map("".join, zip(*[iter(node.path)] * 4))) for node in nodes]
        # use the above path list to create a branch structure.
        # NOTE: reduce applies the provided function with the last returned result
        # (or initial value - 3rd param) and the next item in the sequence.
        branches = [reduce(lambda x, y: {y: x}, path[::-1], {}) for path in paths]  # type: ignore

        def merge(first, second, path=None):
            # slightly hacky, but merges a list of dicts into a nested dict
            if path is None:
                path = []
            for key in second:
                if key in first:
                    if isinstance(first[key], dict) and isinstance(second[key], dict):
                        merge(first[key], second[key], path + [str(key)])
                    elif first[key] == second[key]:
                        pass  # same leaf value
                    else:
                        raise KeyError("Conflict at %s" % ".".join(path + [str(key)]))
                else:
                    first[key] = second[key]
            return first

        # fold the branch list down into a hierarchial data sctructure that represents
        # the folder tree. essentially merges a list of dicts.
        folder_structure = reduce(merge, branches)

        def build_tree(structure, prefix=""):
            # takes a nested dict structure and extracts data from nodeset to
            # recursively rebuilds a tree from a list of nodes
            sub_tree = []
            for path in structure:
                full_path = prefix + path
                node = next(filter(lambda x: x.path == full_path, nodeset), None)
                if node is None:
                    return sub_tree
                # remove the node to speed up subsequent lookups
                nodeset.remove(node)
                folder = {
                    "data": node.__dict__,
                    "children": build_tree(structure[path], full_path),
                    "id": node.id,
                }
                if len(folder["children"]) == 0:
                    folder.pop("children")
                sub_tree.append(folder)
            return sub_tree

        return build_tree(folder_structure)

    @extend_schema(
        parameters=[
            OpenApiParameter("organisationId", OpenApiTypes.INT, required=True, description="Organisation ID"),
            OpenApiParameter(
                "includeRoot", OpenApiTypes.BOOL, required=True, description="Include root folder in response"
            ),
        ],
        responses={status.HTTP_200_OK: dict},
    )
    def get(self, request):
        """
        Get the folder tree for an organisation.
        """
        # Validate query params
        if "organisationId" not in request.query_params:
            raise ParseError("organisationId required in query params")

        if "includeRoot" not in request.query_params:
            raise ParseError("includeRoot required in query params")

        # Get selected organisation
        try:
            organisation = Organisations.objects.get(id=request.query_params["organisationId"])
        except Organisations.DoesNotExist:
            raise NotFound("Organisation not found")

        # Get associated JobsTree
        try:
            root: MP_Node = (
                JobsTree.objects.select_related("primary_org")
                .select_related("secondary_org")
                .select_related("standard_key")
                .get(primary_org=organisation)
            )
        except JobsTree.DoesNotExist:
            raise NotFound("Root folder associated with organisation not found")

        country_code = organisation.country.code
        whole_tree = []

        if organisation == request.organisation:
            if job_name := request.query_params.get("searchQuery"):
                search_results: MP_NodeManager = root.get_tree(parent=root).filter(job_name__icontains=job_name)  # type: ignore
                whole_tree = self._construct_folder_tree(search_results)
            else:
                # get this orgs whole tree
                whole_tree = root.dump_bulk(parent=root)
        else:
            # need to check if the primary org root matching the requested org has any layers where secondary org = user org
            sub_roots: MP_NodeManager = root.get_children().filter(secondary_org=request.organisation)  # type: ignore
            for sub_root in sub_roots:
                if job_name := request.query_params.get("searchQuery"):
                    search_results = sub_root.get_tree(parent=sub_root).filter(job_name__icontains=job_name)
                    whole_tree.extend(self._construct_folder_tree(search_results))
                else:
                    whole_tree.extend(sub_root.dump_bulk(sub_root))

        if not whole_tree:
            return Response({"folders": []})

        sorted_children = (
            sorted(whole_tree[0]["children"], key=lambda d: d["data"]["job_name"])
            if whole_tree[0].get("children", False)
            else []
        )
        data = {
            "whole_tree": whole_tree,
            "sorted_folder": sorted_children,
        }
        data["country"] = country_code

        # [ ================ ]
        root_folder = whole_tree[0]
        include_root = request.query_params["includeRoot"].lower() == "true"
        standards = Standard.objects.all().values()
        standards_keys = {s["id"]: s["display_name"] for s in standards}

        def format_folder(folder, path, formatted_folders):
            # Append to path array
            current_path = path.copy()

            # Only include root if requested
            is_root = folder["id"] == root_folder["id"]
            if not is_root or (is_root and include_root):
                # Append to path array
                current_path.append(str(folder["id"]))
                # Format and append current folder
                pipe_type = "sewer" if folder["data"]["pipe_type_sewer"] else "stormwater"
                standard_key = folder["data"].get("standard_key", None)
                standard = standard_key if standard_key else folder["data"].get("standard_key", None)
                standard_name = standards_keys[standard] if standard else None

                formatted_folders.append(
                    {
                        "path": current_path,
                        "name": folder["data"]["job_name"],
                        "created_date": folder["data"]["created_date"],
                        "id": folder["id"],
                        "standard_key": standard,
                        "standard_name": standard_name,
                        "pipe_type": pipe_type,
                    }
                )

            # Recursive case
            if "children" in folder:
                # Iteratively format and append each child folder
                for child in folder["children"]:
                    format_folder(child, current_path, formatted_folders)

        # Execute formating
        formatted_folders = []
        format_folder(root_folder, [], formatted_folders)

        sorted_dict = sorted(formatted_folders, key=lambda d: d["name"].lower())

        response = {"folders": sorted_dict}

        return Response(response)

    @extend_schema(
        responses={status.HTTP_200_OK: str},  # TODO: Add response schema
    )
    def post(self, request):
        """
        Create a folder in an organisation.
        """
        # Create serializer and validate payload data
        data = request.data
        data["created_date"] = timezone.now()
        serializer = self.serializer_class(data=data, context={"request": request})
        if serializer.is_valid(raise_exception=True):
            new_folder = serializer.save()
        else:
            return Response(serializer.error_messages, status=status.HTTP_400_BAD_REQUEST)

        # [ === V2 logic === ]
        description = "Create folder -> " + new_folder.job_name
        new_folder_dump_bulk = JobsTree.dump_bulk(new_folder)
        now = timezone.now()
        AuditList.objects.create(
            event_type="Create",
            table="JobTree",
            row_id=new_folder_dump_bulk[0]["id"],
            column="Multi",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        return Response(new_folder_dump_bulk)


class FolderDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FolderSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, id):
        try:
            return JobsTree.objects.get(id=id)
        except JobsTree.DoesNotExist:
            raise Http404

    @extend_schema(
        operation_id="inspections_folder_retrieve_by_id",
    )
    def get(self, request, id):
        instance = self.get_object(id)
        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=instance.get_root().primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")
        serializer = self.serializer_class(instance, context={"request": request})
        return Response(serializer.data)

    @extend_schema(responses={status.HTTP_200_OK: list[dict]})
    def patch(self, request, id):
        """
        Update a JobTree record (folder).
        """
        instance = self.get_object(id)
        serializer = self.serializer_class(instance, data=request.data, context={"request": request}, partial=True)
        serializer.is_valid(raise_exception=True)
        updated_folder = serializer.save()

        description = f"Update job: {updated_folder.job_name}"

        AuditList.objects.create(
            event_type="Update",
            table="JobTree",
            row_id=updated_folder.id,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        updated_folder = JobsTree.dump_bulk(updated_folder)
        return Response(updated_folder)

    def delete(self, request, id):
        """
        Delete a JobTree record (folder).
        """
        instance = self.get_object(id)
        serializer = self.serializer_class(instance, data=request.data, context={"request": request}, partial=True)
        serializer.is_valid(raise_exception=True)

        instance.get_descendants().delete()
        instance.delete()

        description = "Delete a job " + instance.job_name
        now = timezone.now()
        AuditList.objects.create(
            event_type="Delete",
            table="JobTree",
            row_id=id,
            column="Multi",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        return Response({"detail": "Successfully deleted"})


class FolderFilter(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FolderSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("org", OpenApiTypes.INT, required=True, description="Organisation ID"),
        ],
        responses={status.HTTP_200_OK: FolderSerializer(many=True)},
    )
    def get(self, request):
        """
        Retrieve a filtered folder tree for an organisation.
        """
        # Validate query params
        if "org" not in request.query_params:
            raise ParseError("'org' query param required")

        # Get selected organisation
        try:
            organisation = Organisations.objects.get(id=request.query_params["org"])
        except Organisations.DoesNotExist:
            raise NotFound("Organisation not found")

        # Get associated JobsTree
        try:
            root = JobsTree.objects.get(primary_org=organisation)
        except JobsTree.DoesNotExist:
            raise NotFound("Root folder associated with organisation not found")

        if organisation == request.organisation:
            whole_tree = root.get_tree(parent=root)
            if job_query := request.query_params.get("query"):
                whole_tree = whole_tree.filter(job_name__icontains=job_query)
        else:
            # need to check if the primary org root matching the requested org has any layers where secondary org = user org
            sub_roots = root.get_children().filter(secondary_org=request.organisation)
            if job_query := request.query_params.get("query"):
                sub_roots = sub_roots.filter(job_name__icontains=job_query)
                sub_trees = [
                    sub_root.get_tree(sub_root).filter(job_name__icontains=job_query) for sub_root in sub_roots
                ]
            else:
                sub_trees = [sub_root.get_tree(sub_root) for sub_root in sub_roots]
            # flatten list of lists
            whole_tree = [node for sub_tree in sub_trees for node in sub_tree]

        response = [self.serializer_class(node).data for node in whole_tree]

        return Response(response)
