from django.db.transaction import atomic
from django.utils import timezone
from djangorestframework_camel_case.parser import Camel<PERSON><PERSON><PERSON><PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON>aseJ<PERSON><PERSON>ender<PERSON>
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status, serializers
from rest_framework.filters import OrderingFilter
from rest_framework.generics import GenericAPIView, RetrieveUpdateDestroyAPIView, CreateAPIView, ListAPIView
from rest_framework.response import Response

from api.actions.models import AuditList
from api.common.pagination import StandardResultsSetPagination
from api.common.permissions import (
    IsAuthenticated,
    HasInspectionAccess,
    HasObservationAccess,
    HasObservedFeatureAccess,
    HasObservedSubFeatureAccess,
    IsStandardUser,
)
from api.inspections.models import Inspection, Observation, ObservedFeature, ObservedSubFeature
from api.inspections.serializers.observation_serializers import (
    ObservationSerializer,
    ObservedFeatureSerializer,
    ObservedSubFeatureSerializer,
)


def _get_update_audit_col_name(validated_data: dict) -> str:
    """
    Determine column name to use for audit log entries on update endpoints.
    """
    if len(validated_data) > 1:
        return "Multi"
    return next(iter(validated_data.keys()))


_OPENAPI_OBSERVATION_OPTIONAL_DETAILS_PARAMS = [
    OpenApiParameter(
        "with_standardised_details",
        type=bool,
        default=False,
        description=(
            "Include per-inspection standard specific information under 'standardisedVals' field of observed features"
        ),
    ),
    OpenApiParameter(
        "with_aggregated_flags",
        type=bool,
        default=False,
        description=(
            "Include an aggregated representation of the flags on the observation's keyframes, "
            "under 'aggregatedFlags' field"
        ),
    ),
    OpenApiParameter(
        "with_keyframe_details",
        type=bool,
        default=False,
        description="Include detailed keyframe information under 'keyframeDetails' field",
    ),
    OpenApiParameter(
        "with_video_frame_details",
        type=bool,
        default=False,
        description=(
            "Include detailed video frame information under 'videoFrameDetails' field of each keyframe. "
            "Only applicable if 'with_keyframe_details' is also true."
        ),
    ),
    OpenApiParameter(
        "with_code_description",
        type=bool,
        default=False,
        description=(
            "Include the full description string for the codes applied to observations, under the 'codeDescription' "
            "field of each observed feature"
        ),
    ),
]


class ObservationOptionalDetailsMixin:
    """
    APIView mixin for common query params used to enrich observation response.
    """

    # ^ Views using this mixin should have a docstring for each HTTP method, otherwise this docstring will be used by
    # drf-spectacular

    def _get_bool_param(self, param_name: str, default: bool = False) -> bool:
        param_value = self.request.query_params.get(param_name, str(default)).lower()
        return param_value == "true"

    def get_with_standardised_details(self) -> bool:
        return self._get_bool_param("with_standardised_details")

    def get_with_aggregated_flags_param(self) -> bool:
        return self._get_bool_param("with_aggregated_flags")

    def get_with_keyframe_details_param(self) -> bool:
        return self._get_bool_param("with_keyframe_details")

    def get_with_video_frame_details_param(self) -> bool:
        return self._get_bool_param("with_video_frame_details")

    def get_with_code_description_param(self) -> bool:
        return self._get_bool_param("with_code_description")

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["with_standardised_details"] = self.get_with_standardised_details()
        ctx["with_aggregated_flags"] = self.get_with_aggregated_flags_param()
        ctx["with_keyframe_details"] = self.get_with_keyframe_details_param()
        ctx["with_video_frame_details"] = self.get_with_video_frame_details_param()
        ctx["with_code_description"] = self.get_with_code_description_param()

        return ctx


class ObservationListCreateView(ObservationOptionalDetailsMixin, GenericAPIView):
    queryset = Inspection.objects.all()
    serializer_class = ObservationSerializer
    permission_classes = [IsAuthenticated, HasInspectionAccess]

    lookup_url_kwarg = "inspection_uuid"

    pagination_class = None

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    filter_backends = [OrderingFilter]
    ordering = ["created_at"]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().visible_to_org(org)
        return qs

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["inspection"] = self.get_object()
        return ctx

    @extend_schema(
        request=ObservationSerializer(many=True),
        responses={status.HTTP_201_CREATED: ObservationSerializer(many=True)},
    )
    @atomic
    def put(self, request, *args, **kwargs):
        """
        Create a sequence of observations on an inspection, replacing any existing observations.
        """

        insp: Inspection = self.get_object()
        insp.observations.all().delete()

        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        observations = serializer.save()

        AuditList.objects.create(
            event_type="Create",
            table="Observation",
            column="Multi",
            description=f"Replaced observations for inspection {insp.uuid} with {len(observations)} new observations.",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(self.get_serializer(instance=observations, many=True).data, status.HTTP_201_CREATED)

    @extend_schema(
        responses={status.HTTP_200_OK: ObservationSerializer(many=True)},
        parameters=_OPENAPI_OBSERVATION_OPTIONAL_DETAILS_PARAMS,
    )
    def get(self, request, *args, **kwargs):
        """
        List all observations made during an inspection
        """
        insp: Inspection = self.get_object()
        observations_qs = insp.observations.with_start_end_chainages().prefetch_details()
        if self.get_with_standardised_details() or self.get_with_code_description_param():
            observations_qs = observations_qs.prefetch_standardised().prefetch_inspection_values()
        if self.get_with_aggregated_flags_param():
            observations_qs = observations_qs.with_flags()
        if self.get_with_keyframe_details_param() and self.get_with_video_frame_details_param():
            observations_qs = observations_qs.prefetch_keyframe_video_frames()
        serializer = self.get_serializer(instance=observations_qs, many=True)
        return Response(serializer.data, status.HTTP_200_OK)


class ObservationRetrieveUpdateDestroyView(ObservationOptionalDetailsMixin, RetrieveUpdateDestroyAPIView):
    queryset = Observation.objects.prefetch_details().with_start_end_chainages()
    serializer_class = ObservationSerializer
    permission_classes = [IsAuthenticated, HasObservationAccess]

    lookup_url_kwarg = "observation_id"
    http_method_names = ["get", "patch", "delete"]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        qs = super().get_queryset().visible_to_org(self.request.organisation)
        if self.get_with_standardised_details() or self.get_with_code_description_param():
            qs = qs.prefetch_standardised().prefetch_inspection_values()  # Used for deriving standard id to use
        if self.get_with_aggregated_flags_param():
            qs = qs.with_flags()
        if self.get_with_keyframe_details_param() and self.get_with_video_frame_details_param():
            qs = qs.prefetch_keyframe_video_frames()
        return qs

    @extend_schema(
        parameters=_OPENAPI_OBSERVATION_OPTIONAL_DETAILS_PARAMS,
    )
    def get(self, request, *args, **kwargs):
        """Retrieve a specific observation and its sub-objects by ID"""
        return super().get(request, *args, **kwargs)

    @atomic
    def patch(self, request, *args, **kwargs):
        """
        Update the top-level fields of an observation.
        """
        obs = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=obs, partial=True)
        serializer.is_valid(raise_exception=True)
        obs = serializer.save()

        AuditList.objects.create(
            event_type="Update",
            table="Observation",
            column=_get_update_audit_col_name(serializer.validated_data),
            description=f"Updated observation {obs.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(data=serializer.data)

    @atomic
    def delete(self, request, *args, **kwargs):
        """Delete an observation and all its sub-objects, by observation ID"""
        obs = self.get_object()
        obs_id = obs.id
        obs.delete()

        AuditList.objects.create(
            event_type="Delete",
            table="Observation",
            column="Multi",
            description=f"Deleted observation {obs_id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(status=status.HTTP_204_NO_CONTENT)


class ObservationGlobalListView(ObservationOptionalDetailsMixin, ListAPIView):
    queryset = Observation.objects.prefetch_details().with_start_end_chainages()
    serializer_class = ObservationSerializer
    permission_classes = [IsAuthenticated, IsStandardUser]

    pagination_class = StandardResultsSetPagination

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    filter_backends = [OrderingFilter]
    ordering = ["inspection_id", "created_at"]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().visible_to_org(org)
        if self.get_with_standardised_details() or self.get_with_code_description_param():
            qs = qs.prefetch_standardised().prefetch_inspection_values()  # Used for deriving standard id to use
        if self.get_with_aggregated_flags_param():
            qs = qs.with_flags()
        if self.get_with_keyframe_details_param() and self.get_with_video_frame_details_param():
            qs = qs.prefetch_keyframe_video_frames()

        if inspection_uuids := self.request.query_params.get("inspection__in"):
            inspection_uuids = inspection_uuids.split(",")
            ser = serializers.ListSerializer(child=serializers.UUIDField(), data=inspection_uuids)
            ser.is_valid(raise_exception=True)
            qs = qs.filter(inspection_id__in=ser.validated_data)

        if ids := self.request.query_params.get("id__in"):
            ids = ids.split(",")
            ser = serializers.ListSerializer(child=serializers.UUIDField(), data=ids)
            ser.is_valid(raise_exception=True)
            qs = qs.filter(id__in=ser.validated_data)

        return qs

    @extend_schema(
        # To stop collision with the ObservationListCreateView when using auto operation IDs
        operation_id="inspections_observations_global_list",
        responses={status.HTTP_200_OK: ObservationSerializer(many=True)},
        parameters=[
            *_OPENAPI_OBSERVATION_OPTIONAL_DETAILS_PARAMS,
            OpenApiParameter(
                "inspection__in",
                type=str,
                required=False,
                description="Filter observations by list of inspection UUIDs, comma-separated",
            ),
            OpenApiParameter(
                "id__in",
                type=str,
                required=False,
                description="Filter observations by list of UUIDs, comma-separated",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        List all observations visible to this organisation across all inspections.
        """
        return super().get(request, *args, **kwargs)


class ObservedFeatureCreateView(CreateAPIView):
    queryset = Observation.objects.all().prefetch_details()
    serializer_class = ObservedFeatureSerializer
    permission_classes = [IsAuthenticated, HasObservationAccess]

    lookup_url_kwarg = "observation_id"

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().visible_to_org(org)
        qs = qs.filter(pk=self.kwargs["observation_id"])
        return qs

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["observation"] = self.get_object()
        return ctx

    @extend_schema(
        request=ObservedFeatureSerializer(),
        responses={status.HTTP_201_CREATED: ObservedFeatureSerializer()},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create an observed feature for an existing observation.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        obs_feat = serializer.save()
        AuditList.objects.create(
            event_type="Create",
            table="ObservedFeature",
            column="Multi",
            description=f"Created observed feature {obs_feat.id} for observation {obs_feat.observation.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(serializer.data, status.HTTP_201_CREATED)


class ObservedFeatureUpdateDestroyView(RetrieveUpdateDestroyAPIView):
    queryset = ObservedFeature.objects.all().prefetch_parents()
    serializer_class = ObservedFeatureSerializer
    permission_classes = [IsAuthenticated, HasObservedFeatureAccess]

    lookup_url_kwarg = "observed_feature_id"
    http_method_names = ["patch", "delete"]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().filter(observation__in=Observation.objects.visible_to_org(org))
        qs = qs.filter(pk=self.kwargs["observed_feature_id"])
        return qs

    @atomic
    def patch(self, request, *args, **kwargs):
        obs_feat = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=obs_feat, partial=True)
        serializer.is_valid(raise_exception=True)
        obs_feat = serializer.save()

        AuditList.objects.create(
            event_type="Update",
            table="ObservedFeature",
            column=_get_update_audit_col_name(serializer.validated_data),
            description=f"Updated observed feature {obs_feat.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(data=serializer.data)

    @atomic
    def delete(self, request, *args, **kwargs):
        obs_feat = self.get_object()
        obs_feat_id = obs_feat.id
        obs_id = obs_feat.observation.id
        obs_feat.delete()

        AuditList.objects.create(
            event_type="Delete",
            table="ObservedFeature",
            column="Multi",
            description=f"Deleted observed feature {obs_feat_id} for observation {obs_id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(status=status.HTTP_204_NO_CONTENT)


class ObservedSubFeatureCreateView(CreateAPIView):
    queryset = ObservedFeature.objects.all().prefetch_parents()
    serializer_class = ObservedSubFeatureSerializer
    permission_classes = [IsAuthenticated, HasObservedFeatureAccess]

    lookup_url_kwarg = "observed_feature_id"

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().filter(observation__in=Observation.objects.visible_to_org(org))
        qs = qs.filter(pk=self.kwargs["observed_feature_id"])
        return qs

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["observed_feature"] = self.get_object()
        return ctx

    @extend_schema(
        request=ObservedSubFeatureSerializer(),
        responses={status.HTTP_201_CREATED: ObservedSubFeatureSerializer()},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create an observed sub-feature for an existing observed feature on an observation.
        """
        feature = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        obs_sub_feat = serializer.save()

        AuditList.objects.create(
            event_type="Create",
            table="ObservedSubFeature",
            column="Multi",
            description=f"Created observed sub-feature {obs_sub_feat.id} for observed feature {feature.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(serializer.data, status.HTTP_201_CREATED)


class ObservedSubFeatureUpdateDestroyView(RetrieveUpdateDestroyAPIView):
    queryset = ObservedSubFeature.objects.all().prefetch_parents()
    serializer_class = ObservedSubFeatureSerializer
    permission_classes = [IsAuthenticated, HasObservedSubFeatureAccess]

    lookup_url_kwarg = "observed_sub_feature_id"
    http_method_names = ["patch", "delete"]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        qs = super().get_queryset().filter(observed_feature__observation__in=Observation.objects.visible_to_org(org))
        qs = qs.filter(pk=self.kwargs["observed_sub_feature_id"])
        return qs

    @atomic
    def patch(self, request, *args, **kwargs):
        obs_sub_feat = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=obs_sub_feat, partial=True)
        serializer.is_valid(raise_exception=True)
        obs_sub_feat = serializer.save()

        AuditList.objects.create(
            event_type="Update",
            table="ObservedSubFeature",
            column=_get_update_audit_col_name(serializer.validated_data),
            description=f"Updated observed sub-feature {obs_sub_feat.id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(data=serializer.data)

    @atomic
    def delete(self, request, *args, **kwargs):
        obs_sub_feat = self.get_object()
        obs_sub_feat_id = obs_sub_feat.id
        obs_feat_id = obs_sub_feat.observed_feature.id
        obs_sub_feat.delete()

        AuditList.objects.create(
            event_type="Delete",
            table="ObservedSubFeature",
            column="Multi",
            description=f"Deleted observed sub-feature {obs_sub_feat_id} for observed feature {obs_feat_id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )
        return Response(status=status.HTTP_204_NO_CONTENT)
