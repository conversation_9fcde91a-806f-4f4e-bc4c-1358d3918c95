# pylint: disable=C0302
import logging
import re
from datetime import datetime
from io import StringIO
from typing import Any

import jsonschema
import pandas as pd
import pydantic
import requests
from django.core.cache import caches
from django.db.models import Count, Q, QuerySet, Subquery
from django.db.transaction import atomic
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiRequest
from pydantic import BaseModel, field_validator
from rest_framework import serializers, status
from rest_framework.exceptions import (
    NotFound,
    ParseError,
    PermissionDenied,
    ValidationError,
)
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import (
    <PERSON><PERSON>AP<PERSON><PERSON>ie<PERSON>,
    ListAPIView,
    RetrieveAPIView,
    RetrieveUpdateAPIView,
    UpdateAPIView,
    ListCreateAPIView,
    RetrieveUpdateDestroyAPIView,
)
from rest_framework.parsers import MultiPartParser, JSONParser
from rest_framework.renderers import JSONRenderer
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from vapar.constants.conversion import StandardValueConverter
from vapar.constants.pipes import StandardEnum

from api.actions.models import AuditList
from api.base.models import Header
from api.common.enums import (
    StatusEnum,
    OrganisationTypeEnum,
    OperatorEnum,
    QueryFilterDataTypeEnum as FilterDataType,
)
from api.common.errors import CustomError
from api.common.json_util import MapPointLinkJSON, VideoFrameJSON
from api.common.pagination import StandardResultsSetPagination, StatusCountPagination
from api.common.permissions import (
    HasInspectionAccess,
    HasMapPointListAccess,
    HasOrganisationAccess,
    IsStandardUser,
    HasAccessToOrgScopedObject,
    IsAuthenticated,
    IsServiceUser,
)
from api.common.storage import (
    get_platform_blob_client,
    put_to_platform_blob_storage,
)
from api.common.string_handling import camel_to_snake
from api.defects.models import StandardHeader
from api.files.models import (
    FileList,
    JobsTree,
    ProcessingList,
    VideoFrames,
)
from api.inspections import analytics, schemas
from api.inspections.gradings import update_gradings
from api.inspections.models import (
    Asset,
    ImportedInspectionFile,
    Inspection,
    InspectionFilter,
    InspectionValue,
    MapPointList,
)
from api.inspections.pydantic_models.inspection_filter import (
    InspectionFilterEntityEnum as FilterEntity,
    InspectionSearchFilterModel,
    build_asset_value_order_subquery,
    build_inspection_value_order_subquery,
)
from api.inspections.pydantic_models.inspection_model import (
    get_inspection_representation,
    restore_cached_inspections,
    InspectionModel,
    InspectionCreateModel,
    InspectionCreateResponseModel,
)
from api.inspections.serializers.asset_serializers import (
    AssetValueListSerializer,
)
from api.inspections.serializers.inspection_serializers import (
    ImportedInspectionFileSerializer,
    InspectionBulkUpdateSerializer,
    InspectionDetailSerializer,
    InspectionFilterSerializer,
    InspectionListSerializer,
    InspectionValueSerializer,
    InspectionValueListSerializer,
    InspectionSerializer,
)
from api.inspections.utilities import import_inspections
from api.inspections.utilities.data_fetching import (
    get_inspection_list,
    paginate_queryset,
    build_paginated_response_payload,
)
from api.inspections.utilities.sync_mappointlist_values import sync_to_mappointlist
from api.inspections.utilities.validate_inspections import (
    get_inspections_for_validation,
    validate_csv,
    validate_standard_value,
    validate_inspections_for_export,
)
from api.inspections.views.asset_views import clear_from_asset_cache
from api.organisations.models import AssetOwners, Organisations
from api.recommendations.models import RepairRecommendation
from api.recommendations.tasks.tasks import (
    generate_repair_recommendations,
    _process_inspections,
    run_many_repair_recommendations,
    update_or_create_recommendation,
)
from api.users.models import CustomUser

DEFAULT_PAGE_SIZE = 100

sas_cache = caches["default"]
inspection_cache = caches["inspections"]

log = logging.getLogger(__name__)


def get_status_counts(queryset: QuerySet[Inspection]) -> dict[str, int]:
    """
    Given a queryset of inspections, count the number of inspections for each status. Do not include statuses that were
    not present in the queryset.
    """
    status_counts_result = (
        queryset.filter(status__in=StatusEnum.get_status_list())
        .values("status")
        .order_by()
        .annotate(count=Count("status"))
    )
    status_counts = {r["status"]: r["count"] for r in status_counts_result}

    return status_counts


def handle_nz_direction(data, inspection: InspectionModel, use_header_names: bool):
    # Direction not a valid header for NZ. Swap value to SetupLocation.
    direction_key = "Direction" if use_header_names else "direction"
    setup_location_key = "SetupLocation" if use_header_names else "opposite of direction"

    if direction_key in data:
        updated_inspection_details = inspection.model_dump()
        updated_inspection_details.update(data)
        updated_inspection = InspectionModel.model_validate(
            updated_inspection_details,
            context={"standard": inspection.standard},
        )
        converter = StandardValueConverter(header="Direction", standard=updated_inspection.standard)
        value = converter.get_standard_value(updated_inspection.setup_location)
        data[setup_location_key] = value.value
        del data[direction_key]


class BaseInspection(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_imported_files(self, organisation: int, filter_record: InspectionFilter) -> dict:
        if filter_record:
            folder_ids = [folder["id"] for folder in filter_record.folder_filter_model]
            files = ImportedInspectionFile.objects.filter(folder__in=folder_ids)
        else:
            files = ImportedInspectionFile.objects.filter(organisation=organisation)

        # Annotate and count files by folder
        files = (
            files.values("folder")  # Group by folder ID
            .annotate(count=Count("folder"))  # Count files in each group
            .order_by("folder")
        )

        # Convert queryset to dictionary
        return {r["folder"]: r["count"] for r in files}

    def get_view_disabled(self, user: CustomUser, inspection: dict) -> dict:
        """
        Determine if the inspection is viewable by the user via inspection list.

        :param user: the requesting user
        :param inspection: the inspection to be filtered
        """

        if inspection["status"] == StatusEnum.PLANNED or not inspection["file"]:
            return {"disabled": True, "message": "This inspection has not been uploaded yet"}

        if user.is_service_user:
            return {"disabled": False, "message": ""}

        file_upload_org_type = inspection["file"]["upload_org_type"]
        user_org = user.organisation

        if (
            user_org.org_type == OrganisationTypeEnum.ASSET_OWNER
            and file_upload_org_type == OrganisationTypeEnum.CONTRACTOR
            and inspection["status"] == StatusEnum.UPLOADED
        ):
            return {
                "disabled": True,
                "message": "A contractor uploaded inspection with the status of Uploaded cannot \
                be viewed by an asset owner. Waiting for contractor review.",
            }
        return {"disabled": False, "message": ""}


def get_cached_inspections(inspection_uuids: set[str]) -> dict[str, Any]:
    """
    Retrieve any cached inspections for a given uuid set.

    :param inspection_uuids: Set of inspection ids to check against cached inspections.
    :return: Dictionary of cached inspections with inspection id as keys.
    """
    log.debug(f"start 'cache.get_many': {datetime.now()}")
    cached_inspections = inspection_cache.get_many(inspection_uuids)
    cached_inspections = restore_cached_inspections(cached_inspections)
    log.debug(f"CACHE_HITS: {len(cached_inspections)}")

    return cached_inspections


class InspectionList2QueryParams(BaseModel):
    organisation: int | None = None
    ordering: str | None = None
    search: str | None = None
    page: int = 1
    page_size: int = 25
    status: StatusEnum | None = None
    use_inspection_filters: bool = True
    date_captured: str | None = None
    asset_id: str | None = None
    folder_id: int | None = None
    file_id: int | None = None
    last_related_update__gte: str | None = None
    uuid__in: str | None = None
    use_header_names: bool = False

    model_config = {"extra": "ignore"}

    @field_validator("*", mode="before")
    @classmethod
    def coerce_lists_of_one_item(cls, val):
        """Needed because django query_params is a multidict"""
        if isinstance(val, list) and len(val) == 1:
            return val[0]
        return val


class InspectionList2(BaseInspection, ListCreateAPIView):
    queryset = Inspection.objects.all()
    pagination_class = StatusCountPagination

    search_fields = [
        ("legacy_id", FilterEntity.INSPECTION),
        ("Direction", FilterEntity.INSPECTION_VALUE),
        ("WorkOrder", FilterEntity.INSPECTION_VALUE),
        ("Material", FilterEntity.ASSET_VALUE),
        ("AssetID", FilterEntity.ASSET_VALUE),
        ("LocationStreet", FilterEntity.ASSET_VALUE),
        ("LocationTown", FilterEntity.ASSET_VALUE),
        ("UpstreamNode", FilterEntity.ASSET_VALUE),
        ("DownstreamNode", FilterEntity.ASSET_VALUE),
        ("filename", FilterEntity.FILE),
    ]

    ordering_fields = {
        "direction": ("Direction", FilterEntity.INSPECTION_VALUE, FilterDataType.STRING, None),
        "chainage": ("LengthSurveyed", FilterEntity.INSPECTION_VALUE, FilterDataType.NUMBER, 0),
        "date_captured": ("Date", FilterEntity.INSPECTION_VALUE, FilterDataType.DATE, None),
        "material": ("Material", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "asset_id": ("AssetID", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "location_street": ("LocationStreet", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "location_town": ("LocationTown", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "upstream_node": ("UpstreamNode", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "downstream_node": ("DownstreamNode", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "diameter": ("HeightDiameter", FilterEntity.ASSET_VALUE, FilterDataType.NUMBER, 0),
        "filename": ("file__filename", FilterEntity.FILE, FilterDataType.STRING, None),
        "created_time": ("file__created_time", FilterEntity.FILE, FilterDataType.DATE, datetime.min),
        "id": ("legacy_id", FilterEntity.INSPECTION, FilterDataType.NUMBER, None),
        "condition_rating": ("structural_grade", FilterEntity.INSPECTION, FilterDataType.NUMBER, 1),
        "service_condition_rating": ("service_grade", FilterEntity.INSPECTION, FilterDataType.NUMBER, 1),
        "created_at": ("created_at", FilterEntity.INSPECTION, FilterDataType.DATE, datetime.min),
        "status": ("status", FilterEntity.INSPECTION, FilterDataType.STRING, None),
        "last_related_update": ("last_related_update", FilterEntity.INSPECTION, FilterDataType.DATE, datetime.min),
    }

    def get_renderers(self):
        if self.use_header_names():
            return [JSONRenderer()]
        else:
            return [CamelCaseJSONRenderer()]

    def use_header_names(self) -> bool:
        if not self.request:
            return False
        return "use_header_names" in self.request.GET and self.request.GET["use_header_names"].lower() == "true"

    def get_base_queryset(
        self, organisation: Organisations, filter_record: InspectionFilter | None
    ) -> QuerySet[Inspection]:
        qs = super().get_queryset().visible_to_org(organisation)

        if filter_record:
            folder_ids = [folder["id"] for folder in filter_record.folder_filter_model]
            query = Q(file__job_tree__in=folder_ids) | (Q(folder__in=folder_ids) & Q(status=StatusEnum.PLANNED.value))
            qs = qs.filter(query)

        qs = qs.filter(Q(legacy_id__isnull=False) | Q(status=StatusEnum.PLANNED.value))

        return qs.order_by("-legacy_id")

    def get_queryset(
        self,
        queryset: QuerySet[Inspection],
        filter_record: InspectionFilter | None,
        params: InspectionList2QueryParams,
    ):
        filters = []

        if params.status:
            queryset = queryset.filter(status=params.status.value)
        elif filter_record and (status_filters := filter_record.filter_model.get("common_filters", {}).get("status")):
            if len(status_filters["value"]) > 0:
                filters.append(
                    InspectionSearchFilterModel(
                        name="status",
                        value=status_filters["value"],
                        operator=OperatorEnum.IN,
                        filter_type=FilterEntity.INSPECTION,
                    )
                )

        if filter_record and (header_filters := filter_record.filter_model.get("header_filters")):
            filters.extend(InspectionSearchFilterModel.from_header_filter(hf) for hf in header_filters)

        if params.date_captured:
            filters.append(
                InspectionSearchFilterModel(
                    name="Date",
                    value=[params.date_captured],
                    operator=OperatorEnum.EQ,
                    filter_type=FilterEntity.INSPECTION_VALUE,
                )
            )

        if params.asset_id:
            filters.append(
                InspectionSearchFilterModel(
                    name="AssetID",
                    value=[params.asset_id],
                    operator=OperatorEnum.EQ,
                    filter_type=FilterEntity.ASSET_VALUE,
                )
            )

        for filter_condition in filters:
            queryset = queryset.filter(filter_condition.to_query())

        if params.folder_id:
            queryset = queryset.filter(folder=params.folder_id)

        if params.file_id:
            queryset = queryset.filter(file=params.file_id)

        if params.last_related_update__gte:
            queryset = queryset.filter(last_related_update__gte=params.last_related_update__gte)

        if params.uuid__in:
            uuids = params.uuid__in.split(",")
            queryset = queryset.filter(uuid__in=uuids)

        if search_term := params.search:
            search_filters = self.create_search_filters(search_term)
            search_query = Q()
            for sf in search_filters:  # Note that search filters are OR'd unlike other filters
                search_query |= sf.to_query()
            queryset = queryset.filter(search_query)

        return queryset

    def get_ordered_queryset(self, queryset: QuerySet[Inspection], ordering_field: str) -> QuerySet[Inspection]:
        if not ordering_field or ordering_field == "none":
            return queryset.order_by("-legacy_id")

        is_descending = False
        ordering = camel_to_snake(ordering_field)

        if ordering.startswith("-"):
            is_descending = True
            ordering_field = ordering_field[1:]

        if ordering_field not in self.ordering_fields:
            return queryset.order_by("-legacy_id")

        prefix = "-" if is_descending else ""
        header_name, entity, data_type, _ = self.ordering_fields[ordering_field]

        ordering_column = None
        if entity == FilterEntity.ASSET_VALUE:
            ordering_column = Subquery(build_asset_value_order_subquery(header_name, data_type))
        elif entity == FilterEntity.INSPECTION_VALUE:
            ordering_column = Subquery(build_inspection_value_order_subquery(header_name, data_type))
        else:
            ordering_column = data_type.build_type_cast_expression(header_name)

        if ordering_column is not None:
            qs = queryset.annotate(ordering_field=ordering_column).order_by(f"{prefix}ordering_field", "-legacy_id")

        return qs

    def create_search_filters(self, search_term: str) -> list[InspectionSearchFilterModel]:
        search_filters = [
            InspectionSearchFilterModel(
                name=field_name,
                filter_type=entity_type,
                data_type=FilterDataType.STRING,  # All searches done as strings
                value=[search_term],
                operator=OperatorEnum.CT,
            )
            for (field_name, entity_type) in self.search_fields
        ]

        return search_filters

    def order_inspection_list(self, inspection_list: list[InspectionModel], ordering: str) -> list[InspectionModel]:
        """
        Sort an inspection list by an ordering field

        :param inspection_list: the inspection list to be ordered
        :param ordering: the ordering required for the inspection list
        """

        def identifier_sort(item: InspectionModel):
            try:
                return int(item.id)
            except (ValueError, TypeError):
                if item.id:
                    return int("".join(i for i in item.id if i.isnumeric()))
                else:
                    return 0

        # start with an ordered list by ID descending
        inspection_list.sort(key=identifier_sort, reverse=True)

        asset_fields = ["asset_id", "location_street", "location_town", "upstream_node", "downstream_node", "diameter"]
        file_fields = ["created_time", "upload_user", "filename", "file_size"]
        folder_fields = ["folder"]

        # Set default ordering to id while merging of mpl and inspection is in progress
        ordering = "-id" if not ordering or ordering == "none" else ordering

        order_fallback_value = {
            "diameter": 0,
            "chainage": 0,
            "created_at": datetime.min,
            "created_time": datetime.min,
            "condition_rating": 1,
            "service_condition_rating": 1,
        }

        if not ordering or ordering == "none":
            return inspection_list

        is_descending = False
        ordering = camel_to_snake(ordering)

        if ordering.startswith("-"):
            is_descending = True
            ordering = ordering[1:]

        if ordering in file_fields:
            inspections_with_data = list(
                filter(
                    lambda inspection: inspection.file is not None and inspection.file[ordering] is not None,
                    inspection_list,
                )
            )
            inspection_without_data = list(
                filter(
                    lambda inspection: inspection.file is None or inspection.file[ordering] is None,
                    inspection_list,
                )
            )

            ordered_list = sorted(
                inspections_with_data,
                key=lambda inspection: inspection.file[ordering],
                reverse=is_descending,
            )

            if is_descending:
                ordered_list = ordered_list + inspection_without_data
            else:
                ordered_list = inspection_without_data + ordered_list
        elif ordering in folder_fields:
            ordered_list = sorted(
                inspection_list,
                key=lambda x: x.folder["job_name"] if x.folder is not None else "",
                reverse=is_descending,
            )
        elif ordering in asset_fields:
            fallback_value = order_fallback_value.get(ordering, "")
            ordered_list = sorted(
                inspection_list,
                key=lambda x: getattr(x.asset, ordering) if getattr(x.asset, ordering) is not None else fallback_value,
                reverse=is_descending,
            )
        elif ordering == "id":
            ordered_list = sorted(inspection_list, key=identifier_sort, reverse=is_descending)
        else:
            fallback_value = order_fallback_value.get(ordering, "")
            ordered_list = sorted(
                inspection_list,
                key=lambda x: getattr(x, ordering) if getattr(x, ordering) is not None else fallback_value,
                reverse=is_descending,
            )

        return ordered_list

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("ordering", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("search", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page_size", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter(
                "status",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
            ),
            OpenApiParameter(
                "use_inspection_filters",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=True,
            ),
            OpenApiParameter("date_captured", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("asset_id", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("folder_id", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("file_id", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("last_related_update__gte", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter(
                "uuid__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of inspection UUIDs to filter by",
            ),
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Return keys matching the OpenAPI schema field names and the header names",
            ),
        ],
        request=None,
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def get(self, request, *args, **kwargs):
        try:
            query_params = InspectionList2QueryParams.model_validate(self.request.query_params)
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        if query_params.organisation:
            organisation = get_object_or_404(Organisations, id=query_params.organisation)
            # check user has access to organisation
            if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=organisation):
                raise PermissionDenied("You do not have access to this organisation")
        else:
            organisation = self.request.organisation

        filter_record = (
            InspectionFilter.objects.filter(user=request.user, organisation=organisation).first()
            if query_params.use_inspection_filters
            else None
        )

        inspection_qs = self.get_base_queryset(organisation=organisation, filter_record=filter_record)
        inspection_qs = self.get_queryset(
            queryset=inspection_qs,
            filter_record=filter_record,
            params=query_params,
        )
        inspection_qs = self.get_ordered_queryset(inspection_qs, ordering_field=query_params.ordering)
        status_counts = get_status_counts(inspection_qs)
        inspection_count = inspection_qs.count()

        # do custom pagination
        inspection_qs = paginate_queryset(inspection_qs, query_params.page, query_params.page_size)

        inspections = get_inspection_list(inspection_qs)
        inspections = self.order_inspection_list(inspections, ordering=query_params.ordering)
        inspection_dicts = [inspection.model_dump(by_alias=query_params.use_header_names) for inspection in inspections]
        for inspection in inspection_dicts:
            inspection["view_disabled"] = super().get_view_disabled(user=request.user, inspection=inspection)

        payload = build_paginated_response_payload(
            request,
            inspection_dicts,
            count=inspection_count,
            current_page=query_params.page,
            page_size=query_params.page_size,
        )
        # Use camel-cased name so it appears the same whether or not use_header_names is set
        payload["statusCounts"] = status_counts
        return Response(payload)

    @extend_schema(
        parameters=[
            OpenApiParameter("asset_uuid", OpenApiTypes.STR, required=True),
            OpenApiParameter("folder_id", OpenApiTypes.INT, required=True),
            OpenApiParameter("file_id", OpenApiTypes.INT, required=True),
        ],
        request=OpenApiRequest(InspectionCreateModel),
        responses={status.HTTP_201_CREATED: InspectionCreateResponseModel},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a new inspection record
        """

        # Undo the effect of camelCase -> snake_case parser on PascalCase keys
        raw_data = {re.sub(r"_([a-z])", lambda x: x.group(1).upper(), k): v for k, v in request.data.items()}
        try:
            payload = InspectionCreateModel.model_validate(raw_data)
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        asset: Asset = get_object_or_404(Asset, uuid=request.query_params.get("asset_uuid"))

        file_id = request.query_params.get("file_id")
        file: FileList | None = get_object_or_404(FileList, id=file_id) if file_id else None

        folder: JobsTree = get_object_or_404(JobsTree, id=request.query_params.get("folder_id"))

        primary_org = folder.owning_org

        # Check object permissions
        if not HasAccessToOrgScopedObject().has_object_permission(request, view=self, obj=asset):
            raise PermissionDenied("User does not have access to the asset")

        if file and not HasAccessToOrgScopedObject().has_object_permission(
            request, view=self, obj=file, org_field_name="target_org"
        ):
            raise PermissionDenied("User does not have access to the file")

        if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=primary_org):
            raise PermissionDenied("User does not have access to the folder")

        if (file and asset.organisation != file.target_org) or asset.organisation != primary_org:
            return Response(
                "Asset, file and folder must belong to the same organisation", status=status.HTTP_400_BAD_REQUEST
            )

        inspection = Inspection.objects.create(
            asset=asset,
            created_by=request.user,
            folder=folder,
            file=file,
            status=StatusEnum.UPLOADED,
        )

        # Invalidate the asset cache entry to keep inspections_count per asset up to date
        clear_from_asset_cache([asset.uuid])

        # Populate inspection values
        header_names_to_vals = payload.model_dump(mode="json", by_alias=True)
        standard_headers_qs = StandardHeader.objects.filter(
            standard=folder.standard_key,
            header__type=Header.HeaderType.INSPECTION,
            header__name__in=header_names_to_vals.keys(),
        ).select_related("header")
        header_names_to_standard_header = {sh.header.name: sh for sh in standard_headers_qs}
        header_names_to_vals = {k: v for k, v in header_names_to_vals.items() if k in header_names_to_standard_header}
        for header_name, val in header_names_to_vals.items():
            standard_header = header_names_to_standard_header[header_name]
            inspection.create_inspection_value(standard_header, "" if val is None else str(val))

        analytics.send_inspection_created_event(
            get_inspection_representation(inspection, skip_cache=True), inspection.asset.organisation, request.user
        )
        analytics.send_asset_linked_to_inspection_event(
            asset_id=asset.uuid, inspection_id=inspection.uuid, owner_org=asset.organisation, user=request.user
        )

        response_dto = InspectionCreateResponseModel(uuid=inspection.uuid)
        return Response(response_dto.model_dump(mode="json"), status=status.HTTP_201_CREATED)


class InspectionDetail2(BaseInspection, RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    queryset = Inspection.objects.all()
    serializer_class = InspectionSerializer
    lookup_field = "uuid"

    def get_permissions(self):
        if self.request.method == "DELETE":  # Delete is only available to service users
            return [IsAuthenticated(), IsServiceUser()]

        return super().get_permissions()

    def get_queryset(self):
        if self.request.method == "DELETE":
            return self.queryset.all()

        return self.queryset.exclude(file__hidden=True)

    def use_header_names(self) -> bool:
        if not self.request:
            return False
        return "use_header_names" in self.request.GET and self.request.GET["use_header_names"].lower() == "true"

    def get_parsers(self):
        """
        We need to preserve object keys without conversion if we are relying on mapping header names
        """
        if self.use_header_names():
            return [JSONParser()]
        else:
            return [CamelCaseJSONParser()]

    def get_renderers(self):
        if self.use_header_names():
            return [JSONRenderer()]
        else:
            return [CamelCaseJSONRenderer()]

    def handle_status_change(self, request: Request, initial_inspection: InspectionModel, mappoint: MapPointList):
        # check for running repair recommendations
        if initial_inspection.status == StatusEnum.UPLOADED.value:
            if request.data["status"] == StatusEnum.REVIEWED.value:
                # TODO: to we need to record this as an inspection value or against the inspection table directly?
                # header name: ReviewerCertificateNumber
                # then sync to MPL
                mappoint.reviewed_by = request.user.full_name
            if request.data["status"] not in [StatusEnum.PLANNED.value, StatusEnum.UPLOADED.value]:
                self.run_repair_recommendations(mappoint=mappoint)

        # sync status back to mpl table
        mappoint.status = request.data["status"]
        mappoint.save()

    def run_repair_recommendations(self, mappoint: MapPointList):
        inspections = _process_inspections([mappoint])
        generate_repair_recommendations(inspections)
        update_or_create_recommendation(inspections[0], self.request.user)

    @extend_schema(
        responses={status.HTTP_200_OK: InspectionModel},
        parameters=[
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Use header names instead of MPL field names as keys",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Fetch an inspection record using the Pydantic model
        """
        inspection = self.get_object()
        inspection = get_inspection_representation(inspection=inspection, skip_cache=True)
        return Response(inspection.model_dump(by_alias=self.use_header_names()))

    @atomic
    @extend_schema(
        parameters=[
            OpenApiParameter("uuid", OpenApiTypes.STR, OpenApiParameter.PATH, required=True),
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Use header names instead of MPL field names as keys",
            ),
        ],
        request=dict[str, Any],
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def patch(self, request, *args, **kwargs):
        """
        Update an inspection record and the associated inspection values.
        """
        data = request.data
        inspection = self.get_object()
        initial_inspection = get_inspection_representation(inspection=inspection, skip_cache=True)

        original_asset = inspection.asset
        if "asset" in data:
            asset = Asset.objects.filter(uuid=data["asset"]).first()

            if not asset:
                raise ValidationError("Unable to find an asset")

            if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=asset.organisation):
                raise PermissionDenied("You do not have permission to assign an Asset for this organisation")

            if asset.standard != inspection.folder.standard_key.id:
                raise ValidationError("Asset standard must match the Inspection Standard")
        else:
            asset = inspection.asset

        if initial_inspection.standard in (
            StandardEnum.NZ_PIPE_MANUAL_3,
            StandardEnum.NZ_PIPE_MANUAL_4,
        ):
            handle_nz_direction(data, initial_inspection, self.use_header_names())

        try:
            super().patch(request, *args, **kwargs)
        except Exception as e:
            log.error(e)
            raise

        inspection = self.get_object()

        # if chainage, update gradings
        if "chainage" in data and int(initial_inspection.chainage) != int(data["chainage"]):
            update_gradings(inspection)

        inspection_repr = get_inspection_representation(inspection=inspection, skip_cache=True)

        AuditList.objects.create(
            event_type="Update",
            table="Inspection",
            description=f"Updated inspection {inspection.uuid}",
            date_of_modification=timezone.now(),
            user=request.user,
            column="Multi",
        )

        mappoint = MapPointList.objects.filter(inspection_id=inspection.uuid).last()
        if mappoint is not None:
            mapped_fields = list(request.data.keys())
            if "asset" in data:
                mapped_fields += [
                    "asset_id",
                    "upstream_node",
                    "downstream_node",
                    "material",
                    "diameter",
                    "name",
                    "pipe_type",
                ]

            sync_to_mappointlist(
                mappointlist=mappoint,
                inspection=inspection_repr,
                mapped_fields=mapped_fields,
            )

            description = f"Update survey {mappoint.id}"
            column = "Multi"

            if "status" in data:
                if len(data.keys()) == 1:
                    column = "status"
                    description = f"From value {initial_inspection.status} to value {request.data['status']}"

                self.handle_status_change(request=request, initial_inspection=initial_inspection, mappoint=mappoint)

            # update auditlist table
            AuditList.objects.create(
                event_type="Update",
                table="Mappointlist",
                row_id=mappoint.id,
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
                column=column,
            )

        analytics.send_inspection_updated_event(inspection_repr, inspection.asset.organisation, request.user)
        if asset != original_asset:
            analytics.send_asset_linked_to_inspection_event(
                asset_id=asset.uuid, inspection_id=inspection.uuid, owner_org=asset.organisation, user=request.user
            )
            # Invalidate the asset cache entry to keep inspections_count per asset up to date
            clear_from_asset_cache([asset.uuid])

        inspection_cache.delete(inspection.uuid)

        return Response(
            inspection_repr.model_dump(by_alias=self.use_header_names(), mode="json"), status=status.HTTP_200_OK
        )

    @atomic
    def delete(self, request, *args, **kwargs):
        """
        Delete an inspection and child entities
        """

        inspection: Inspection = self.get_object()
        inspection_cache.delete(str(inspection.uuid))
        RepairRecommendation.objects.filter(target__inspection=inspection).delete()
        MapPointList.objects.filter(inspection=inspection).delete()
        inspection.delete()

        # Invalidate the asset cache entry to keep inspections_count per asset up to date
        clear_from_asset_cache([inspection.asset.uuid])

        return Response(status=status.HTTP_204_NO_CONTENT)


class InspectionList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionListSerializer
    queryset = (
        MapPointList.objects.select_related("associated_file")
        .select_related("standard_key")
        .select_related("inspection")
        .filter(deleted_at__isnull=True)
        .exclude(associated_file__hidden=True)
    )
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = [
        "id",
        "asset_id",
        "name",
        "upstream_node",
        "downstream_node",
        "direction",
        "material",
        "associated_file__filename",
    ]
    ordering_fields = [
        "id",
        "status",
        "asset_id",
        "name",
        "upstream_node",
        "downstream_node",
        "direction",
        "material",
        "chainage",
        "diameter",
        "date_captured",
        "condition_rating",
        "service_condition_rating",
        "inspection_notes",
        "associated_file__filename",
        "associated_file__file_size",
        "associated_file__created_time",
        "associated_file__upload_user",
        "associated_file__job_tree__job_name",
        "associated_file__created_time",
    ]
    ordering = ["-id"]
    pagination_class = StandardResultsSetPagination

    def get_base_queryset(self, folder_ids):
        """
        The base queryset is the set of inspections after the folder filter has been applied.
        This has been separated out so that we can collect total status counts on the list of folders requested
         before we apply other filters including the status filter.
        """
        queryset = self.queryset
        folder_ids = [int(x) for x in folder_ids.split(",")]

        query_filter = (
            Q(associated_file__job_tree__in=folder_ids)
            | Q(inspection__file__job_tree__in=folder_ids)
            | (Q(inspection__folder__in=folder_ids) & Q(inspection__status=StatusEnum.PLANNED.value))
        )
        return queryset.filter(query_filter)

    def get_queryset(self, folder_ids):
        queryset = self.get_base_queryset(folder_ids=folder_ids)

        is_asset_owner = self.request.organisation.is_asset_owner
        asset_owners = AssetOwners.objects.select_related("org").filter(org=self.request.organisation)

        query_filter = Q()

        # TODO: before we get here, we should have already checked that the user has access to the folder
        if is_asset_owner and asset_owners.exists():
            # Asset owner can view inspections uploaded by their own org or their contractors (they are target_org in either case)
            query_filter &= Q(associated_file__target_org=self.request.organisation) | Q(
                inspection__asset__organisation=self.request.organisation
            )
        else:
            asset_owners = (
                AssetOwners.objects.prefetch_related("contractor")
                .select_related("org")
                .filter(contractor__org=self.request.organisation)
            )
            organisations = [asset_owner.org for asset_owner in asset_owners]
            organisations.append(self.request.organisation)
            query_filter &= Q(associated_file__upload_org=self.request.organisation) | Q(
                inspection__asset__organisation__in=organisations
            )

        status = self.request.query_params.get("status", None)
        if status is not None:
            status = status.split(",")
            for item in status:
                if item not in StatusEnum.get_status_list():
                    raise ValidationError("Invalid status")
            query_filter &= Q(status__in=status)

        return queryset.filter(query_filter)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "folder_ids",
                OpenApiTypes.STR,
                required=True,
                many=True,
                explode=False,
                description="List of job ids (folder ids)",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of inspections
        """
        # pylint: disable=protected-access
        folder_ids = request.query_params.get("folder_ids", None)
        ordering = request.query_params.get("ordering", None)

        if not folder_ids:
            raise serializers.ValidationError("You need to supply folders to retrieve inspections")

        # Generate status counts post folder filter and pre column filter
        base_queryset = self.get_base_queryset(folder_ids=folder_ids)
        status_counts_result = (
            base_queryset.filter(status__in=StatusEnum.get_status_list())
            .values("status")
            .order_by("status")
            .annotate(count=Count("status"))
        )
        status_counts = {r["status"]: r["count"] for r in status_counts_result}

        # matching
        for_matching = (
            ImportedInspectionFile.objects.select_related("folder")
            .select_related("organisation")
            .select_related("created_by")
            .filter(folder__in=[int(x) for x in folder_ids.split(",")])
            .values("folder")
            .order_by("folder")
            .annotate(count=Count("folder"))
        )

        matching_count = {r["folder"]: r["count"] for r in for_matching}

        if ordering:
            prefix = ""
            field = ordering
            find_prefix = ordering.split("-")

            if len(find_prefix) == 2:
                prefix = "-"
                field = find_prefix[1]

            if field in [
                "videoName",
                "jobName",
                "fileSize",
                "videoUser",
                "uploadedTime",
            ]:
                ordering_map = {
                    "videoName": "associated_file__filename",
                    "jobName": "associated_file__job_tree__job_name",
                    "fileSize": "associated_file__file_size",
                    "videoUser": "associated_file__upload_user",
                    "uploadedTime": "associated_file__created_time",
                }

                ordering_field = ordering_map[field]
                updated_ordering_field = prefix + ordering_field
            else:
                updated_ordering_field = re.sub("([A-Z]+)", r"_\1", ordering).lower()

            if updated_ordering_field != "id" and updated_ordering_field != "-id":
                updated_ordering_field = f"{updated_ordering_field},-id"

            request.query_params._mutable = True
            request.query_params["ordering"] = updated_ordering_field
            request.query_params._mutable = False

        # Custom implementation of super().list() - allows for serializer context
        queryset = self.filter_queryset(self.get_queryset(folder_ids=folder_ids))

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(
                page,
                many=True,
                context={"request": request, "matching_count": matching_count},
            )
            response = self.get_paginated_response(serializer.data)
            response.data["status_counts"] = status_counts
            return response

        serializer = self.get_serializer(
            queryset,
            many=True,
            context={"request": request, "matching_count": matching_count},
        )
        response = Response(serializer.data)
        response.data["status_counts"] = status_counts

        return response


class PlannedInspectionList(InspectionList):
    @extend_schema(
        parameters=[
            OpenApiParameter("folder_id", OpenApiTypes.INT, required=True),
        ]
    )
    def get(self, request, *args, **kwargs):
        folder_id = request.query_params.get("folder_id", None)

        if folder_id in [None, ""] or "," in folder_id:
            raise serializers.ValidationError("You need to supply a single folder to retrieve planned inspections")

        queryset = self.get_base_queryset(folder_ids=folder_id).filter(status="Planned").order_by("-id")
        self.paginate_queryset(queryset)
        serializer = self.get_serializer(queryset, many=True, context={"request": request})
        response = self.get_paginated_response(serializer.data)

        return response


class InspectionDetail(RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasOrganisationAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionDetailSerializer
    queryset = MapPointList.objects.filter(deleted_at__isnull=True)
    lookup_field = "id"
    http_method_names = ["get", "patch"]

    def get_object(self, pk):
        try:
            return MapPointList.objects.get(id=pk)
        except MapPointList.DoesNotExist:
            raise Http404

    def get(self, request, id=None):
        """
        Get an inspection record (mappointlist).
        """
        userobj = request.user
        is_asset_owner = userobj.is_asset_owner()

        inspection = MapPointList.objects.get(pk=id)

        if inspection.inspection:
            organisation = inspection.inspection.asset.organisation
        else:
            organisation = (
                inspection.associated_file.target_org if is_asset_owner else inspection.associated_file.upload_org
            )

        self.check_object_permissions(request, organisation)

        serializer_data = self.serializer_class(inspection)
        return Response(serializer_data.data, status=status.HTTP_200_OK)

    def patch(self, request, id=None):
        """
        Update an inspection record.
        """
        point = self.get_object(pk=id)

        data = request.data

        # location = data.get("location")
        current_status = data.get("status")
        run_repair_recommendations = False

        # audit record info
        description = f"Update survey {point.id}"
        column = "Multi"

        keys = request.data.keys()
        if current_status is not None and len(keys) == 1:
            column = "status"
            description = f"From value {point.status} to value {current_status}"

        chainage = data.get("chainage")

        if current_status is not None and point.status == "Uploaded":
            if current_status == "Reviewed":
                point.reviewed_by = request.user.full_name
            if current_status != "Uploaded":
                run_repair_recommendations = True

            point.status = current_status

        serializer = self.serializer_class(point, data=data, context={"request": request}, partial=True)
        if serializer.is_valid(raise_exception=True):
            serializer.save()

        if run_repair_recommendations:
            inspections = _process_inspections([point])
            generate_repair_recommendations(inspections)
            update_or_create_recommendation(inspections[0], request.user)

        AuditList.objects.create(
            event_type="Update",
            table="Mappointlist",
            row_id=point.id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
            column=column,
        )

        # serializer is valid so can update the grades
        if chainage and point.chainage != chainage:
            update_gradings(point.inspection)

        return Response(serializer.data, status=status.HTTP_200_OK)


class StandardInspectionDetail(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasMapPointListAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get"]

    def get_object(self, id):
        try:
            return MapPointList.objects.get(id=id)
        except MapPointList.DoesNotExist:
            raise Http404

    def build_response_data(self, inspection: MapPointList) -> dict:
        inspection_values = inspection.inspection.get_inspection_values()
        inspection_value_list = InspectionValueListSerializer(inspection_values, many=True).data
        asset_values = inspection.inspection.asset.get_asset_values()
        asset_value_list = AssetValueListSerializer(asset_values, many=True).data
        for item in inspection_value_list:
            item["type"] = "inspection"
        for item in asset_value_list:
            item["type"] = "asset"

        response_data = inspection_value_list + asset_value_list

        return response_data

    @extend_schema(responses={status.HTTP_200_OK: AssetValueListSerializer(many=True)})
    def get(self, request, id=None):
        """
        GET the inspection values and asset values associated with a mappointlist through mappointlist.inspection.
        """
        inspection = self.get_object(id=id)
        self.check_object_permissions(request, inspection)

        if not inspection.inspection:
            raise ValidationError("This inspection (mappointlist) does not have an associated inspection")

        response_data = self.build_response_data(inspection)

        return Response(response_data, status=status.HTTP_200_OK)


class InspectionValueDetail(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    serializer_class = InspectionValueSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    request_schema = schemas.update_value_request_schema
    http_method_names = ["patch"]

    def get_object(self, uuid):
        try:
            return InspectionValue.objects.get(uuid=uuid)
        except InspectionValue.DoesNotExist:
            raise Http404

    def check_for_mapped_field(self, inspection: Inspection, standard_header: StandardHeader):
        """
        Checks to see if we need to sync the value to a mappointlist field.
        """
        mapped_mpl_field = standard_header.get_mapped_mpl_field()

        if mapped_mpl_field:
            mappointlist = MapPointList.objects.filter(inspection=inspection).first()
            inspection_model = get_inspection_representation(inspection=mappointlist.inspection, skip_cache=True)
            sync_to_mappointlist(
                mappointlist=mappointlist,
                inspection=inspection_model,
                mapped_fields=[mapped_mpl_field],
            )

    @extend_schema(
        request=OpenApiRequest(schemas.update_value_request_schema),
    )
    def patch(self, request, uuid):
        """
        Update a inspection value record.
        """
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection_value = self.get_object(uuid)

        self.check_object_permissions(request, inspection_value.inspection)

        data = request.data.copy()
        data["standard_header"] = inspection_value.standard_header.uuid

        serializer = self.serializer_class(
            data=data,
            instance=inspection_value,
            context={"standard_header": inspection_value.standard_header},
        )
        serializer.is_valid(raise_exception=True)
        instance = InspectionValue(**serializer.validated_data)
        validation_errors = validate_standard_value(instance)

        if len(validation_errors) > 0:
            raise ValidationError(validation_errors)

        serializer.save(context={"standard_header": inspection_value.standard_header})

        inspection = inspection_value.inspection

        if inspection_value.standard_header.header.name == "Direction":
            start_node = inspection.get_start_node_inspection_value()
            end_node = inspection.get_end_node_inspection_value()

            if start_node and end_node:
                start_node_value = str(start_node.value)
                end_node_value = str(end_node.value)

                if start_node_value and start_node_value:
                    start_node.value = end_node_value
                    start_node.save()

                    end_node.value = start_node_value
                    end_node.save()

        self.check_for_mapped_field(
            inspection=inspection_value.inspection,
            standard_header=inspection_value.standard_header,
        )

        inspection_cache.delete(inspection_value.inspection.uuid)

        return Response(serializer.data, status=status.HTTP_200_OK)


class InspectionMatch(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasMapPointListAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionDetailSerializer
    request_schema = schemas.inspection_id_schema

    def get_object(self, id):
        try:
            return MapPointList.objects.get(id=id)
        except MapPointList.DoesNotExist:
            raise Http404

    @extend_schema(
        request=OpenApiRequest(schemas.inspection_id_schema),
    )
    def patch(self, request, id):
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection = self.get_object(id)
        self.check_object_permissions(request, inspection)

        inspection_to_match_id = request.data.get("inspection_id")
        inspection_to_match = MapPointList.objects.filter(id=inspection_to_match_id).first()

        if not inspection_to_match:
            raise ValidationError("Unable to find inspection to match")

        self.check_object_permissions(request, inspection_to_match)

        if inspection_to_match.status != "Planned":
            raise ValidationError("inspection_id must have a status of Planned")

        if id == inspection_to_match_id:
            raise ValidationError("inspection_id cannot be the same as the inspection being matched")

        if inspection.get_folder_id() != inspection_to_match.get_folder_id():
            raise ValidationError("inspection_id must be in the same folder as the inspection being matched")

        # inspection to match fields need to override the inspection fields
        # unless the inspection to match field is empty.
        mapped_mpl_fields = Header.objects.filter(~Q(mapped_mpl_field="")).values_list("mapped_mpl_field", flat=True)

        for field in mapped_mpl_fields:
            if field == "opposite of direction":
                break

            if getattr(inspection_to_match, field) is not None:
                setattr(inspection, field, getattr(inspection_to_match, field))

        inspection.inspection = inspection_to_match.inspection
        inspection.save()

        inspection_to_match.soft_delete()

        description = f"Match survey {inspection.id} to survey {inspection_to_match.id}"

        AuditList.objects.create(
            event_type="Update",
            table="Mappointlist",
            row_id=inspection.id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        serializer = self.serializer_class(inspection)
        return Response(serializer.data, status=status.HTTP_200_OK)


class InspectionBulkMove(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_folder(self, id):
        try:
            return JobsTree.objects.get(id=id)
        except JobsTree.DoesNotExist:
            raise NotFound("Folder not found (id:{})".format(id))

    @extend_schema(
        request=OpenApiRequest(schemas.base_bulk_inspection_request_schema),
        responses={status.HTTP_200_OK: str},
    )
    def patch(self, request, folder_id):
        """
        Bulk move inspections to a folder. Id is referring to the target folder id.
        Planned inspections can't be moved as they don't have an associated file.
        """
        destination_folder_id = folder_id
        destination_folder = self.get_folder(destination_folder_id)

        # Business level - permission checking
        if not destination_folder.can_contain_inspections or destination_folder.secondary_org:
            raise PermissionDenied(
                "Cannot move files (inspections) into the root folder or folders that contain other folders"
            )

        # TODO: check if the user has permission to move files into the destination folder
        # TODO: check if the user has access to the files to be moved

        # Moving by inspection id list
        if request.data.get("inspection_ids", None) is not None:
            inspections = Inspection.objects.filter(uuid__in=request.data["inspection_ids"])

            # check all inspections have an associated file
            if inspections.filter(file__isnull=True).exists():
                raise PermissionDenied("Moving inspections without an associated file is not supported")

            inspections = (
                Inspection.objects.filter(uuid__in=request.data["inspection_ids"])
                .filter(Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation))
                .select_related("file__job_tree")
            )

            if len(inspections) == 0:
                raise NotFound("Inspections specified to be moved were not found")

            distinct_inspections = (
                inspections.distinct("file__job_tree").order_by("file__job_tree").select_related("file__job_tree")
            )

            if distinct_inspections.count() > 1:
                # Check if the folders have the same ancestor
                secondary_org = []

                for inspection in distinct_inspections:
                    folder = inspection.file.job_tree
                    sec_org = folder.get_ancestors().exclude(secondary_org=None)
                    if sec_org:
                        sec_org = sec_org.first().secondary_org.id
                    else:
                        sec_org = None

                    if len(secondary_org) == 0:
                        secondary_org.append(sec_org)
                    elif sec_org != secondary_org[0]:
                        return PermissionDenied("Cannot move files (inspections) between organisations")

            current_folder = inspections[0].file.job_tree

        # Moving by current folder
        elif request.data.get("current_folder_id", None) is not None:
            current_folder = self.get_folder(request.data["current_folder_id"])
            inspections = (
                Inspection.objects.filter(file__job_tree=current_folder)
                .filter(Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation))
                .select_related("file__job_tree")
            )
            if len(inspections) == 0:
                raise NotFound("Inspections specified to be moved were not found")

        # No files specified
        else:
            raise ParseError("inspectionIds or currentFolderId required")

        # If destination is contractor, check if current is the same contractor
        # otherwise if AO, they will both be null.
        current_folder_second = current_folder.get_ancestors().exclude(secondary_org=None)
        destination_folder_second = destination_folder.get_ancestors().exclude(secondary_org=None)
        if current_folder_second or destination_folder_second:
            destination_secondary = None
            current_secondary = None
            if destination_folder_second:
                destination_secondary = destination_folder_second.first().secondary_org.id
            if current_folder_second:
                current_secondary = current_folder_second.first().secondary_org.id
            if current_secondary != destination_secondary:
                raise PermissionDenied("Cannot move files (inspections) between the top level folders for contractors.")

        elif current_folder.get_root() != destination_folder.get_root():
            raise PermissionDenied("Cannot move files between organisations")

        # Perform bulk update
        file_bulk_update_list = []
        inspection_bulk_update_list = []

        for inspection in inspections:
            inspection_cache.delete(str(inspection.uuid))
            original_folder = inspection.file.job_tree

            original_folder_org = original_folder.get_root().primary_org
            secondary_folder = original_folder.get_ancestors().exclude(secondary_org=None).first()
            secondary_folder_org = None
            if secondary_folder:
                secondary_folder_org = secondary_folder.secondary_org.id

            # Check original and destination folder belongs to this user org
            if original_folder_org.id != request.organisation.id and secondary_folder_org != request.organisation.id:
                raise PermissionDenied({"message": "You don't have permission to move the files from this folder"})

            inspection.file.job_tree = destination_folder
            inspection.folder = destination_folder
            inspection_bulk_update_list.append(inspection)
            file_bulk_update_list.append(inspection.file)

            description = "From value " + str(original_folder.id) + " to value " + str(destination_folder.id)
            now = timezone.now()
            AuditList.objects.create(
                event_type="Update",
                table="Inspection",
                row_id=inspection.legacy_id,
                column="file.job_tree",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

        if len(file_bulk_update_list) > 0:
            FileList.objects.bulk_update(file_bulk_update_list, ["job_tree"])

        if len(inspection_bulk_update_list) > 0:
            Inspection.objects.bulk_update(inspection_bulk_update_list, ["folder"])

        return Response("Moved successfully")


class ImportedInspectionFileList(CreateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasOrganisationAccess]
    parser_classes = [MultiPartParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = ImportedInspectionFileSerializer
    queryset = ImportedInspectionFile.objects.all()

    @atomic
    def post(self, request, *args, **kwargs):
        """
        Import a csv for creation of planned inspections
        """
        folder = request.data.get("folder", None)
        file = request.FILES["file"]

        folder_obj = None

        if folder:
            folder_obj = JobsTree.objects.filter(id=folder).first()

        if not folder:
            raise ValidationError("Must provide valid folder")

        if not file or file.content_type != "text/csv":
            raise ValidationError("Must provide a csv file")

        top_folder = folder_obj.get_root()

        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=top_folder.primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")

        standard = folder_obj.standard_key
        standard_name = standard.display_name

        csv_data = pd.read_csv(file, keep_default_na=False, na_values=[""])
        csv_data.fillna("", inplace=True)
        csv_data.drop_duplicates(keep="first")

        errors = validate_csv(csv_data=csv_data, standard=standard, folder=folder)

        duplicates = None
        duplicate_rows = []

        if errors:
            error_messages = []
            header_errors = errors["header_errors"]
            data_errors = errors["data_errors"]

            duplicates = errors.get("duplicates", False)
            duplicate_rows = errors.get("duplicate_rows")

            if header_errors:
                if len(errors["missing_required_headers"]) > 0:
                    error_messages.append(f"CSV is missing required headers for {standard_name}.")
                    err_str = ", ".join(map(str, errors["missing_required_headers"]))
                    error_messages.append(f"Missing headers: {err_str}.")

                if len(errors["invalid_headers"]) > 0:
                    error_messages.append(f"CSV contains headers that are not valid for {standard_name}.")
                    err_str = ", ".join(map(str, errors["invalid_headers"]))
                    error_messages.append(f"Invalid headers: {err_str}.")

            if data_errors:
                if len(errors["missing_required_data"]) > 0:
                    error_messages.append(f"CSV is missing required data for {standard_name}.")
                    for entry in errors["missing_required_data"]:
                        data_type_str = entry["required_data_type"]
                        if entry["required_data_type"] == "options":
                            data_type_str = f"one of the following: {entry['options']}"
                        error_messages.append(
                            f"Missing data for {entry['header']} on row {entry['row']}. Expected data type is {data_type_str}."
                        )

                if len(errors["invalid_data"]) > 0:
                    error_messages.append(f"CSV contains invalid data for {standard_name}.")
                    for entry in errors["invalid_data"]:
                        data_type_str = entry["required_data_type"]
                        if entry["required_data_type"] == "options":
                            data_type_str = f"one of the following: {entry['options']}"
                        error_messages.append(
                            f"Invalid data for {entry['header']} with value {entry['value']} on row {entry['row']}. Expected data type is {data_type_str}."
                        )

            if header_errors or data_errors:
                return Response(
                    {
                        "inspections_imported": False,
                        "error_messages": error_messages,
                        **errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        request.data["created_by"] = request.user.id
        request.data["organisation"] = top_folder.primary_org_id
        request.data["file_name"] = file.name

        response = super().post(request, *args, **kwargs)

        file_bytes = file.file.getvalue()
        put_to_platform_blob_storage(
            file=file_bytes,
            blob_name=f"{response.data['uuid']}.csv",
            container_name=settings.BLOB_STORAGE_DRT_CONTAINER,
        )

        file_obj = ImportedInspectionFile.objects.filter(uuid=response.data["uuid"]).first()

        if duplicate_rows:
            file_obj.imported = False
            file_obj.import_action = "awaiting_action"
            file_obj.save()

            response.data = {
                "inspections_imported": False,
                "data": {**response.data},
                "warnings": {
                    "duplicates": duplicates,
                    "duplicate_rows": duplicate_rows,
                },
            }
            return response

        import_inspections.create_inspection_records(
            csv_data=csv_data,
            standard=standard,
            folder=folder_obj,
            inspection_file=response.data["uuid"],
            user=request.user,
            organisation=request.organisation,
        )

        file_obj.imported = True
        file_obj.save()

        description = f"Inspections CSV uploaded by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Create",
            table="InspectionFile",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        serializer = self.serializer_class(file_obj)

        response.data = {
            "inspections_imported": True,
            "data": {**serializer.data},
        }

        return response


class ImportedInspectionFileDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ImportedInspectionFile.objects.all()
    serializer_class = ImportedInspectionFileSerializer

    @atomic
    def post(self, request, uuid):
        """
        Process uploaded inspections file to create inspection records.
        """
        file = ImportedInspectionFile.objects.filter(uuid=uuid).first()
        if not file:
            raise NotFound("Imported inspections file not found")

        if file.imported:
            raise ValidationError("This file has already been imported")

        top_folder = file.folder.get_root()

        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=top_folder.primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")

        action = request.data.get("action", None)

        if not action:
            raise ValidationError("Must provide an action for processing duplicate imported inspections")

        inspections = InspectionValue.objects.filter(imported_inspection_file=file.uuid).first()
        if inspections:
            raise ValidationError("This file has already been imported. Please upload a new file")

        blob = None

        try:
            blob = get_platform_blob_client(
                blob_name=f"{file.uuid}.csv", container_name=settings.BLOB_STORAGE_DRT_CONTAINER
            )
            if not blob.exists():
                raise ValidationError("Unable to locate csv file")
        except Exception:
            raise ValidationError("Unable to locate csv file")

        action_options = [
            "cancel_import",
            "create_duplicates",
            "overwrite_duplicates",
            "ignore_duplicates",
        ]

        if action not in action_options:
            raise ValidationError(f"Action must be one of the following: {action_options.join(',')}")

        if action == "cancel_import":
            file.delete()
            msg = "Import has been cancelled"
        else:
            blob_content = blob.download_blob()
            csv_data = pd.read_csv(StringIO(blob_content.content_as_text()))
            csv_data = csv_data.fillna("")
            csv_data.drop_duplicates(keep="first")
            standard = file.folder.standard_key
            user = CustomUser.objects.filter(id=request.user.id).first()

            data = {
                "csv_data": csv_data,
                "standard": standard,
                "folder": file.folder,
                "inspection_file": file.uuid,
                "user": user,
                "organisation": file.organisation,
                "action": action,
            }

            msg = "Successfully imported inspections"

            import_inspections.create_inspection_records(**data)
            file.imported = True
            file.save()

        description = f"Inspection File processed into DB by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Create",
            table="InspectionFile",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(msg, status=status.HTTP_200_OK)


class BulkUpdateStatus(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionBulkUpdateSerializer
    request_schema = schemas.bulk_update_status_request_schema

    @extend_schema(
        request=OpenApiRequest(schemas.bulk_update_status_request_schema),
        responses={status.HTTP_200_OK: InspectionSerializer(many=True)},
    )
    @atomic
    def post(self, request):
        """
        Bulk update inspection status.
        Planned inspections cannot be updated in bulk.
        """
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        # Set vars
        is_asset_owner = request.organisation.is_asset_owner
        inspection_ids = request.data["inspection_ids"]
        status = request.data["status"]

        serializer_data = {"status": status}

        base_inspections = Inspection.objects.filter(pk__in=inspection_ids)

        if base_inspections.filter(file__isnull=True).exists():
            raise NotFound("You cannot update the status of an inspection without an associated file.")

        inspections = base_inspections.filter(
            Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation)
        )
        if len(inspections) == 0:
            raise NotFound("No inspections returned (Check inspection ids and users permissions)")

        # Count related contractors
        asset_owner_exists = AssetOwners.objects.filter(org=request.organisation).exists()
        related_contractor_count = 0
        if is_asset_owner and asset_owner_exists:
            related_contractor_count = request.organisation.assetowners.contractor.count()

        # Setup serializer and validate data
        serializer = self.serializer_class(data=serializer_data, partial=True)
        serializer.is_valid(raise_exception=True)
        mpl_bulk_update_list = []
        inspection_bulk_update_list = []

        invalid_ids = []
        if serializer.validated_data["status"] == StatusEnum.UPLOADED and related_contractor_count > 0:
            # If asset_owner has contractor and the points from uploaded belong to them
            # Record those points, and exclude them from saving
            related_contractors = request.organisation.assetowners.contractor.all()
            contractor_inspections = inspections.filter(file__upload_org__contractors__in=related_contractors)
            if len(contractor_inspections) > 0:
                inspections = inspections.exclude(uuid__in=contractor_inspections)
                if len(inspections) == 0:
                    raise PermissionDenied("Cannot move inspections uploaded by contractors")
                else:
                    invalid_ids = list(contractor_inspections.values_list("id", flat=True))

        elif serializer.validated_data["status"] != StatusEnum.UPLOADED and related_contractor_count > 0:
            # if move to other status, exclude points from contractor uploaded where original status is uploaded
            related_contractors = request.organisation.assetowners.contractor.all()
            inspections = inspections.exclude(
                file__upload_org__contractors__in=related_contractors,
                status=StatusEnum.UPLOADED,
            )
            if len(inspections) == 0:
                raise PermissionDenied("Cannot move inspections uploaded by contractors")
        rr_run = []
        now = timezone.now()
        for inspection in inspections:
            original_status = inspection.status
            inspection.status = serializer.validated_data["status"]
            inspection.last_related_update = now

            if original_status == StatusEnum.UPLOADED and inspection.status != original_status:
                rr_run.append(inspection.legacy_id)

            mappoint = MapPointList.objects.get(inspection_id=inspection.uuid)
            mappoint.status = serializer.validated_data["status"]
            if serializer.validated_data["status"] == StatusEnum.REVIEWED and original_status == StatusEnum.UPLOADED:
                mappoint.reviewed_by = request.user.full_name
                # TODO this needs to be recorded on the inspection record as well as MPL
                proc_obj = ProcessingList.objects.filter(associated_file=inspection.file).first()
                video_id = inspection.file.id
                if proc_obj:
                    if proc_obj.request_endpoint != "":
                        payload = {
                            "video_id": video_id,
                            "progress": "COMPLETE",
                            "results": {
                                "Asset Data": MapPointLinkJSON(mappoint, request.organisation),
                                "Frame Data": [
                                    VideoFrameJSON(frame)
                                    for frame in VideoFrames.objects.prefetch_related("parent_video", "defect_scores")
                                    .filter(parent_video__pk=video_id)
                                    .filter(is_hidden=False)
                                    .exclude(defect_scores__is_shown=False)
                                ],
                            },
                        }
                        requests.post(proc_obj.request_endpoint, body=payload)
                    proc_obj.delete()
                objs = VideoFrames.objects.filter(parent_video__id=video_id).filter(is_accepted=False)
                for obj in objs:
                    obj.is_accepted = True
                    obj.save()
            mpl_bulk_update_list.append(mappoint)
            inspection_bulk_update_list.append(inspection)
            description = "From value " + str(original_status) + " to value " + serializer.validated_data["status"]
            AuditList.objects.create(
                event_type="Update",
                table="MapPointList",
                row_id=mappoint.id,
                column="status",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

        MapPointList.objects.bulk_update(mpl_bulk_update_list, ["status", "reviewed_by"])
        Inspection.objects.bulk_update(inspection_bulk_update_list, ["status", "last_related_update"])

        run_many_repair_recommendations(rr_run, request.user)

        description = f"Bulk update status to {status} requested by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Export",
            table="MapPointList",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        insp_ids = list(str(insp.uuid) for insp in inspections)
        inspection_cache.delete_many(insp_ids)

        if len(invalid_ids) > 0:
            return Response({"status": "Partially updated. Invalid ids: " + str(invalid_ids)})
        else:
            return Response(
                [InspectionSerializer(inspection).data for inspection in inspections],
                status=200,
            )


class BulkInspectionValidation(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("inspection_ids", OpenApiTypes.UUID, required=True, many=True, explode=False),
        ],
        responses={status.HTTP_200_OK: schemas.bulk_validation_response_schema},
    )
    def get(self, request):
        """
        Get validation results for a list of inspections
        """

        if "inspection_ids" not in request.query_params:
            raise ParseError("inspection_ids is a required query parameter")

        inspection_uuids = request.query_params["inspection_ids"].split(",")

        inspections_qs = get_inspections_for_validation(inspection_uuids)
        validation = validate_inspections_for_export(inspections_qs)

        return Response(validation)


class InspectionFilterDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_organisation(self, organisation_id):
        try:
            organisation = Organisations.objects.get(id=organisation_id)
        except Organisations.DoesNotExist:
            raise NotFound(CustomError(code="ORGANISATION_NOT_FOUND", message="Organisation not found").serialize())
        return organisation

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation", OpenApiTypes.INT, required=True, description="Organisation ID"),
        ],
        responses={status.HTTP_200_OK: schemas.inspection_filter_response_schema},
    )
    def get(self, request):
        organisation = self.get_organisation(request.GET.get("organisation"))

        try:
            inspection_filter = InspectionFilter.objects.get(organisation=organisation, user=request.user)
            serializer = InspectionFilterSerializer(inspection_filter)
            return Response(serializer.data)
        except InspectionFilter.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        request=OpenApiRequest(schemas.inspection_filter_request_schema),
        responses={status.HTTP_200_OK: schemas.inspection_filter_response_schema},
    )
    def patch(self, request):
        organisation = self.get_organisation(request.data.get("organisation"))

        try:
            inspection_filter = InspectionFilter.objects.get(organisation=organisation, user=request.user)
            serializer = InspectionFilterSerializer(inspection_filter, data=request.data, partial=True)
        except InspectionFilter.DoesNotExist:
            serializer = InspectionFilterSerializer(data=request.data, context={"user": request.user})

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
