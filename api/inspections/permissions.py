from rest_framework.permissions import BasePermission

from api.files.models import ProcessingList


class IsOrgOwnerOfProcessingFile(BasePermission):
    def has_object_permission(self, request, view, obj: ProcessingList):
        return request.organisation == obj.target_org


class IsOrgUploaderOfProcessingFile(BasePermission):
    def has_object_permission(self, request, view, obj: ProcessingList):
        return request.organisation == obj.associated_file.upload_org


class IsOrgOwnerOfFootage(BasePermission):
    def has_object_permission(self, request, view, obj):
        return request.organisation == obj.target_org
