"""
Inspections app signals.
"""

from django.db.models import signals
from django.dispatch import receiver
from django.utils import timezone

from api.common.enums import PipeTypeEnum
from api.files.models import FileList, VideoFrames
from api.inspections.models import InspectionValue, Inspection, Asset, AssetValue, MapPointList
from api.inspections.pydantic_models.inspection_model import get_inspection_representation
from api.inspections.serializers.asset_serializers import AssetSerializer
from api.recommendations.models import RepairRecommendation, Custom_Repair_Values


@receiver(signals.post_save, sender=Inspection)
def inspection_post_create(sender, instance: Inspection, created, *args, **kwargs):
    if not created:
        return

    if instance.file:
        asset = AssetSerializer(instance.asset, context={"skip_cache": True}).data
        inspection_model = get_inspection_representation(instance, asset, skip_cache=True)
        # create mappointlist from InspectionModel data
        mpl_record = MapPointList.objects.create(
            name=inspection_model.name[:200],
            chainage=inspection_model.chainage,
            chainage_unit=inspection_model.chainage_unit,
            diameter=inspection_model.asset.diameter,
            condition_rating=instance.structural_grade,
            service_condition_rating=instance.service_grade,
            date_captured=inspection_model.date_captured,
            asset_id=inspection_model.asset.asset_id[:100] if inspection_model.asset.asset_id else "",
            associated_file=instance.file,
            material=inspection_model.asset.material,
            start_node=inspection_model.start_node[:100] if inspection_model.start_node else "",
            end_node=inspection_model.end_node[:100] if inspection_model.end_node else "",
            direction=inspection_model.direction,
            upstream_node=inspection_model.asset.upstream_node[:100] if inspection_model.asset.upstream_node else "",
            downstream_node=(
                inspection_model.asset.downstream_node[:100] if inspection_model.asset.downstream_node else ""
            ),
            pipe_type=inspection_model.asset.pipe_type.value,
            is_accepted=True,
            job=None,
            sewer_data=inspection_model.asset.pipe_type == PipeTypeEnum.SEWER,
            standard_key=instance.file.job_tree.standard_key,
            status=instance.status,
            inspection_notes=inspection_model.inspection_notes,
            inspection=instance,
        )
        instance.legacy_id = str(mpl_record.id)
        instance.save()


@receiver([signals.post_save, signals.pre_delete], sender=InspectionValue)
def update_inspection_timestamp_on_inspection_value_update(sender, instance: InspectionValue, *args, **kwargs):
    Inspection.objects.filter(uuid=instance.inspection_id).update(last_related_update=timezone.now())


@receiver(signals.post_save, sender=Asset)
def update_inspection_timestamp_on_asset_update(sender, instance: Asset, *args, **kwargs):
    Inspection.objects.filter(asset=instance).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=AssetValue)
def update_inspection_timestamp_on_asset_value_update(sender, instance: AssetValue, *args, **kwargs):
    Inspection.objects.filter(asset=instance.asset_id).update(last_related_update=timezone.now())


@receiver(signals.post_save, sender=FileList)
def update_inspection_timestamp_on_video_update(sender, instance: FileList, *args, **kwargs):
    Inspection.objects.filter(file=instance).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=VideoFrames)
def update_inspection_timestamp_on_video_frame_update(sender, instance: VideoFrames, *args, **kwargs):
    Inspection.objects.filter(file_id=instance.parent_video_id).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=RepairRecommendation)
def update_inspection_timestamp_on_repair_recommendation_update(
    sender, instance: RepairRecommendation, *args, **kwargs
):
    Inspection.objects.filter(mappointlist=instance.target_id).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=Custom_Repair_Values)
def update_inspection_timestamp_on_custom_repair_values_update(sender, instance: Custom_Repair_Values, *args, **kwargs):
    Inspection.objects.filter(mappointlist=instance.point).update(last_related_update=timezone.now())
