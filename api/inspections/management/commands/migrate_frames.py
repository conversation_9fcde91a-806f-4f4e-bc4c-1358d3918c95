from django.core.management import BaseCommand
from django.db import transaction
from tqdm import tqdm

from api.inspections.models import (
    Footage,
    Inspection,
    Keyframe,
    KeyframeObservation,
    Observation,
    VideoFrames,
)


class Command(BaseCommand):
    help = """
    Migrate existing VideoFrames data to the new KeyFrame/Observation structure.
    This is intended as a one-off operation.
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "-r", "--range",
            nargs=2,
            type=int,
            metavar=("MIN_ID", "MAX_ID"),
            help="Optional range of VideoFrame IDs to migrate (inclusive).",
        )

    @transaction.atomic
    def handle(self, *args, **options):
        BATCH_SIZE = 2000
        last_id = 0

        min_id, max_id = None, None
        if options["range"]:
            min_id, max_id = options["range"]

        qs = VideoFrames.objects.all()
        if min_id is not None:
            qs = qs.filter(id__gte=min_id)
            last_id = min_id - 1
        if max_id is not None:
            qs = qs.filter(id__lte=max_id)

        print("Starting migration with batched VideoFrames...")

        all_vf_count = qs.count()
        with tqdm(total=all_vf_count, desc="Processing VideoFrames") as pbar:
            while True:
                batch = list(
                    qs.select_related(
                        "parent_video",
                        "parent_video__job_tree__standard_key",
                        "parent_video__target_org",
                        "defect_model",
                    )
                    .filter(id__gt=last_id)
                    .order_by("id")[:BATCH_SIZE]
                )
                if not batch:
                    break

                pbar.write(f"Processing batch with {len(batch)} video frames (id > {last_id})")

                parent_video_ids = {vf.parent_video_id for vf in batch}

                existing_footage = {
                    f.video_file_id: f for f in Footage.objects.filter(video_file_id__in=parent_video_ids)
                }

                footage_to_create = []
                for vf in batch:
                    if vf.parent_video_id not in existing_footage:
                        footage = Footage(
                            video_file=vf.parent_video,
                            chainage_unit="FT"
                            if getattr(
                                getattr(getattr(vf.parent_video, "job_tree", None), "standard_key", None), "name", ""
                            )
                            in ["PACP7", "PACP8"]
                            else "M",
                            target_org=vf.parent_video.target_org,
                            total_frames=vf.parent_video.total_frames,
                        )
                        footage_to_create.append(footage)
                        existing_footage[vf.parent_video_id] = footage

                if footage_to_create:
                    pbar.write(f"Bulk creating {len(footage_to_create)} Footage records...")
                    Footage.objects.bulk_create(footage_to_create, ignore_conflicts=True)
                    persisted = Footage.objects.filter(video_file_id__in=parent_video_ids)
                    existing_footage = {f.video_file_id: f for f in persisted}

                inspections = {ins.file_id: ins for ins in Inspection.objects.filter(file_id__in=parent_video_ids)}

                keyframes_to_create = []
                observations_to_create = []

                for vf in batch:
                    try:
                        time_ms = int(vf.time_reference)
                    except (TypeError, ValueError):
                        time_ms = 0

                    keyframes_to_create.append(
                        Keyframe(
                            footage=existing_footage[vf.parent_video_id],
                            video_frame=vf,
                            time_reference_milliseconds=time_ms,
                            sequence_number=vf.frame_id,
                            chainage=vf.chainage_number,
                            is_hidden=vf.is_hidden,
                            at_joint=vf.at_joint if vf.at_joint else False,
                            has_loss_of_vision=("loss of vision" in vf.defect_model.name.lower())
                            if vf.defect_model
                            else False,
                            has_textbox=("textbox" in vf.defect_model.name.lower()) if vf.defect_model else False,
                            has_title=("title" in vf.defect_model.name.lower()) if vf.defect_model else False,
                        )
                    )

                    inspection = inspections.get(vf.parent_video_id)
                    if inspection:
                        observations_to_create.append(
                            Observation(
                                inspection=inspection,
                                remarks=vf.remarks if vf.remarks else "",
                            )
                        )
                    else:
                        observations_to_create.append(None)

                pbar.write(f"Bulk creating {len(keyframes_to_create)} Keyframes...")
                Keyframe.objects.bulk_create(keyframes_to_create, ignore_conflicts=True)
                pbar.write(f"Bulk creating {len([o for o in observations_to_create if o])} Observations...")
                Observation.objects.bulk_create([o for o in observations_to_create if o], ignore_conflicts=True)

                created_keyframes = list(
                    Keyframe.objects.filter(footage__video_file_id__in=parent_video_ids).order_by("-id")[
                        : len(keyframes_to_create)
                    ]
                )
                created_keyframes.reverse()

                created_observations = list(
                    Observation.objects.filter(inspection__file_id__in=parent_video_ids).order_by("-id")[
                        : len([o for o in observations_to_create if o])
                    ]
                )
                created_observations.reverse()

                joins_to_create = []
                obs_idx = 0
                for kf, obs in zip(created_keyframes, observations_to_create):
                    if obs is not None:
                        joins_to_create.append(
                            KeyframeObservation(keyframe=kf, observation=created_observations[obs_idx])
                        )
                        obs_idx += 1

                pbar.write(f"Bulk creating {len(joins_to_create)} KeyframeObservation records...")
                KeyframeObservation.objects.bulk_create(joins_to_create, ignore_conflicts=True)

                pbar.update(len(batch))
                last_id = batch[-1].id

        print("Committing to database...")
