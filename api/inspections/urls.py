from django.urls import path

from .views.footage_views import FootageCreateView, FootageDetailView, FootageKeyframesCreateDestroyView
from .views.inspection_views import (
    BulkInspectionValidation,
    BulkUpdateStatus,
    ImportedInspectionFileDetail,
    ImportedInspectionFileList,
    InspectionBulkMove,
    InspectionDetail,
    InspectionDetail2,
    InspectionMatch,
    InspectionFilterDetail,
    InspectionList,
    InspectionList2,
    InspectionValueDetail,
    PlannedInspectionList,
    StandardInspectionDetail,
)
from .views.frame_views import (
    FrameList,
    FrameList2,
    FrameListByVideo,
    FrameGlobalList,
    VideoFramesCreateUpdateDestroy,
    FrameDetail,
    FrameDefectUpdate,
)
from .views.folder_tree_views import FolderList, FolderDetail, FolderFilter
from .views.observation_views import (
    ObservationListCreateView,
    ObservationRetrieveUpdateDestroyView,
    ObservedFeatureCreateView,
    ObservedFeatureUpdateDestroyView,
    ObservedSubFeatureCreateView,
    ObservedSubFeatureUpdateDestroyView,
    ObservationGlobalListView,
)
from .views.sharing_views import (
    ShareableVideoPlayLink,
    ShareableVideoDownloadLink,
    ShareableImageLink,
    ShareablePDFLink,
    InspectionShareableLinks,
)
from .views.file_views import (
    ProcessingFileListCreate,
    ProcessingFileDetail,
    InspectionFileList,
    InspectionFileUploadComplete,
    FileDownload,
    BulkDeleteFiles,
    FileListView,
    ProcessingFileRetryView,
    AllFilesView,
    InspectionFileDetail,
    InspectionFileUploadMediaView,
    ProcessingFileCheckView,
)

from .views.asset_views import (
    AssetListCreateDestroy,
    AssetRetrieveUpdate,
    AssetValueDetail,
    AssetInspectionsList,
    AssetMatchOrCreateView,
)

urlpatterns = [
    path("inspections", InspectionList.as_view(), name="inspection_list"),
    path("inspections2", InspectionList2.as_view(), name="inspection_list2"),
    path("inspections/<int:id>", InspectionDetail.as_view(), name="inspection_detail"),
    path(
        "inspections/filters",
        InspectionFilterDetail.as_view(),
        name="inspection_filter",
    ),
    path("inspections2/<uuid:uuid>", InspectionDetail2.as_view(), name="inspection_detail2"),
    path(
        "inspections/planned",
        PlannedInspectionList.as_view(),
        name="planned_inspections",
    ),
    path(
        "inspections/<int:id>/shareable-links",
        InspectionShareableLinks.as_view(),
        name="inspection_shareable_links",
    ),
    path("inspections/<int:id>/match", InspectionMatch.as_view(), name="inspection_match"),
    path(
        "inspections/<int:id>/standard-headers",
        StandardInspectionDetail.as_view(),
        name="inspection_standard_headers",
    ),
    path(
        "inspections/inspection-values/<uuid:uuid>",
        InspectionValueDetail.as_view(),
        name="inspection_value_detail",
    ),
    path(
        "inspections/asset-values/<uuid:uuid>",
        AssetValueDetail.as_view(),
        name="asset_value_detail",
    ),
    path("inspections/<int:inspection_id>/frames", FrameList.as_view(), name="frame_list"),
    path("inspections2/<uuid:uuid>/frames", FrameList2.as_view(), name="frame_list_by_inspection_2"),
    path(
        "inspections/videos/<int:video_id>/frames",
        VideoFramesCreateUpdateDestroy.as_view(),
        name="video_frames_create_destroy",
    ),
    path("inspections/frames/<int:pk>", FrameDetail.as_view(), name="frame_detail"),
    path("inspections/frames", FrameGlobalList.as_view(), name="frame_global_list"),
    path(
        "inspections/frames/<int:pk>/update-defect",
        FrameDefectUpdate.as_view(),
        name="frame_detail",
    ),
    path(
        "inspections/<uuid:inspection_uuid>/observations",
        ObservationListCreateView.as_view(),
        name="observations_create_list",
    ),
    path(
        "inspections/observations/<uuid:observation_id>",
        ObservationRetrieveUpdateDestroyView.as_view(),
        name="observation_detail",
    ),
    path(
        "inspections/observations",
        ObservationGlobalListView.as_view(),
        name="observation_global_list",
    ),
    path(
        "inspections/observations/<uuid:observation_id>/features",
        ObservedFeatureCreateView.as_view(),
        name="observed_feature_create",
    ),
    path(
        "inspections/observations/features/<uuid:observed_feature_id>",
        ObservedFeatureUpdateDestroyView.as_view(),
        name="observed_feature_detail",
    ),
    path(
        "inspections/observations/features/<uuid:observed_feature_id>/sub-features",
        ObservedSubFeatureCreateView.as_view(),
        name="observed_sub_feature_create",
    ),
    path(
        "inspections/observations/sub-features/<uuid:observed_sub_feature_id>",
        ObservedSubFeatureUpdateDestroyView.as_view(),
        name="observed_sub_feature_detail",
    ),
    path("inspections/folders", FolderList.as_view()),
    path("inspections/folders/<int:id>", FolderDetail.as_view()),
    path("inspections/folders/search", FolderFilter.as_view()),
    path("inspections/bulk-update-status", BulkUpdateStatus.as_view()),
    path("inspections/bulk-delete", BulkDeleteFiles.as_view()),
    path("inspections/move/<int:folder_id>", InspectionBulkMove.as_view()),
    path(
        "inspections/import",
        ImportedInspectionFileList.as_view(),
        name="import_inspection_file",
    ),
    path(
        "inspections/import/<uuid>/process",
        ImportedInspectionFileDetail.as_view(),
        name="import_inspection",
    ),
    path("inspections/validate", BulkInspectionValidation.as_view(), name="validate_inspections"),
    path("inspections/export/validate", BulkInspectionValidation.as_view(), name="validate_inspections_export"),
    path("files", FileListView.as_view(), name="files_list"),
    path("files/processing/<int:id>", ProcessingFileDetail.as_view()),
    path("files/download", FileDownload.as_view(), name="file_download"),
    path("files/processing", ProcessingFileListCreate.as_view(), name="file_processing_list_create"),
    path("files/processing/<int:file_id>/retry", ProcessingFileRetryView.as_view()),
    path("files/upload", InspectionFileList.as_view(), name="file_upload"),
    path("files/<int:pk>/upload-complete", InspectionFileUploadComplete.as_view(), name="file_upload_complete"),
    path("files/<int:id>", InspectionFileDetail.as_view(), name="file_detail"),
    path("files/<int:id>/upload", InspectionFileUploadMediaView.as_view(), name="file_upload_media"),
    path("files/<int:id>/frames", FrameListByVideo.as_view(), name="frame_list_by_video_file"),
    path("files/all", AllFilesView.as_view(), name="all_files"),
    path(
        "processing/check",
        ProcessingFileCheckView.as_view(),
        name="processing_file_check",
    ),
    path("footage", FootageCreateView.as_view(), name="footage_create"),
    path("footage/<uuid:footage_id>", FootageDetailView.as_view(), name="retrieve_footage_detail"),
    path(
        "footage/<uuid:footage_id>/keyframes",
        FootageKeyframesCreateDestroyView.as_view(),
        name="footage_keyframes_create_destroy",
    ),
]

asset_urls = [
    path("assets", AssetListCreateDestroy.as_view(), name="asset_list_create_destroy"),
    path("assets/<uuid:uuid>", AssetRetrieveUpdate.as_view(), name="asset_retrieve_update"),
    path("assets/<uuid:uuid>/inspections", AssetInspectionsList.as_view(), name="asset_inspections_list"),
    path("assets/match-or-create", AssetMatchOrCreateView.as_view(), name="asset_match_or_create"),
]

external_share_links = [
    path(
        "api/playurl/<inspection_id>",
        ShareableVideoPlayLink.as_view(),
        name="shareable_video_play_url",
    ),
    path(
        "api/vids/<token>",
        ShareableVideoDownloadLink.as_view(),
        name="shareable_video_download_link",
    ),
    path("api/images/<token>", ShareableImageLink.as_view(), name="shareable_image_link"),
    path("api/assetpdf/<token>", ShareablePDFLink.as_view(), name="shareable_pdf_link"),
]
