from django.urls import path

from .views.footage_views import FootageCreateView, FootageDetailView, FootageKeyframesCreateDestroyView
from .views.inspection_views import (
    BulkInspectionValidation,
    BulkUpdateStatus,
    ImportedInspectionFileDetail,
    ImportedInspectionFileList,
    InspectionBulkMove,
    InspectionDetail,
    InspectionDetail2,
    InspectionMatch,
    InspectionFilterDetail,
    InspectionList,
    InspectionList2,
    InspectionValueDetail,
    PlannedInspectionList,
    StandardInspectionDetail,
)
from .views.observation_views import (
    ObservationListCreateView,
    ObservationRetrieveUpdateDestroyView,
    ObservedFeatureCreateView,
    ObservedFeatureUpdateDestroyView,
    ObservedSubFeatureCreateView,
    ObservedSubFeatureUpdateDestroyView,
    ObservationGlobalListView,
)
from .views.sharing_views import (
    ShareableVideoPlayLink,
    ShareableVideoDownloadLink,
    ShareableImageLink,
    ShareablePDFLink,
    InspectionShareableLinks,
)

from .views.asset_views import (
    AssetList<PERSON>reateDestroy,
    AssetRetrieveUpdate,
    AssetValueDetail,
    AssetInspectionsList,
    AssetMatchOrCreateView,
)

urlpatterns = [
    path("inspections", InspectionList.as_view(), name="inspection_list"),
    path("inspections2", InspectionList2.as_view(), name="inspection_list2"),
    path("inspections/<int:id>", InspectionDetail.as_view(), name="inspection_detail"),
    path(
        "inspections/filters",
        InspectionFilterDetail.as_view(),
        name="inspection_filter",
    ),
    path("inspections2/<uuid:uuid>", InspectionDetail2.as_view(), name="inspection_detail2"),
    path(
        "inspections/planned",
        PlannedInspectionList.as_view(),
        name="planned_inspections",
    ),
    path(
        "inspections/<int:id>/shareable-links",
        InspectionShareableLinks.as_view(),
        name="inspection_shareable_links",
    ),
    path("inspections/<int:id>/match", InspectionMatch.as_view(), name="inspection_match"),
    path(
        "inspections/<int:id>/standard-headers",
        StandardInspectionDetail.as_view(),
        name="inspection_standard_headers",
    ),
    path(
        "inspections/inspection-values/<uuid:uuid>",
        InspectionValueDetail.as_view(),
        name="inspection_value_detail",
    ),
    path(
        "inspections/asset-values/<uuid:uuid>",
        AssetValueDetail.as_view(),
        name="asset_value_detail",
    ),
    path(
        "inspections/<uuid:inspection_uuid>/observations",
        ObservationListCreateView.as_view(),
        name="observations_create_list",
    ),
    path(
        "inspections/observations/<uuid:observation_id>",
        ObservationRetrieveUpdateDestroyView.as_view(),
        name="observation_detail",
    ),
    path(
        "inspections/observations",
        ObservationGlobalListView.as_view(),
        name="observation_global_list",
    ),
    path(
        "inspections/observations/<uuid:observation_id>/features",
        ObservedFeatureCreateView.as_view(),
        name="observed_feature_create",
    ),
    path(
        "inspections/observations/features/<uuid:observed_feature_id>",
        ObservedFeatureUpdateDestroyView.as_view(),
        name="observed_feature_detail",
    ),
    path(
        "inspections/observations/features/<uuid:observed_feature_id>/sub-features",
        ObservedSubFeatureCreateView.as_view(),
        name="observed_sub_feature_create",
    ),
    path(
        "inspections/observations/sub-features/<uuid:observed_sub_feature_id>",
        ObservedSubFeatureUpdateDestroyView.as_view(),
        name="observed_sub_feature_detail",
    ),
    path("inspections/bulk-update-status", BulkUpdateStatus.as_view()),
    path("inspections/move/<int:folder_id>", InspectionBulkMove.as_view()),
    path(
        "inspections/import",
        ImportedInspectionFileList.as_view(),
        name="import_inspection_file",
    ),
    path(
        "inspections/import/<uuid>/process",
        ImportedInspectionFileDetail.as_view(),
        name="import_inspection",
    ),
    path("inspections/validate", BulkInspectionValidation.as_view(), name="validate_inspections"),
    path("inspections/export/validate", BulkInspectionValidation.as_view(), name="validate_inspections_export"),
    path("footage", FootageCreateView.as_view(), name="footage_create"),
    path("footage/<uuid:footage_id>", FootageDetailView.as_view(), name="retrieve_footage_detail"),
    path(
        "footage/<uuid:footage_id>/keyframes",
        FootageKeyframesCreateDestroyView.as_view(),
        name="footage_keyframes_create_destroy",
    ),
]

asset_urls = [
    path("assets", AssetListCreateDestroy.as_view(), name="asset_list_create_destroy"),
    path("assets/<uuid:uuid>", AssetRetrieveUpdate.as_view(), name="asset_retrieve_update"),
    path("assets/<uuid:uuid>/inspections", AssetInspectionsList.as_view(), name="asset_inspections_list"),
    path("assets/match-or-create", AssetMatchOrCreateView.as_view(), name="asset_match_or_create"),
]

external_share_links = [
    path(
        "api/playurl/<inspection_id>",
        ShareableVideoPlayLink.as_view(),
        name="shareable_video_play_url",
    ),
    path(
        "api/vids/<token>",
        ShareableVideoDownloadLink.as_view(),
        name="shareable_video_download_link",
    ),
    path("api/images/<token>", ShareableImageLink.as_view(), name="shareable_image_link"),
    path("api/assetpdf/<token>", ShareablePDFLink.as_view(), name="shareable_pdf_link"),
]
