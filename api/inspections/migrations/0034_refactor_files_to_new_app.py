import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("files", "0001_initial"),
        ("inspections", "0033_remove_observation_keyframes"),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.RemoveField(
                    model_name="processinglist",
                    name="associated_file",
                ),
                migrations.RemoveField(
                    model_name="videoframes",
                    name="parent_video",
                ),
                migrations.AlterField(
                    model_name="mappointlist",
                    name="associated_file",
                    field=models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="files.filelist",
                    ),
                ),
                migrations.AlterField(
                    model_name="inspection",
                    name="file",
                    field=models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="files.filelist",
                    ),
                ),
                migrations.AlterField(
                    model_name="footage",
                    name="video_file",
                    field=models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="derived_footage",
                        to="files.filelist",
                    ),
                ),
                migrations.RemoveField(
                    model_name="resultsfileassets",
                    name="associated_file",
                ),
                migrations.RemoveField(
                    model_name="jobs",
                    name="Organisation",
                ),
                migrations.RemoveField(
                    model_name="jobs",
                    name="standard_key",
                ),
                migrations.AlterField(
                    model_name="mappointlist",
                    name="job",
                    field=models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to="files.jobs"
                    ),
                ),
                migrations.RemoveField(
                    model_name="jobstree",
                    name="primary_org",
                ),
                migrations.RemoveField(
                    model_name="jobstree",
                    name="secondary_org",
                ),
                migrations.RemoveField(
                    model_name="jobstree",
                    name="standard_key",
                ),
                migrations.AlterField(
                    model_name="inspection",
                    name="folder",
                    field=models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="files.jobstree",
                    ),
                ),
                migrations.AlterField(
                    model_name="importedinspectionfile",
                    name="folder",
                    field=models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="files.jobstree",
                    ),
                ),
                migrations.RemoveField(
                    model_name="processinglist",
                    name="standard_key",
                ),
                migrations.RemoveField(
                    model_name="processinglist",
                    name="target_org",
                ),
                migrations.RemoveField(
                    model_name="resultsfile",
                    name="organisation",
                ),
                migrations.RemoveField(
                    model_name="resultsfileassets",
                    name="results_file",
                ),
                migrations.RemoveField(
                    model_name="resultsfiledefects",
                    name="results_file_asset",
                ),
                migrations.RemoveField(
                    model_name="resultsfiledefects",
                    name="associated_video_frame",
                ),
                migrations.RemoveField(
                    model_name="videoframes",
                    name="defect_model",
                ),
                migrations.RemoveField(
                    model_name="videoframes",
                    name="defect_scores",
                ),
                migrations.AlterField(
                    model_name="keyframe",
                    name="video_frame",
                    field=models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="keyframes",
                        to="files.videoframes",
                    ),
                ),
                migrations.DeleteModel(
                    name="FileList",
                ),
                migrations.DeleteModel(
                    name="Jobs",
                ),
                migrations.DeleteModel(
                    name="JobsTree",
                ),
                migrations.DeleteModel(
                    name="ProcessingList",
                ),
                migrations.DeleteModel(
                    name="ResultsFile",
                ),
                migrations.DeleteModel(
                    name="ResultsFileAssets",
                ),
                migrations.DeleteModel(
                    name="ResultsFileDefects",
                ),
                migrations.DeleteModel(
                    name="VideoFrames",
                ),
            ],
            database_operations=[], # Don't actually drop tables since we want the data in the files app
        )
    ]
