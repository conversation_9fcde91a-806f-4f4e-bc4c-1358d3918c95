# Generated by Django 5.0.8 on 2025-07-04 03:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "defects",
            "0042_remove_standardsubfeature_standardsubfeature_numeric_xor_categorical_and_more",
        ),
        (
            "inspections",
            "0030_observedsubfeature_unique_observed_sub_feature_per_feature",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="observedsubfeature",
            name="observed_sub_feature_unit_required_if_numeric_value_or_range_set",
        ),
        migrations.RemoveConstraint(
            model_name="observedsubfeature",
            name="observed_sub_feature_cannot_have_numeric_value_or_range_if_option_selected",
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("GE", "GENERIC_ERROR"),
                    ("VE", "VALIDATION_ERROR"),
                    ("FC", "FILE_CORRUPTED"),
                    ("TW", "TIMEOUT_WHILE_WAITING"),
                    ("TP", "TIMEOUT_WHILE_PROCESSING"),
                    ("TU", "TIMEOUT_WHILE_UPLOADING"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
        migrations.AddConstraint(
            model_name="observedsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        ("numeric_unit__isnull", True), ("numeric_value__isnull", False)
                    ),
                    models.Q(
                        ("numeric_range_min__isnull", False),
                        ("numeric_unit__isnull", True),
                    ),
                    models.Q(
                        ("numeric_range_max__isnull", False),
                        ("numeric_unit__isnull", True),
                    ),
                    _connector="OR",
                    _negated=True,
                ),
                name="observed_sub_feature_unit_required_if_numeric_value_or_range_set",
            ),
        ),
        migrations.AddConstraint(
            model_name="observedsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        ("numeric_value__isnull", False),
                        ("selected_option__isnull", False),
                    ),
                    models.Q(
                        ("numeric_range_min__isnull", False),
                        ("selected_option__isnull", False),
                    ),
                    models.Q(
                        ("numeric_range_max__isnull", False),
                        ("selected_option__isnull", False),
                    ),
                    _connector="OR",
                    _negated=True,
                ),
                name="observed_sub_feature_cannot_have_numeric_value_or_range_if_option_selected",
            ),
        ),
    ]
