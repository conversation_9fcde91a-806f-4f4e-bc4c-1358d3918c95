# Generated by Django 5.0.8 on 2025-07-24 01:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "inspections",
            "0031_remove_observedsubfeature_observed_sub_feature_unit_required_if_numeric_value_or_range_set_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="observedsubfeature",
            name="numeric_unit",
            field=models.CharField(
                choices=[
                    ("PERC", "PERCENTAGE"),
                    ("METRE", "METRES"),
                    ("MM", "MILLIMETRES"),
                    ("FEET", "FEET"),
                    ("INCH", "INCHES"),
                    ("COUNT", "COUNT"),
                    ("CUBIC_M", "CUBIC_METRES"),
                    ("CUBIC_FT", "CUBIC_FEET"),
                    ("DEGREES", "DEGREES"),
                ],
                default=None,
                max_length=10,
                null=True,
            ),
        ),
    ]
