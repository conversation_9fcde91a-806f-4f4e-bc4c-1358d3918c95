from typing import Iterable, List
from django.core.exceptions import ValidationError

from api.common.errors import CustomValidationError
from api.defects.models import StandardHeader
from api.defects.standards import BaseStandard, get_standard_instance
from api.files.models import VideoFrames
from api.inspections.models import Inspection, InspectionValue, AssetValue


def check_mandatory_headers(headers, standard):
    standard_headers = StandardHeader.objects.filter(standard=standard)
    missing_required_headers = []
    valid_headers = []

    for standard_header in standard_headers:
        if standard_header.name not in headers and standard_header.required:
            missing_required_headers.append(standard_header.name)
        else:
            valid_headers.append(standard_header.name)

    return missing_required_headers, valid_headers


def validate_csv(csv_data, standard, folder):
    header_errors = False
    data_errors = False
    duplicates = False
    headers = csv_data.columns.values
    standard_headers = StandardHeader.objects.filter(standard=standard)
    missing_required_headers = []
    missing_required_data = []
    valid_headers = []
    invalid_headers = []
    invalid_data = []
    duplicate_rows = []

    # check for valid and missing required headers
    missing_required_headers, valid_headers = check_mandatory_headers(headers=headers, standard=standard)

    existing_inspections = Inspection.objects.filter(folder=folder)

    if len(valid_headers) < 1:
        invalid_headers = ["No valid headers present in the csv"]
        return {
            "header_errors": True,
            "invalid_headers": invalid_headers,
            "missing_required_headers": missing_required_headers,
            "missing_required_data": missing_required_data,
            "data_errors": False,
            "invalid_data": invalid_data,
            "duplicates": duplicates,
            "duplicate_rows": duplicate_rows,
        }

    for i, row in csv_data.iterrows():
        # check for invalid headers while we're here
        for value in row.index:
            standard_header = standard_headers.filter(name=value).first()

            if not standard_header:
                invalid_headers.append(value)
                continue

        # check for invalid data based on stored data type
        for j, value in enumerate(row.values):
            standard_header = standard_headers.filter(name=headers[j]).first()

            if not standard_header:
                continue

            data_type = standard_header.data_type
            options = standard_header.options_selections

            if value == "" and standard_header.required:
                missing_required_data.append(
                    {
                        "header": standard_header.name,
                        "value": value,
                        "required_data_type": data_type,
                        "options": options,
                        "row": i + 2,
                    }
                )
                continue

            elif value != "":
                data_error = False

                # this is obviously a pointless check but it's here for completeness
                if data_type == "string" and not str(value):
                    data_error = True

                if data_type == "number" and not isinstance(value, (float, int)):
                    data_error = True

                if data_type == "boolean" and not isinstance(value, bool):
                    data_error = True

                if data_type == "options":
                    if str(value) not in options:
                        data_error = True

                if data_error:
                    invalid_data.append(
                        {
                            "header": standard_header.name,
                            "value": value,
                            "required_data_type": data_type,
                            "options": options,
                            "row": i + 2,
                        }
                    )

        if existing_inspections:
            duplicate_assets = get_duplicate_assets(
                row=row.values,
                folder=folder,
                existing_inspections=existing_inspections,
                standard=standard,
                headers=headers,
            )
            duplicate_inspections = get_duplicate_inspections(
                row=row.values,
                folder=folder,
                existing_inspections=existing_inspections,
                standard=standard,
                headers=headers,
                duplicate_assets=duplicate_assets,
            )

            if len(duplicate_assets) > 0 or len(duplicate_inspections) > 0:
                duplicates = True
                duplicate_rows.append(i + 1)

    invalid_headers = list(dict.fromkeys(invalid_headers))

    if len(invalid_headers) > 0 or len(missing_required_headers) > 0:
        header_errors = True

    if len(invalid_data) > 0 or len(missing_required_data) > 0:
        data_errors = True

    if header_errors or data_errors or duplicates:
        return {
            "header_errors": header_errors,
            "invalid_headers": invalid_headers,
            "missing_required_headers": missing_required_headers,
            "missing_required_data": missing_required_data,
            "data_errors": data_errors,
            "invalid_data": invalid_data,
            "duplicates": duplicates,
            "duplicate_rows": duplicate_rows,
        }

    return False


def get_duplicate_assets(row, folder, existing_inspections, standard, headers):
    asset_id_header = StandardHeader.objects.filter(header__name="AssetID", standard=standard).first()
    duplicate_assets = []

    if asset_id_header:
        header_index_asset_id = list(headers).index(asset_id_header.name)

        assets = [inspection.asset for inspection in existing_inspections]
        assets = list(set(assets))

        for asset in assets:
            asset_id_value = AssetValue.objects.filter(asset=asset.uuid, standard_header=asset_id_header).first()
            if asset_id_value:
                if row[header_index_asset_id] == asset_id_value.value:
                    duplicate_assets.append(asset)
                    continue

    return duplicate_assets


def get_duplicate_inspections(row, folder, existing_inspections, standard, headers, duplicate_assets):
    inspections = []
    start_node_value = None
    finish_node_value = None

    asset_id_header = StandardHeader.objects.filter(header__name="AssetID", standard=standard).first()
    start_node_header = get_start_node_header(standard=standard)
    finish_node_header = get_finish_node_header(standard=standard)

    if not start_node_header or not finish_node_header:
        raise ValidationError("Unable to check for duplicates")

    header_index_asset_id = list(headers).index(asset_id_header.name)
    header_index_start_node = list(headers).index(start_node_header.name)
    header_index_finish_node = list(headers).index(finish_node_header.name)

    for inspection in existing_inspections:
        asset_ids = AssetValue.objects.filter(asset__in=duplicate_assets, standard_header=asset_id_header.uuid)
        if row[header_index_asset_id] in asset_ids.values_list("value", flat=True):
            inspections.append(inspection)
            continue

        start_node_value = get_start_node_value(header_uuid=start_node_header.uuid, inspection_uuid=inspection.uuid)
        finish_node_value = get_finish_node_value(header_uuid=finish_node_header.uuid, inspection_uuid=inspection.uuid)

        if start_node_value or finish_node_value:
            if (
                start_node_value.value == row[header_index_start_node]
                and finish_node_value.value == row[header_index_finish_node]
            ):
                inspections.append(inspection)
                continue
            elif start_node_value.value == row[header_index_start_node] and row[header_index_finish_node] == "":
                inspections.append(inspection)
                continue
            elif finish_node_value.value == row[header_index_finish_node] and row[header_index_start_node] == "":
                inspections.append(inspection)
                continue

    return inspections


def get_start_node_header(standard: str) -> StandardHeader:
    start_node_header = StandardHeader.objects.filter(header__name="StartNodeRef", standard=standard).first()
    if not start_node_header:
        start_node_header = StandardHeader.objects.filter(header__name="UpstreamNode", standard=standard).first()

    return start_node_header


def get_finish_node_header(standard):
    finish_node_header = StandardHeader.objects.filter(header__name="FinishNodeRef", standard=standard).first()
    if not finish_node_header:
        finish_node_header = StandardHeader.objects.filter(header__name="DownstreamNode", standard=standard).first()

    return finish_node_header


def get_start_node_value(header_uuid, inspection_uuid):
    return InspectionValue.objects.filter(inspection=inspection_uuid, standard_header=header_uuid).first()


def get_finish_node_value(header_uuid, inspection_uuid):
    return InspectionValue.objects.filter(inspection=inspection_uuid, standard_header=header_uuid).first()


def _build_inspection_name(insp: Inspection) -> str:
    """
    Build a display name for the inspection using asset details

    This is a port of the logic that was previously used for creating the MPL 'name' field in the processing function.
    """

    # Note: Circular import between this file, pydantic_models.inspection_model and serializers.inspection_serializers
    from api.inspections.pydantic_models.inspection_model import get_inspection_representation

    if not insp.asset:
        return "Location Unknown"

    insp_repr = get_inspection_representation(insp)
    asset_repr = insp_repr.asset
    if not asset_repr.location_street and not asset_repr.location_town:
        return "Location Unknown"

    country = insp.asset.organisation.country.code
    parts = (p for p in (asset_repr.location_street, asset_repr.location_town, country) if p)

    return ", ".join(parts)[:200]


def validate_inspection_details(inspection: Inspection) -> tuple[bool, dict]:
    validation = {
        "is_valid": True,
        "inspection_id": inspection.legacy_id,
        "inspection_name": _build_inspection_name(inspection),
        "errors": [],
    }
    standard: BaseStandard = get_standard_instance(inspection.file.job_tree.standard_key.name)

    # --- INSPECTION VALIDATION ---
    # --- Common inspection validation checks ---
    if inspection is not None:
        validation["errors"].extend(standard.validate_inspection(inspection))
        # --- ASSET VALIDATION ---
        asset = inspection.asset
        asset_values = AssetValue.objects.filter(asset=asset)
        validation["errors"].extend(standard.validate_asset(asset=asset, asset_values=asset_values))

    # --- DEFECT VALIDATION ---
    frames = (
        VideoFrames.objects.prefetch_related("parent_video", "defect_model", "defect_scores")
        .filter(
            parent_video=inspection.file,
            is_hidden=False,
            defect_scores__isnull=False,
            defect_scores__is_shown=True,
        )
        .order_by("frame_id")
    )

    if frames:
        validation["errors"].extend(standard.validate_frames(frames=frames))

    is_valid = len(validation["errors"]) == 0
    validation["is_valid"] = is_valid
    return is_valid, validation


def validate_standard_value(inspection_value: InspectionValue) -> List[CustomValidationError]:
    standard: BaseStandard = get_standard_instance(inspection_value.standard_header.standard.name)
    error = standard.validate_standard_value(inspection_value)
    errors = [error] if error else []
    return errors


def validate_inspections_for_export(inspections: Iterable[Inspection]):
    response = {"passed": True, "results": []}

    invalid_count = 0
    for inspection in inspections:
        is_valid, validation = validate_inspection_details(inspection)
        if not is_valid:
            invalid_count += 1
        response["results"].append(validation)

    response["results"].sort(key=lambda x: x["is_valid"])
    response["passed"] = invalid_count == 0

    return response


def get_inspections_for_validation(inspection_uuids: list[str]) -> Iterable[Inspection]:
    """
    Creates an appropriate QuerySet from a list of inspections uuids to validate.

    :param inspection_uuids: The list of inspection ids to fetch.
    :return: The Inspection QuerySet
    """
    inspections_qs = (
        Inspection.objects.filter(uuid__in=inspection_uuids)
        .exclude(file__hidden=True)
        .select_related("file")
        .select_related("file__job_tree")
        .select_related("file__job_tree__standard_key")
        .select_related("file__upload_org")
        .select_related("folder")
        .select_related("folder__standard_key")
        .select_related("created_by")
        .select_related("asset")
    )
    return inspections_qs
