import logging
from datetime import datetime
from typing import Any
from uuid import UUID

from django.db.models import Count, F, Max
from django.utils import timezone
from django.conf import settings
from django.core.cache import caches
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

import jsonschema

from api.base.models import Header
from api.base.serializers import SewerDataField
from api.actions.models import AuditList
from api.common.storage import get_platform_blob_url_with_sas, get_platform_storage_sas_token
from api.inspections.models import (
    FileList,
    JobsTree,
    MapPointList,
    ProcessingList,
    VideoFrames,
    ImportedInspectionFile,
    Inspection,
    InspectionValue,
    InspectionFilter,
)
from api.inspections.serializers.mixins import HeaderMixin, StandardHeaderMixin
from api.inspections.utilities.validate_inspections import validate_standard_value
from api.organisations.models import Organisations
from api.defects.models import Standard, StandardHeader
from vapar.constants.conversion import StandardValueConverter

from api.users.models import CustomUser

log = logging.getLogger(__name__)
sas_cache = caches["default"]
inspection_cache = caches["inspections"]


class InspectionListSerializer(serializers.ModelSerializer):
    created_at = serializers.SerializerMethodField(read_only=True)
    file_size = serializers.SerializerMethodField(read_only=True)
    job_name = serializers.SerializerMethodField(read_only=True)
    uploaded_time = serializers.SerializerMethodField(read_only=True)
    video_id = serializers.SerializerMethodField(read_only=True)
    video_job_id = serializers.SerializerMethodField(read_only=True)
    video_name = serializers.SerializerMethodField(read_only=True)
    video_url = serializers.SerializerMethodField(read_only=True)
    video_user = serializers.SerializerMethodField(read_only=True)
    water_level_url = serializers.SerializerMethodField(read_only=True)
    view_disabled = serializers.SerializerMethodField(read_only=True)
    to_be_matched = serializers.SerializerMethodField(read_only=True)

    inspection_notes = serializers.CharField(max_length=1000, read_only=True)
    name = serializers.CharField(max_length=200, read_only=True)

    def get_job_name(self, data) -> str | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.job_name
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.job_name

        return None

    def get_file_size(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.file_size
        return None

    def get_video_id(self, data) -> int | None:
        if data.associated_file:
            return data.associated_file.id
        return None

    def get_video_job_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.id
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.id

        return None

    def get_video_name(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.filename
        return None

    def get_video_url(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.url
        return None

    def get_video_user(self, data) -> str | None:
        if data.associated_file and data.associated_file.upload_user:
            return data.associated_file.upload_user
        return None

    def get_uploaded_time(self, data) -> datetime | None:
        if data.associated_file:
            return data.associated_file.created_time
        return None

    def get_water_level_url(self, data) -> str:
        return get_platform_blob_url_with_sas(data.water_level_url)

    def get_created_at(self, data) -> datetime | None:
        if data.associated_file:
            return data.associated_file.created_time
        return None

    def get_view_disabled(self, data) -> dict[str, Any]:
        request = self.context["request"]
        is_asset_owner = request.organisation.is_asset_owner

        if data.associated_file and data.associated_file.uploaded_by:
            uploaded_by_contractor = data.associated_file.uploaded_by.organisation.org_type == "Contractor"

            if is_asset_owner and uploaded_by_contractor and data.status == "Uploaded":
                return {
                    "disabled": True,
                    "message": "A contractor uploaded inspection with the status of Uploaded cannot be viewed by an asset owner. Waiting for contractor review.",
                }
        else:
            return {
                "disabled": True,
                "message": "This inspection has no associated video file",
            }

        return {"disabled": False, "message": ""}

    def get_to_be_matched(self, data) -> bool:
        if not data.inspection:
            matching_count = self.context["matching_count"]
            found = matching_count.get(data.associated_file.job_tree.id, 0)

            return found > 0

        if data.status == MapPointList.PLANNED_STATUS:
            return False

        return False

    class Meta:
        model = MapPointList
        exclude = [
            "geometry",
            "first_frame",
            "associated_file",
            "job",
            "standard_key",
            "process_model_id",
            "cr_model_id",
            "ser_cr_model_id",
            "pipe_type",
            "start_node",
            "end_node",
        ]


class InspectionDetailSerializer(serializers.ModelSerializer):
    folder = serializers.SerializerMethodField(read_only=True)
    folder_name = serializers.SerializerMethodField(read_only=True)
    target_org_id = serializers.SerializerMethodField(read_only=True)
    upload_org_id = serializers.SerializerMethodField(read_only=True)

    def get_target_org_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.target_org_id:
            return data.associated_file.target_org_id
        return None

    def get_upload_org_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.upload_org_id:
            return data.associated_file.upload_org_id
        return None

    def get_folder(self, data) -> int | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.id
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.id
        return None

    def get_folder_name(self, data) -> str | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.job_name
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.job_name
        return None

    class Meta:
        model = MapPointList
        fields = [
            "chainage",
            "asset_id",
            "name",
            "material",
            "diameter",
            "upstream_node",
            "downstream_node",
            "direction",
            "folder",
            "folder_name",
            "status",
            "date_captured",
            "standard_key",
            "id",
            "condition_rating",
            "service_condition_rating",
            "inspection_notes",
            "inspection",
            "target_org_id",
            "upload_org_id",
        ]

    def validate(self, data):
        if data.get("chainage_number", 0) > settings.MAX_CHAINAGE:
            raise serializers.ValidationError(f"Chainage cannot be greater than {settings.MAX_CHAINAGE}")

        return data


class InspectionSerializer(serializers.ModelSerializer):
    def _ensure_string(self, val: str | int | bool | None) -> str:
        value = val if val is not None else ""
        return str(value)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if self.instance:  # Update
            if self.instance.footage_id is not None and "footage" in attrs:
                raise serializers.ValidationError(
                    "Cannot change footage_id of an existing inspection if it is already set"
                )

        if footage := attrs.get("footage"):
            request_org = self.context["request"].organisation
            if footage.target_org != request_org and not request_org.is_contractor_for_org(footage.target_org):
                raise PermissionDenied(
                    "You do not have permission to create or update an inspection with this footage."
                )

        return attrs

    def update(self, instance: Inspection, validated_data: dict):
        use_header_names = self.context["request"].query_params.get("use_header_names", "false").lower() == "true"
        inspection = super().update(instance, validated_data)
        data = self.context["request"].data
        inspection_values_keys = set(data).difference(validated_data)

        if use_header_names and not inspection.asset:
            raise serializers.ValidationError("Cannot update Inspections without Assets")

        for key in inspection_values_keys:
            if use_header_names:
                standard_header = StandardHeader.objects.filter(
                    header__name=key, standard=inspection.asset.standard, header__type=Header.HeaderType.INSPECTION
                ).first()
                if not standard_header:
                    log.warning(f"Header ({key}) not found for standard. Ignoring.", extra={"header_name": key})
                    continue
                inspection_value, _ = InspectionValue.objects.get_or_create(
                    inspection_id=inspection.uuid,
                    standard_header__header__name=key,
                    defaults={"value": "", "standard_header": standard_header},
                )
            else:
                inspection_value = InspectionValue.objects.filter(
                    inspection_id=inspection.uuid, standard_header__header__mapped_mpl_field=key
                ).first()
                if not inspection_value:
                    if not inspection.asset:
                        raise serializers.ValidationError("Cannot update Inspections without Assets")
                    try:
                        standard_header = StandardHeader.objects.get(
                            header__mapped_mpl_field=key, standard_id=inspection.asset.standard
                        )
                    except StandardHeader.DoesNotExist:
                        raise serializers.ValidationError(f"{key} is an invalid standard header")
                    inspection_value = inspection.create_inspection_value(
                        standard_header=standard_header, value=data.get(key)
                    )

            serializer = InspectionValueSerializer(
                instance=inspection_value,
                data={"value": self._ensure_string(data.get(key))},
                partial=True,
                context={"standard_header": inspection_value.standard_header},
            )

            if serializer.is_valid(raise_exception=True):
                serializer.save()

            validation_errors = validate_standard_value(serializer.instance)

            if len(validation_errors) > 0:
                raise serializers.ValidationError(validation_errors)

        return inspection

    class Meta:
        model = Inspection
        fields = ["asset", "folder", "status", "legacy_id", "footage"]
        read_only_fields = ["uuid"]


class InspectionValueSerializer(StandardHeaderMixin, serializers.ModelSerializer):
    inspection = serializers.PrimaryKeyRelatedField(read_only=True)
    # Field was removed from model
    # imported_inspection_file = serializers.PrimaryKeyRelatedField(read_only=True)
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)
    value = serializers.CharField(allow_blank=True)

    def get_header_uuid(self, data) -> UUID:
        return data.standard_header.header.uuid

    def get_header_name(self, data) -> str:
        return data.standard_header.header.name

    def to_internal_value(self, data):
        standard_header = self.context["standard_header"]

        if standard_header.header.name in ("Direction", "SetupLocation"):
            converter = StandardValueConverter(header="Direction", standard=standard_header.standard.display_name)
            converted_value = converter.get_standard_value(value=data["value"])
            try:
                data["value"] = converted_value.value
            except AttributeError:
                data["value"] = converted_value
        return super().to_internal_value(data)

    class Meta:
        model = InspectionValue
        fields = [
            "uuid",
            "value",
            "standard_header",
            "standard_header_name",
            "inspection",
            # "imported_inspection_file",
            "header_uuid",
            "header_name",
        ]


class InspectionValueListSerializer(StandardHeaderMixin, HeaderMixin, serializers.ModelSerializer):
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)
    standard_id = serializers.SerializerMethodField(read_only=True)
    is_editable = serializers.SerializerMethodField(read_only=True)

    def get_standard_id(self, data):
        return data.standard_header.standard.id

    def get_header_uuid(self, data):
        return data.standard_header.header.uuid

    def get_header_name(self, data):
        return data.standard_header.header.name

    def get_is_editable(self, data):
        return data.standard_header.header.is_editable

    class Meta:
        model = InspectionValue
        fields = [
            "uuid",
            "value",
            "standard_id",
            "standard_header",
            "standard_header_name",
            "header_uuid",
            "header_name",
            "is_editable",
        ]


class UploadedByUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ["id", "first_name", "last_name"]


class FramesListSerializer(serializers.ModelSerializer):
    defect_score_severity = serializers.SerializerMethodField(read_only=True)
    defect_id = serializers.SerializerMethodField(read_only=True)
    defect_class = serializers.SerializerMethodField(read_only=True)
    defect_code = serializers.SerializerMethodField(read_only=True)
    defect_str_score = serializers.SerializerMethodField(read_only=True)
    defect_ser_score = serializers.SerializerMethodField(read_only=True)
    pipe_type_sewer = serializers.SerializerMethodField(read_only=True)
    material = serializers.SerializerMethodField(read_only=True)
    inspection_id = serializers.SerializerMethodField(read_only=True)
    image_url = serializers.SerializerMethodField(read_only=True)
    defect_score_is_shown = serializers.SerializerMethodField(read_only=True)
    chainage_number = serializers.DecimalField(required=True, allow_null=False, max_digits=6, decimal_places=2)

    def get_defect_score_is_shown(self, data) -> bool | None:
        if data.defect_scores:
            return data.defect_scores.is_shown
        return None

    def get_defect_id(self, data) -> int | None:
        if data.defect_scores:
            return data.defect_scores.id
        return None

    def get_defect_class(self, data) -> str | None:
        if data.defect_scores:
            return data.defect_scores.defect_description
        return None

    def get_defect_code(self, data) -> str | None:
        if data.defect_scores:
            return data.defect_scores.defect_code
        return None

    def get_defect_str_score(self, data) -> str | None:
        if data.defect_scores:
            return data.defect_scores.structural_score
        return None

    def get_defect_ser_score(self, data) -> str | None:
        if data.defect_scores:
            return data.defect_scores.service_score
        return None

    def get_pipe_type_sewer(self, data) -> bool | None:
        if data.parent_video and hasattr(data.parent_video, "mappointlist"):
            return data.parent_video.mappointlist.sewer_data
        return None

    def get_material(self, data) -> str | None:
        if data.parent_video and hasattr(data.parent_video, "mappointlist"):
            return data.parent_video.mappointlist.material
        return None

    def get_inspection_id(self, data) -> int | None:
        if data.parent_video and hasattr(data.parent_video, "mappointlist"):
            return data.parent_video.mappointlist.id
        return None

    def get_defect_score_severity(self, data) -> str | None:
        if not data.defect_scores:
            return None
        return data.defect_scores.defect_score_severity

    def get_image_url(self, data: VideoFrames) -> str:
        image_loc = data.image_location if data.image_location else settings.FALLBACK_FRAME_IMAGE_URL
        if not (sas_url := self.context.get("sas_url")):
            sas_url = get_platform_storage_sas_token(
                container_name=settings.BLOB_STORAGE_FRAMES_CONTAINER, region=data.parent_video.storage_region
            )
        # The VideoFrameList fetch pre-fetches the parent video. A single VideoFrame fetch of the parent is cheap.
        return get_platform_blob_url_with_sas(
            blob_path=image_loc, sas_token=sas_url, region=data.parent_video.storage_region
        )

    def validate_chainage(self, data) -> float:
        if float(data) > settings.MAX_CHAINAGE:
            raise serializers.ValidationError(f"Chainage cannot be greater than {settings.MAX_CHAINAGE}")
        return float(data)

    class Meta:
        model = VideoFrames
        fields = (
            "id",
            "inspection_id",
            "image_location",
            "image_url",
            "frame_id",
            "class_label",
            "class_certainty",
            "chainage",
            "chainage_number",
            "is_hidden",
            "is_accepted",
            "all_class_breakdown",
            "at_joint",
            "at_clock",
            "to_clock",
            "cont_defect_start",
            "cont_defect_end",
            "quantity1_value",
            "quantity1_units",
            "quantity2_value",
            "quantity2_units",
            "remarks",
            "is_matched",
            "parent_video",
            "pipe_type_sewer",
            "material",
            "defect_id",
            "defect_class",
            "defect_code",
            "defect_str_score",
            "defect_ser_score",
            "defect_score_severity",
            "defect_score_is_shown",
            "time_reference",
        )


class FrameExtendedEditSerializer(FramesListSerializer):
    """
    Serializer for editing frames, with additional fields accessible
    """

    id = serializers.IntegerField()

    class Meta(FramesListSerializer.Meta):
        fields = [*FramesListSerializer.Meta.fields, "image_location"]


class VideoFrameCreateSerializer(serializers.ModelSerializer):
    chainage_number = serializers.DecimalField(required=True, allow_null=False, max_digits=6, decimal_places=2)
    time_reference = serializers.RegexField(
        regex=r"^\d{1,2}:\d{2}:\d{2}(\.\d{1,3})?$",
        error_messages={"invalid": 'Invalid time_reference format. It should be in the format "HH:MM:SS.S".'},
        required=False,
    )

    def to_internal_value(self, data):
        # The below is required due to CamelCaseJSONParser placing an underscore before the number
        data.setdefault("quantity1_value", data.get("quantity_1_value"))
        data.setdefault("quantity1_units", data.get("quantity_1_units"))
        data.setdefault("quantity2_value", data.get("quantity_2_value"))
        data.setdefault("quantity2_units", data.get("quantity_2_units"))
        return super().to_internal_value(data)

    def validate(self, data: dict) -> dict:
        data = super().validate(data)
        if self.partial and isinstance(self.initial_data, list):
            if any("id" not in item for item in self.initial_data):
                raise serializers.ValidationError("Id must be provided for each frame when updating")
        return data

    class Meta:
        model = VideoFrames
        fields = (
            "id",
            "parent_video",
            "image_location",
            "frame_id",
            "time_reference",
            "chainage",
            "chainage_number",
            "class_label",
            "class_certainty",
            "duplicate_of",
            "is_hidden",
            "cont_defect_start",
            "cont_defect_end",
            "at_joint",
            "quantity1_value",
            "quantity1_units",
            "quantity2_value",
            "quantity2_units",
            "at_clock",
            "to_clock",
            "defect_model",
            "defect_scores",
            "all_class_breakdown",
            "is_accepted",
            "remarks",
        )


class FolderSerializer(serializers.ModelSerializer):
    parent_job = serializers.PrimaryKeyRelatedField(queryset=JobsTree.objects.all(), write_only=True)
    pipe_type_sewer = serializers.BooleanField(required=True)
    standard_key = serializers.PrimaryKeyRelatedField(queryset=Standard.objects.all(), required=True)
    selected_contractor = serializers.PrimaryKeyRelatedField(queryset=Organisations.objects.all(), required=False)
    destination_folder = serializers.PrimaryKeyRelatedField(
        queryset=JobsTree.objects.all(), write_only=True, required=False
    )

    class Meta:
        model = JobsTree
        fields = [
            "primary_org",
            "secondary_org",
            "job_name",
            "created_date",
            "pipe_type_sewer",
            "standard_key",
            "parent_job",
            "selected_contractor",
            "destination_folder",
        ]

    def create(self, validated_data):
        # [ === V2 logic === ]
        is_asset_owner = self.context["request"].organisation.is_asset_owner

        if is_asset_owner:
            if validated_data.get("selected_contractor", None):
                new_job = validated_data["parent_job"].add_child(
                    job_name=validated_data["job_name"],
                    created_date=timezone.now(),
                    pipe_type_sewer=validated_data["pipe_type_sewer"],
                    standard_key=validated_data["standard_key"],
                    secondary_org=validated_data["selected_contractor"],
                )
            else:
                new_job = validated_data["parent_job"].add_child(
                    job_name=validated_data["job_name"],
                    created_date=timezone.now(),
                    pipe_type_sewer=validated_data["pipe_type_sewer"],
                    standard_key=validated_data["standard_key"],
                )

            files = FileList.objects.filter(job_tree=validated_data["parent_job"])
            if len(files) > 0:
                # move files to this new job
                for file_ in files:
                    file_.job_tree = new_job
                FileList.objects.bulk_update(files, ["job_tree"])
        else:
            # Can create sub jobs below this top level job
            new_job = validated_data["parent_job"].add_child(
                job_name=validated_data["job_name"],
                created_date=timezone.now(),
                pipe_type_sewer=validated_data["pipe_type_sewer"],
                standard_key=validated_data["standard_key"],
            )

            files = FileList.objects.filter(job_tree=validated_data["parent_job"])
            if len(files) > 0:
                # move files to this new job
                for file_ in files:
                    file_.job_tree = new_job
                FileList.objects.bulk_update(files, ["job_tree"])

        # Update folders and invalidate cache for relevant inspections
        inspections = Inspection.objects.filter(file__id__in=[f.id for f in files])
        for i in inspections:
            i.folder = new_job
            i.save()
        inspection_cache.delete_many([str(i.uuid) for i in inspections])

        return new_job

    def update(self, instance, validated_data):
        if "job_name" in validated_data:
            instance.job_name = validated_data["job_name"]
            instance.save()
        if "destination_folder" in validated_data:
            # if the job name already exists in this folder, append a 1 to the job name
            job_name = instance.job_name
            target_job = validated_data["destination_folder"]
            while target_job.get_children().filter(job_name=job_name).exists():
                job_name = job_name + " (1)"
            if job_name != instance.job_name:
                instance.job_name = job_name
                instance.save()
            instance.move(target_job, pos="last-child")

            description = (
                "Move a job "
                + JobsTree.objects.get(id=instance.id).job_name
                + " to "
                + validated_data["destination_folder"].job_name
            )
            now = timezone.now()
            AuditList.objects.create(
                event_type="Update",
                table="JobTree",
                row_id=instance.id,
                column="Multi",
                description=description,
                date_of_modification=now,
                user=self.context["request"].user,
            )

        # Invalidate cache for inspections linked to this folder
        inspections = Inspection.objects.filter(folder=instance)
        inspection_cache.delete_many([str(i.uuid) for i in inspections])

        return instance

    def validate(self, data):
        if self.context["request"].method == "POST":
            # [ === V2 logic === ]
            is_asset_owner = self.context["request"].organisation.is_asset_owner

            if is_asset_owner:
                # Asset owners can create jobs&sub-jobs for contractors
                # First find the node by parent id and then check the root node if it belongs to the user's org & it was the leaf node
                # If it belongs to user's org, we will then check if this job contains any files, then we will create a new child folder
                root_job = data["parent_job"].get_root()
                org = self.context["request"].organisation
                if root_job.primary_org == org:
                    if data.get("selected_contractor", None):
                        # creating top level jobs for contractor
                        secondary_org = data["selected_contractor"]
                        if data["parent_job"].secondary_org == secondary_org:
                            raise serializers.ValidationError("The parent job is already a top level contractor job")

                        if (
                            secondary_org.contractors not in org.assetowners.contractor.all()
                            or data["parent_job"].get_ancestors().filter(secondary_org=secondary_org).count() != 0
                            or data["parent_job"].get_depth() != 1
                        ):
                            # if request contractor is one of asset owner's contractor and the ancestor nodes have not been linked to any contractors, we can create a top level folder for contractor
                            raise serializers.ValidationError(
                                "Invalid contractor. or the ancestors already have a top level job"
                            )
                else:
                    raise serializers.ValidationError("You are not allowed to create job for this org")
            else:
                # Contractors can only create sub-jobs from the highest level job that the asset owner created for them or their own jobs
                org = self.context["request"].organisation
                root_job = data["parent_job"].get_root()
                ancestor_jobs = data["parent_job"].get_ancestors().filter(secondary_org=org).count()
                if not (ancestor_jobs > 0 or data["parent_job"].secondary_org == org or root_job.primary_org == org):
                    raise serializers.ValidationError("You are not allowed to create job for this org")

            # Validate job_name
            if data["parent_job"].get_children().filter(job_name=data["job_name"]).exists():
                raise serializers.ValidationError("Folder name exists")

            if data["parent_job"].get_depth() == 5:
                raise serializers.ValidationError("Maximum folder depth reached")

        elif self.context["request"].method == "PATCH":
            request = self.context["request"]

            # [ === V2 logic === ]

            # User preconditions check (these should be custom permission classes in future)
            is_asset_owner = request.organisation.is_asset_owner

            # Set common vars
            current_job = self.instance

            # RENAME FOLDER
            if "job_name" in data:
                job_name = data["job_name"]

                # Validation + resource specific permission checks
                if current_job.get_siblings().filter(job_name=job_name).exists() and job_name != current_job.job_name:
                    raise serializers.ValidationError("Job name already in use, please choose another name.")

                current_level = current_job.get_depth()
                if current_job.secondary_org or current_level == 1:
                    raise serializers.ValidationError("Cannot modify the top level job")

                current_root = current_job.get_root()
                org = request.organisation
                if is_asset_owner:
                    if current_root.primary_org != org:
                        raise serializers.ValidationError("You do not have permission to rename this folder")
                else:
                    contractor_job_top_level = current_job.get_ancestors().filter(secondary_org=org).first()
                    if not (current_root.primary_org == org or contractor_job_top_level):
                        # here the reason we check root instead of depth = 2 is that contractors do not have top level folders
                        raise serializers.ValidationError("You do not have permission to rename this folder")

            # MOVE FOLDER
            if "destination_folder" in data:
                # Cannot move a job into the 4th(5th in database) level job(because it will become 5th) - total depth must be less than 5
                depth_below_current = 0
                if current_job.get_descendants().exists():
                    max_depth_in_current = current_job.get_descendants().aggregate(Max("depth"))["depth__max"]
                    depth_below_current = max_depth_in_current - current_job.get_depth()
                final_depth = data["destination_folder"].get_depth() + depth_below_current + 1  # 1 is for current job
                if final_depth > 5:
                    raise serializers.ValidationError("Maximum level of jobs reached")
                elif current_job.secondary_org is not None:
                    raise serializers.ValidationError("Cannot move top level jobs for a contractor")

                # Anyone can move jobs but cannot move the job into the lowest level job if the lowest level job has file.
                file_exsited = FileList.objects.filter(job_tree=data["destination_folder"]).exists()
                if file_exsited:
                    raise serializers.ValidationError("Target job contains files. Please move these files first")

                if is_asset_owner:
                    # Assetowners can move sub jobs for contractors but only within the top level of that contractor.
                    # so we first check if current job is under a contractor's job
                    current_level = current_job.get_depth()
                    contractor_job = current_job.get_ancestors().filter(secondary_org__isnull=False).first()
                    if (current_level == 2 and contractor_job) or current_level == 1:
                        raise serializers.ValidationError("Cannot move the top level job")

                    current_root = current_job.get_root()
                    org = request.organisation
                    if current_root.primary_org == org:
                        # now the current job belongs to this user's org then check if this job belongs to contractor also
                        if contractor_job:
                            # check if target job belong to the same org
                            is_target_contractor_job = (
                                data["destination_folder"]
                                .get_ancestors()
                                .filter(secondary_org=contractor_job.secondary_org)
                                .exists()
                            )
                            if not (
                                is_target_contractor_job
                                or data["destination_folder"].secondary_org == contractor_job.secondary_org
                            ):
                                raise serializers.ValidationError("Cannot move the job outside of this top level job")
                        elif (
                            data["destination_folder"].get_ancestors().filter(secondary_org__isnull=False).exists()
                            or data["destination_folder"].secondary_org
                        ):  # current is not a contractor job but target is
                            raise serializers.ValidationError("Cannot move this job into a contractor job")
                    else:
                        raise serializers.ValidationError("You do not have permission to move this job")
                else:
                    # contractor can move themselves jobs and the jobs asset owner created for them but only within top level
                    org = request.organisation
                    if current_job.secondary_org == org:
                        raise serializers.ValidationError("Cannot move the top level job")

                    current_root = current_job.get_root()

                    contractor_job_top_level = current_job.get_ancestors().filter(secondary_org=org).first()
                    if current_root.primary_org == org:
                        # here the reason we check root instead of depth = 2 is that contractors do not have top level folders
                        target_root = data["destination_folder"].get_root()
                        if current_root == target_root or current_root == data["destination_folder"]:
                            pass
                        else:
                            raise serializers.ValidationError("Cannot move the job outside of this top level job")
                    elif contractor_job_top_level:
                        # if it's a contractor job created by asset owner, we can only move the job within this
                        if not (
                            data["destination_folder"].get_ancestors().filter(secondary_org=org).first()
                            == contractor_job_top_level
                            or data["destination_folder"] == contractor_job_top_level
                        ):
                            raise serializers.ValidationError("Cannot move the job outside of this top level job")
                    else:
                        raise serializers.ValidationError("You do not have permission to move this job")

            # [ ============= ]

        elif self.context["request"].method == "DELETE":
            request = self.context["request"]

            # [ === V2 logic === ]

            # User preconditions check (these should be custom permission classes in future)
            is_asset_owner = request.organisation.is_asset_owner

            # Set common vars
            current_job = self.instance
            current_level = current_job.get_depth()

            if current_level == 1:
                raise serializers.ValidationError("Cannot delete the top level job")

            if is_asset_owner:
                contractor_job = current_job.get_ancestors().filter(secondary_org__isnull=False).first()

                if current_job.secondary_org:
                    raise serializers.ValidationError("Cannot delete the top level job")

                current_root = current_job.get_root()
                org = request.organisation
                if current_root.primary_org == org:
                    if current_job.is_leaf():
                        files_exist = (
                            FileList.objects.filter(job_tree=current_job)
                            .alias(mpl_exists=Count(F("mappointlist")))
                            .filter(mpl_exists__gt=0)
                            .exclude(hidden=True)
                            .exists()
                        )
                    else:
                        children_jobs = current_job.get_descendants()
                        files_exist = (
                            FileList.objects.filter(job_tree__in=children_jobs)
                            .alias(mpl_exists=Count(F("mappointlist")))
                            .filter(mpl_exists__gt=0)
                            .exclude(hidden=True)
                            .exists()
                        )

                    if files_exist:
                        raise serializers.ValidationError(
                            "Cannot delete a folder containing files (or with child folders that contain files)"
                        )
                else:
                    raise serializers.ValidationError("You do not have permission to delete")

            else:
                org = request.organisation
                current_level = current_job.get_depth()

                if current_job.secondary_org == org:
                    raise serializers.ValidationError("Cannot delete the top level job")

                current_root = current_job.get_root()
                contractor_job_top_level = current_job.get_ancestors().filter(secondary_org=org).first()

                if current_root.primary_org == org or contractor_job_top_level:
                    # here the reason we check root instead of depth = 2 is that contractors do not have top level folders
                    if current_job.is_leaf():
                        files_exist = (
                            FileList.objects.filter(job_tree=current_job)
                            .alias(mpl_exists=Count(F("mappointlist")))
                            .filter(mpl_exists__gt=0)
                            .exclude(hidden=True)
                            .exists()
                        )
                    else:
                        children_jobs = current_job.get_descendants()
                        files_exist = (
                            FileList.objects.filter(job_tree__in=children_jobs)
                            .alias(mpl_exists=Count(F("mappointlist")))
                            .filter(mpl_exists__gt=0)
                            .exclude(hidden=True)
                            .exists()
                        )

                    if files_exist:
                        raise serializers.ValidationError(
                            "Cannot delete a folder containing files (or with child folders that contain files)"
                        )
                else:
                    raise serializers.ValidationError("You do not have permission to delete")

                # [ ============= ]

        return data


class FramesEditSerializer(serializers.ModelSerializer):
    # units = ["-", "%", "m", "mm", "in", "ft", "°", "S", "M", "L"]
    chainage_number = serializers.FloatField(required=True, allow_null=False)
    to_clock = serializers.IntegerField(required=False, min_value=1, max_value=12, allow_null=True)
    at_clock = serializers.IntegerField(required=False, min_value=1, max_value=12, allow_null=True)
    quantity1_value = serializers.FloatField(required=False, allow_null=True)
    quantity2_value = serializers.FloatField(required=False, allow_null=True)
    quantity1_units = serializers.CharField(required=False)
    quantity2_units = serializers.CharField(required=False)
    defect_score_severity = serializers.SerializerMethodField(read_only=True)
    defect_score_is_shown = serializers.SerializerMethodField(read_only=True)

    def get_defect_score_severity(self, data) -> str | None:
        if not data.defect_scores:
            return None
        return data.defect_scores.defect_score_severity

    def get_defect_score_is_shown(self, data) -> bool | None:
        if data.defect_scores:
            return data.defect_scores.is_shown
        return None

    class Meta:
        model = VideoFrames
        fields = [
            "is_hidden",
            "quantity1_units",
            "quantity1_value",
            "quantity2_units",
            "quantity2_value",
            "at_clock",
            "to_clock",
            "chainage_number",
            "chainage",
            "at_joint",
            "remarks",
            "defect_score_severity",
            "defect_score_is_shown",
            "cont_defect_start",
            "cont_defect_end",
        ]

    def validate_chainage(self, data):
        if float(data) > settings.MAX_CHAINAGE:
            raise serializers.ValidationError(f"Chainage cannot be greater than {settings.MAX_CHAINAGE}")
        return float(data)


class FileSerializer(serializers.ModelSerializer):
    standard_key = serializers.SerializerMethodField(read_only=True)
    download_url = serializers.SerializerMethodField(read_only=True)
    play_url = serializers.SerializerMethodField(read_only=True)
    sewer_data = SewerDataField(source="job_tree.pipe_type_sewer", read_only=True, default=True)

    def _get_sas_token(self, file_id, region, download=False) -> str:
        access = "download" if download else "play"
        cache_key = f"{file_id}:{settings.BLOB_STORAGE_VIDEOS_CONTAINER}:{access}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(
                region=region,
                container_name=settings.BLOB_STORAGE_VIDEOS_CONTAINER,
                download=download,
            )
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)
        return sas_token

    def get_download_url(self, data: FileList) -> str | None:
        if not data.url:
            return None

        sas_token = self._get_sas_token(data.id, data.storage_region, download=True)
        return get_platform_blob_url_with_sas(region=data.storage_region, blob_path=data.url, sas_token=sas_token)

    def get_play_url(self, data: FileList) -> str | None:
        if not data.play_url:
            return None

        sas_token = self._get_sas_token(data.id, data.storage_region)
        return get_platform_blob_url_with_sas(region=data.storage_region, blob_path=data.play_url, sas_token=sas_token)

    def get_standard_key(self, data: FileList) -> int | None:
        if data.job_tree:
            return data.job_tree.standard_key_id
        return None

    class Meta:
        model = FileList
        fields = [
            "id",
            "job_id",
            "filename",
            "file_type",
            "file_size",
            "url",
            "target_org",
            "upload_org",
            "upload_user",
            "job_tree",
            "uploaded_by",
            "upload_completed_time",
            "upload_completed",
            "created_time",
            "hidden",
            "updated_at",
            "standard_key",
            "storage_region",
            "sewer_data",
            "download_url",
            "play_url",
        ]


class FileCreateSerializer(FileSerializer):
    """
    Serializer for creating standalone file records, for uploading at a later time.
    Same fields as the regular file serializer, with restrictions on which fields can be written to.
    """

    def validate(self, attrs):
        attrs = super().validate(attrs)

        folder = attrs.get("job_tree")
        if not folder:
            raise serializers.ValidationError("jobTree is required")

        upload_org = self.context["request"].organisation
        if not folder.can_contain_inspections:
            raise serializers.ValidationError("Cannot upload inspections or file media to this folder")
        if not folder.allows_uploads_from(upload_org):
            raise serializers.ValidationError("Your organisation does not have permission to upload to this folder")

        return attrs

    def create(self, validated_data):
        folder = validated_data["job_tree"]
        target_org = folder.get_root().primary_org
        upload_org = self.context["request"].organisation
        # Sets file name related fields if available
        filename = validated_data.get("filename", "")
        file_url = f"{settings.BLOB_STORAGE_VIDEOS_CONTAINER}/{filename}" if filename else ""

        obj = FileList.objects.create(
            job_tree=folder,
            filename=filename,
            url=file_url,
            upload_org=upload_org,
            target_org=target_org,
            storage_region=target_org.country.code,
        )
        return obj

    class Meta(FileSerializer.Meta):
        read_only_fields = [
            "id",
            "created_time",
            "updated_at",
            "upload_completed_time",
            "upload_completed",
            "file_size",
            "file_type",
            "url",
            "hidden",
            "upload_user",
            "upload_org",
            "target_org",
            "uploaded_by",
            "storage_region",
        ]


class ProcessingInspectionsListSerializer(serializers.ModelSerializer):
    file_list_id = serializers.SerializerMethodField(read_only=True)
    url = serializers.SerializerMethodField(read_only=True)
    folder_name = serializers.SerializerMethodField(read_only=True)
    folder_id = serializers.SerializerMethodField(read_only=True)
    target_organisation_id = serializers.SerializerMethodField(read_only=True)
    upload_organisation_id = serializers.SerializerMethodField(read_only=True)
    can_view = serializers.SerializerMethodField(read_only=True)
    file_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProcessingList
        fields = [
            "id",
            "file_list_id",
            "file_name",
            "filename",
            "upload_completed",
            "file_size",
            "url",
            "folder_name",
            "folder_id",
            "status",
            "status_reason",
            "target_organisation_id",
            "upload_organisation_id",
            "can_view",
            "upload_user",
            "target_org",
            "associated_file",
            "manual_qa_required",
            "sewer_data",
            "standard_key",
            "created_time",
            "updated_at",
            "times_retried",
            "retryable_state",
            "is_retryable",
        ]

    def get_file_list_id(self, data) -> int | None:
        if data.associated_file:
            return data.associated_file.id
        return None

    def get_file_name(self, data) -> str | None:
        if data.filename:
            return data.filename
        return None

    def get_target_organisation_id(self, data) -> int | None:
        if data.target_org:
            return data.target_org.id
        return None

    def get_upload_organisation_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.uploaded_by:
            return data.associated_file.uploaded_by.organisation.id
        return None

    def get_url(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.url
        return None

    def get_folder_name(self, data) -> str | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.job_name
        return None

    def get_folder_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.id
        return None

    def get_can_view(self, data) -> bool:
        request = self.context["request"]
        is_asset_owner = request.organisation.is_asset_owner

        if data.associated_file and data.associated_file.uploaded_by:
            uploaded_by_contractor = data.associated_file.uploaded_by.organisation.is_contractor
            try:
                associated_mappointlist = MapPointList.objects.get(associated_file=data.associated_file)
            except MapPointList.DoesNotExist:
                return False

            if is_asset_owner and uploaded_by_contractor and associated_mappointlist.status == "Uploaded":
                return False
        else:
            return False

        return True


class ProcessingFileRetrySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProcessingList
        fields = ["status", "status_reason", "times_retried", "retryable_state"]


class ImportedInspectionFileSerializer(serializers.ModelSerializer):
    organisation = serializers.PrimaryKeyRelatedField(queryset=Organisations.objects.all(), required=True)
    folder = serializers.PrimaryKeyRelatedField(queryset=JobsTree.objects.all(), required=True)
    file_name = serializers.CharField(required=True)

    class Meta:
        model = ImportedInspectionFile
        fields = [
            "uuid",
            "organisation",
            "folder",
            "file_name",
            "blob_storage",
            "imported",
            "import_action",
            "created_at",
            "created_by",
        ]


class InspectionBulkUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Inspection
        fields = ["status"]


class InspectionFilterSerializer(serializers.ModelSerializer):
    class Meta:
        model = InspectionFilter
        fields = ["uuid", "organisation", "filter_model", "folder_filter_model"]

    def validate_folder_filter_model(self, data):
        try:
            jsonschema.validate(data, InspectionFilter.FOLDER_FILTER_MODEL_SCHEMA)
        except jsonschema.exceptions.ValidationError as e:
            raise serializers.ValidationError(e.message)
        return data

    def validate_filter_model(self, data):
        # try:
        #     jsonschema.validate(data, InspectionFilter.FILTER_MODEL_SCHEMA)
        # except jsonschema.exceptions.ValidationError as e:
        #     raise serializers.ValidationError(e.message)
        return data

    def create(self, validated_data):
        user = self.context["user"]
        return InspectionFilter.objects.create(**validated_data, user=user)

    def update(self, instance, validated_data):
        instance.filter_model = validated_data.get("filter_model", instance.filter_model)

        instance.folder_filter_model = validated_data.get("folder_filter_model", instance.folder_filter_model)
        instance.save()

        return instance


class InspectionFilePatchSerializer(serializers.ModelSerializer):
    file_size = serializers.RegexField(required=False, regex=r"^(0|(\d+(\.\d+)?(KB|MB|GB)))$")

    class Meta:
        model = FileList
        fields = [
            "total_frames",
            "file_type",
            "file_size",
            "processing_started_time",
            "processing_completed_time",
            "upload_completed",
            "upload_completed_time",
            "hidden",
        ]


class InspectionFileUploadMediaSerializer(serializers.Serializer):
    """
    Serializer for requesting to upload a file for an existing file record.
    """

    file_size = serializers.RegexField(required=True, regex=r"^(0|(\d+(\.\d+)?(KB|MB|GB)))$", write_only=True)
    filename = serializers.CharField(required=True, write_only=True)
    blob_url_with_sas = serializers.CharField(read_only=True)

    def validate(self, data: dict):
        data = super().validate(data)

        data["filename"] = data["filename"].replace("#", "")

        now = timezone.now()
        date_str = now.strftime("%d_%m_%Y_%H_%M_%S_") + str(now.microsecond // 1000)

        upload_filename = f"{date_str}_{data['filename']}"
        url = f"{settings.BLOB_STORAGE_VIDEOS_CONTAINER}/{upload_filename}"
        data["url"] = url

        return data

    def update(self, instance: FileList, validated_data: dict):
        if instance.upload_completed:
            raise serializers.ValidationError("Cannot upload media to a file that has already been uploaded")

        instance.file_size = validated_data["file_size"]
        instance.filename = validated_data["filename"]
        instance.url = validated_data["url"]
        instance.uploaded_by = self.context["request"].user
        instance.save()
        return instance

    def to_representation(self, instance: FileList):
        sas_url = get_platform_blob_url_with_sas(
            blob_path=instance.url, region=instance.storage_region, write_access=True
        )
        return {"blob_url_with_sas": sas_url}


class ProcessingFileCreateSerializer(serializers.ModelSerializer):
    file_id = serializers.IntegerField(required=True)

    class Meta:
        model = ProcessingList
        fields = [
            "file_id",
            "status",
            "upload_completed",
            "manual_qa_required",
            "sewer_data",
        ]

    def validate(self, validated_data):
        validated_data = super().validate(validated_data)
        validated_data.setdefault("upload_completed", False)
        validated_data.setdefault("manual_qa_required", False)
        validated_data.setdefault("sewer_data", False)

        try:
            file = FileList.objects.get(id=validated_data.pop("file_id"))
        except FileList.DoesNotExist:
            raise serializers.ValidationError("File does not exist")

        if ProcessingList.objects.filter(associated_file=file).exists():
            raise serializers.ValidationError("Processing record already exists for this file")

        request = self.context["request"]
        if file.upload_org != request.organisation and file.target_org != request.organisation:
            raise PermissionDenied("User does not have permission to mark this file as processing")

        validated_data["associated_file"] = file
        validated_data["target_org"] = file.target_org
        validated_data["upload_user"] = request.user
        validated_data["filename"] = file.filename
        validated_data["file_size"] = file.file_size
        return validated_data


class ProcessingFilePatchSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProcessingList
        fields = ["status", "status_reason", "upload_completed"]
