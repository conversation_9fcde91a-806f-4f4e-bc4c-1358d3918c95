import logging
from datetime import datetime
from typing import Any
from uuid import UUID

from django.conf import settings
from django.core.cache import caches
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

import jsonschema

from api.base.models import Header
from api.common.storage import get_platform_blob_url_with_sas
from api.files.models import (
    JobsTree,
)
from api.inspections.models import (
    MapPointList,
    ImportedInspectionFile,
    Inspection,
    InspectionValue,
    InspectionFilter,
)
from api.inspections.serializers.mixins import HeaderMixin, StandardHeaderMixin
from api.inspections.utilities.validate_inspections import validate_standard_value
from api.organisations.models import Organisations
from api.defects.models import StandardHeader
from vapar.constants.conversion import StandardValueConverter

from api.users.models import CustomUser

log = logging.getLogger(__name__)
sas_cache = caches["default"]
inspection_cache = caches["inspections"]


class InspectionListSerializer(serializers.ModelSerializer):
    created_at = serializers.SerializerMethodField(read_only=True)
    file_size = serializers.SerializerMethodField(read_only=True)
    job_name = serializers.SerializerMethodField(read_only=True)
    uploaded_time = serializers.SerializerMethodField(read_only=True)
    video_id = serializers.SerializerMethodField(read_only=True)
    video_job_id = serializers.SerializerMethodField(read_only=True)
    video_name = serializers.SerializerMethodField(read_only=True)
    video_url = serializers.SerializerMethodField(read_only=True)
    video_user = serializers.SerializerMethodField(read_only=True)
    water_level_url = serializers.SerializerMethodField(read_only=True)
    view_disabled = serializers.SerializerMethodField(read_only=True)
    to_be_matched = serializers.SerializerMethodField(read_only=True)

    inspection_notes = serializers.CharField(max_length=1000, read_only=True)
    name = serializers.CharField(max_length=200, read_only=True)

    def get_job_name(self, data) -> str | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.job_name
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.job_name

        return None

    def get_file_size(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.file_size
        return None

    def get_video_id(self, data) -> int | None:
        if data.associated_file:
            return data.associated_file.id
        return None

    def get_video_job_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.id
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.id

        return None

    def get_video_name(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.filename
        return None

    def get_video_url(self, data) -> str | None:
        if data.associated_file:
            return data.associated_file.url
        return None

    def get_video_user(self, data) -> str | None:
        if data.associated_file and data.associated_file.upload_user:
            return data.associated_file.upload_user
        return None

    def get_uploaded_time(self, data) -> datetime | None:
        if data.associated_file:
            return data.associated_file.created_time
        return None

    def get_water_level_url(self, data) -> str:
        return get_platform_blob_url_with_sas(data.water_level_url)

    def get_created_at(self, data) -> datetime | None:
        if data.associated_file:
            return data.associated_file.created_time
        return None

    def get_view_disabled(self, data) -> dict[str, Any]:
        request = self.context["request"]
        is_asset_owner = request.organisation.is_asset_owner

        if data.associated_file and data.associated_file.uploaded_by:
            uploaded_by_contractor = data.associated_file.uploaded_by.organisation.org_type == "Contractor"

            if is_asset_owner and uploaded_by_contractor and data.status == "Uploaded":
                return {
                    "disabled": True,
                    "message": "A contractor uploaded inspection with the status of Uploaded cannot be viewed by an asset owner. Waiting for contractor review.",
                }
        else:
            return {
                "disabled": True,
                "message": "This inspection has no associated video file",
            }

        return {"disabled": False, "message": ""}

    def get_to_be_matched(self, data) -> bool:
        if not data.inspection:
            matching_count = self.context["matching_count"]
            found = matching_count.get(data.associated_file.job_tree.id, 0)

            return found > 0

        if data.status == MapPointList.PLANNED_STATUS:
            return False

        return False

    class Meta:
        model = MapPointList
        exclude = [
            "geometry",
            "first_frame",
            "associated_file",
            "job",
            "standard_key",
            "process_model_id",
            "cr_model_id",
            "ser_cr_model_id",
            "pipe_type",
            "start_node",
            "end_node",
        ]


class InspectionDetailSerializer(serializers.ModelSerializer):
    folder = serializers.SerializerMethodField(read_only=True)
    folder_name = serializers.SerializerMethodField(read_only=True)
    target_org_id = serializers.SerializerMethodField(read_only=True)
    upload_org_id = serializers.SerializerMethodField(read_only=True)

    def get_target_org_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.target_org_id:
            return data.associated_file.target_org_id
        return None

    def get_upload_org_id(self, data) -> int | None:
        if data.associated_file and data.associated_file.upload_org_id:
            return data.associated_file.upload_org_id
        return None

    def get_folder(self, data) -> int | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.id
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.id
        return None

    def get_folder_name(self, data) -> str | None:
        if data.associated_file and data.associated_file.job_tree:
            return data.associated_file.job_tree.job_name
        elif data.inspection and data.inspection.folder:
            return data.inspection.folder.job_name
        return None

    class Meta:
        model = MapPointList
        fields = [
            "chainage",
            "asset_id",
            "name",
            "material",
            "diameter",
            "upstream_node",
            "downstream_node",
            "direction",
            "folder",
            "folder_name",
            "status",
            "date_captured",
            "standard_key",
            "id",
            "condition_rating",
            "service_condition_rating",
            "inspection_notes",
            "inspection",
            "target_org_id",
            "upload_org_id",
        ]

    def validate(self, data):
        if data.get("chainage_number", 0) > settings.MAX_CHAINAGE:
            raise serializers.ValidationError(f"Chainage cannot be greater than {settings.MAX_CHAINAGE}")

        return data


class InspectionSerializer(serializers.ModelSerializer):
    def _ensure_string(self, val: str | int | bool | None) -> str:
        value = val if val is not None else ""
        return str(value)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if self.instance:  # Update
            if self.instance.footage_id is not None and "footage" in attrs:
                raise serializers.ValidationError(
                    "Cannot change footage_id of an existing inspection if it is already set"
                )

        if footage := attrs.get("footage"):
            request_org = self.context["request"].organisation
            if footage.target_org != request_org and not request_org.is_contractor_for_org(footage.target_org):
                raise PermissionDenied(
                    "You do not have permission to create or update an inspection with this footage."
                )

        return attrs

    def update(self, instance: Inspection, validated_data: dict):
        use_header_names = self.context["request"].query_params.get("use_header_names", "false").lower() == "true"
        inspection = super().update(instance, validated_data)
        data = self.context["request"].data
        inspection_values_keys = set(data).difference(validated_data)

        if use_header_names and not inspection.asset:
            raise serializers.ValidationError("Cannot update Inspections without Assets")

        for key in inspection_values_keys:
            if use_header_names:
                standard_header = StandardHeader.objects.filter(
                    header__name=key, standard=inspection.asset.standard, header__type=Header.HeaderType.INSPECTION
                ).first()
                if not standard_header:
                    log.warning(f"Header ({key}) not found for standard. Ignoring.", extra={"header_name": key})
                    continue
                inspection_value, _ = InspectionValue.objects.get_or_create(
                    inspection_id=inspection.uuid,
                    standard_header__header__name=key,
                    defaults={"value": "", "standard_header": standard_header},
                )
            else:
                inspection_value = InspectionValue.objects.filter(
                    inspection_id=inspection.uuid, standard_header__header__mapped_mpl_field=key
                ).first()
                if not inspection_value:
                    if not inspection.asset:
                        raise serializers.ValidationError("Cannot update Inspections without Assets")
                    try:
                        standard_header = StandardHeader.objects.get(
                            header__mapped_mpl_field=key, standard_id=inspection.asset.standard
                        )
                    except StandardHeader.DoesNotExist:
                        raise serializers.ValidationError(f"{key} is an invalid standard header")
                    inspection_value = inspection.create_inspection_value(
                        standard_header=standard_header, value=data.get(key)
                    )

            serializer = InspectionValueSerializer(
                instance=inspection_value,
                data={"value": self._ensure_string(data.get(key))},
                partial=True,
                context={"standard_header": inspection_value.standard_header},
            )

            if serializer.is_valid(raise_exception=True):
                serializer.save()

            validation_errors = validate_standard_value(serializer.instance)

            if len(validation_errors) > 0:
                raise serializers.ValidationError(validation_errors)

        return inspection

    class Meta:
        model = Inspection
        fields = ["asset", "folder", "status", "legacy_id", "footage"]
        read_only_fields = ["uuid"]


class InspectionValueSerializer(StandardHeaderMixin, serializers.ModelSerializer):
    inspection = serializers.PrimaryKeyRelatedField(read_only=True)
    # Field was removed from model
    # imported_inspection_file = serializers.PrimaryKeyRelatedField(read_only=True)
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)
    value = serializers.CharField(allow_blank=True)

    def get_header_uuid(self, data) -> UUID:
        return data.standard_header.header.uuid

    def get_header_name(self, data) -> str:
        return data.standard_header.header.name

    def to_internal_value(self, data):
        standard_header = self.context["standard_header"]

        if standard_header.header.name in ("Direction", "SetupLocation"):
            converter = StandardValueConverter(header="Direction", standard=standard_header.standard.display_name)
            converted_value = converter.get_standard_value(value=data["value"])
            try:
                data["value"] = converted_value.value
            except AttributeError:
                data["value"] = converted_value
        return super().to_internal_value(data)

    class Meta:
        model = InspectionValue
        fields = [
            "uuid",
            "value",
            "standard_header",
            "standard_header_name",
            "inspection",
            # "imported_inspection_file",
            "header_uuid",
            "header_name",
        ]


class InspectionValueListSerializer(StandardHeaderMixin, HeaderMixin, serializers.ModelSerializer):
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)
    standard_id = serializers.SerializerMethodField(read_only=True)
    is_editable = serializers.SerializerMethodField(read_only=True)

    def get_standard_id(self, data):
        return data.standard_header.standard.id

    def get_header_uuid(self, data):
        return data.standard_header.header.uuid

    def get_header_name(self, data):
        return data.standard_header.header.name

    def get_is_editable(self, data):
        return data.standard_header.header.is_editable

    class Meta:
        model = InspectionValue
        fields = [
            "uuid",
            "value",
            "standard_id",
            "standard_header",
            "standard_header_name",
            "header_uuid",
            "header_name",
            "is_editable",
        ]


class UploadedByUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ["id", "first_name", "last_name"]


class ImportedInspectionFileSerializer(serializers.ModelSerializer):
    organisation = serializers.PrimaryKeyRelatedField(queryset=Organisations.objects.all(), required=True)
    folder = serializers.PrimaryKeyRelatedField(queryset=JobsTree.objects.all(), required=True)
    file_name = serializers.CharField(required=True)

    class Meta:
        model = ImportedInspectionFile
        fields = [
            "uuid",
            "organisation",
            "folder",
            "file_name",
            "blob_storage",
            "imported",
            "import_action",
            "created_at",
            "created_by",
        ]


class InspectionBulkUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Inspection
        fields = ["status"]


class InspectionFilterSerializer(serializers.ModelSerializer):
    class Meta:
        model = InspectionFilter
        fields = ["uuid", "organisation", "filter_model", "folder_filter_model"]

    def validate_folder_filter_model(self, data):
        try:
            jsonschema.validate(data, InspectionFilter.FOLDER_FILTER_MODEL_SCHEMA)
        except jsonschema.exceptions.ValidationError as e:
            raise serializers.ValidationError(e.message)
        return data

    def validate_filter_model(self, data):
        # try:
        #     jsonschema.validate(data, InspectionFilter.FILTER_MODEL_SCHEMA)
        # except jsonschema.exceptions.ValidationError as e:
        #     raise serializers.ValidationError(e.message)
        return data

    def create(self, validated_data):
        user = self.context["user"]
        return InspectionFilter.objects.create(**validated_data, user=user)

    def update(self, instance, validated_data):
        instance.filter_model = validated_data.get("filter_model", instance.filter_model)

        instance.folder_filter_model = validated_data.get("folder_filter_model", instance.folder_filter_model)
        instance.save()

        return instance
