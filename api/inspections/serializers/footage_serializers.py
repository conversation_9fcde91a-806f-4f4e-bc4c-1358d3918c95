from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.generics import get_object_or_404

from api.files.models import FileList
from api.inspections.models import Footage, Keyframe
from api.inspections.models.footage import ChainageUnit
from api.inspections.serializers.inspection_serializers import FramesListSerializer


class KeyframeSerializer(serializers.ModelSerializer):
    video_frame_id = serializers.IntegerField(required=False, allow_null=True)
    video_frame_details = serializers.SerializerMethodField()

    @extend_schema_field(FramesListSerializer(allow_null=True))
    def get_video_frame_details(self, obj: Keyframe) -> dict | None:
        if not self.context.get("with_video_frame_details", False):
            return None
        if obj.video_frame_id is None:
            return None
        ser = FramesListSerializer(obj.video_frame, context=self.context)
        return ser.data

    def validate(self, attrs):
        attrs = super().validate(attrs)
        footage = self.context["footage"]
        attrs["footage_id"] = footage.id
        if video_frame_id := attrs.get("video_frame_id"):
            # Note: We iterate over the cached frames queryset like this to avoid hitting the database multiple times.
            if not footage.video_file or not any(
                frame.id == video_frame_id for frame in footage.video_file.videoframes_set.all()
            ):
                raise serializers.ValidationError(
                    "The provided video frame ID does not exist for the footage's video file."
                )

        return attrs

    def create(self, validated_data):
        footage = self.context["footage"]
        keyframe = footage.append_new_keyframe(
            video_frame=validated_data.get("video_frame_id"),
            time_reference_milliseconds=validated_data["time_reference_milliseconds"],
            chainage=validated_data.get("chainage", 0.0),
            is_hidden=validated_data.get("is_hidden", False),
            at_joint=validated_data.get("at_joint", False),
            has_loss_of_vision=validated_data.get("has_loss_of_vision", False),
            has_textbox=validated_data.get("has_textbox", False),
            has_title=validated_data.get("has_title", False),
        )
        return keyframe

    class Meta:
        model = Keyframe
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "footage_id",
            "sequence_number",
            "video_frame_details",
        ]
        fields = [
            "id",
            "footage_id",
            "video_frame_id",
            "time_reference_milliseconds",
            "sequence_number",
            "chainage",
            "is_hidden",
            "created_at",
            "updated_at",
            "at_joint",
            "has_loss_of_vision",
            "has_textbox",
            "has_title",
            "video_frame_details",
        ]


class FootageSerializer(serializers.ModelSerializer):
    video_file_id = serializers.IntegerField(required=False, allow_null=True)

    def create(self, validated_data: dict) -> Footage:
        org = self.context["request"].organisation
        if video_file_id := validated_data.pop("video_file_id", None):
            video_file = get_object_or_404(FileList, id=video_file_id, target_org=org)
        else:
            video_file = None
        chainage_unit = validated_data.pop("chainage_unit", ChainageUnit.METRES)
        return Footage.objects.create_for_org(org, file=video_file, chainage_unit=chainage_unit)

    class Meta:
        model = Footage
        read_only_fields = [
            "id",
            "total_frames",
            "created_at",
            "updated_at",
        ]
        fields = [
            "id",
            "chainage_unit",
            "video_file_id",
            "total_frames",
            "created_at",
            "updated_at",
        ]


class FootageDetailSerializer(serializers.ModelSerializer):
    """
    Detailed representation of inspection footage, including keyframes.
    """

    keyframes = KeyframeSerializer(many=True, read_only=True)

    class Meta:
        model = Footage
        read_only_fields = [
            "id",
            "total_frames",
            "created_at",
            "updated_at",
            "keyframes",
        ]
        fields = [
            "id",
            "video_file_id",
            "total_frames",
            "created_at",
            "updated_at",
            "keyframes",
        ]
