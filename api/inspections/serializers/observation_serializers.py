import logging

from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from api.defects.models import SubFeature, SubFeatureOption, SubFeatureUnit
from api.defects.models.standards import SubFeatureValDetails
from api.defects.serializers import CodeSerializer, CodeScoreSerializer
from api.inspections.models import Observation, Keyframe, ObservedFeature, ObservedSubFeature, Inspection
from api.inspections.serializers.footage_serializers import KeyframeSerializer

log = logging.getLogger(__name__)


class ObservedSubFeatureSerializer(serializers.ModelSerializer):
    def validate(self, attrs) -> dict:
        attrs = super().validate(attrs)

        sub_feature: SubFeature = attrs.get("sub_feature") or self.instance.sub_feature
        selected_option: SubFeatureOption | None = attrs.get("selected_option")

        if sub_feature.kind == SubFeature.Kind.NUMERIC:
            if (
                attrs.get("numeric_value") is None
                and attrs.get("numeric_range_min") is None
                and attrs.get("numeric_range_max") is None
            ):
                log.warning(
                    "Failed validation for ObservedSubFeature: no numeric value or range specified",
                    extra={
                        "attrs": attrs,
                    },
                )
                raise ValidationError("Must specify a numeric value for numeric sub-feature")
            if attrs.get("numeric_value") is not None and (
                attrs.get("numeric_range_min") is not None or attrs.get("numeric_range_max") is not None
            ):
                log.warning(
                    "Failed validation for ObservedSubFeature: both numeric value and numeric range specified",
                    extra={
                        "attrs": attrs,
                    },
                )
                raise ValidationError("Cannot specify both numeric value and numeric range for numeric sub-feature")
            if attrs.get("numeric_unit") is None:
                log.warning(
                    "Failed validation for ObservedSubFeature: no numeric unit specified",
                    extra={
                        "attrs": attrs,
                    },
                )
                raise ValidationError("Must specify numeric unit for numeric sub-feature")
            if selected_option is not None:
                log.warning(
                    "Failed validation for ObservedSubFeature: selected option specified for numeric sub-feature",
                    extra={
                        "attrs": attrs,
                    },
                )
                raise ValidationError("Cannot specify selected option for numeric sub-feature")

            unit = SubFeatureUnit(attrs["numeric_unit"])
            unit_numeric_kind = unit.get_numeric_kind()
            if unit_numeric_kind.value != sub_feature.numeric_kind:
                log.warning(
                    "Failed validation for ObservedSubFeature: numeric unit does not match numeric kind of sub-feature",
                    extra={
                        "attrs": attrs,
                        "sub_feature_numeric_kind": sub_feature.numeric_kind,
                        "numeric_unit": unit.get_numeric_kind(),
                    },
                )
                raise ValidationError(
                    "Failed validation for ObservedSubFeature: "
                    f"Numeric unit does not match the numeric kind of the sub-feature '{sub_feature.display_name}' "
                    f"(expected {sub_feature.numeric_kind}, got {unit_numeric_kind})"
                )

        else:  # CATEGORICAL
            if selected_option.sub_feature != sub_feature:
                raise ValidationError("Selected option does not belong to the provided sub-feature")
            if (
                attrs.get("numeric_value") is not None
                or attrs.get("numeric_range_min") is not None
                or attrs.get("numeric_range_max") is not None
                or attrs.get("numeric_unit") is not None
            ):
                log.warning(
                    "Failed validation for ObservedSubFeature: numeric value or unit specified "
                    "for categorical sub-feature",
                    extra={
                        "attrs": attrs,
                    },
                )
                raise ValidationError("Cannot specify numeric value or unit for categorical sub-feature")

        return attrs

    def create(self, validated_data: dict) -> ObservedSubFeature:
        observed_feature = self.context["observed_feature"]
        try:
            obs_sub_feature = observed_feature.append_sub_feature(
                sub_feature=validated_data["sub_feature"],
                numeric_value=validated_data.get("numeric_value"),
                numeric_range_min=validated_data.get("numeric_range_min"),
                numeric_range_max=validated_data.get("numeric_range_max"),
                numeric_unit=validated_data.get("numeric_unit"),
                selected_option=validated_data.get("selected_option"),
                prediction_certainty=validated_data.get("sub_feature_class_prediction_certainty"),
            )
        except ValueError as e:
            raise ValidationError(str(e))
        return obs_sub_feature

    def update(self, instance: ObservedSubFeature, validated_data: dict) -> ObservedSubFeature:
        instance.numeric_value = validated_data.get("numeric_value", instance.numeric_value)
        instance.numeric_range_min = validated_data.get("numeric_range_min", instance.numeric_range_min)
        instance.numeric_range_max = validated_data.get("numeric_range_max", instance.numeric_range_max)
        instance.numeric_unit = validated_data.get("numeric_unit", instance.numeric_unit)
        instance.selected_option = validated_data.get("selected_option", instance.selected_option)
        instance.sub_feature_class_prediction_certainty = validated_data.get(
            "sub_feature_class_prediction_certainty", instance.sub_feature_class_prediction_certainty
        )

        instance.save()
        return instance

    class Meta:
        model = ObservedSubFeature
        read_only_fields = [
            "id",
            "observed_feature",
            "created_at",
            "updated_at",
        ]
        fields = [
            "id",
            "created_at",
            "updated_at",
            "sub_feature",
            "observed_feature",
            "numeric_value",
            "numeric_range_min",
            "numeric_range_max",
            "numeric_unit",
            "selected_option",
            "sub_feature_class_prediction_certainty",
        ]


class StandardisedReportValuesSerializer(serializers.Serializer):
    """
    A read-only representation of report values under a standard
    """

    characterisation_1 = serializers.CharField(allow_null=True, default=None)
    characterisation_2 = serializers.CharField(allow_null=True, default=None)
    quantity_1_value = serializers.FloatField(allow_null=True, default=None)
    quantity_2_value = serializers.FloatField(allow_null=True, default=None)
    quantity_1_units = serializers.CharField(allow_null=True, default=None)
    quantity_2_units = serializers.CharField(allow_null=True, default=None)


class ObservationAggregatedFlagsSerializer(serializers.Serializer):
    """
    An aggregated representation of the flags on the keyframes connected to the observation.
    """

    at_joint = serializers.BooleanField(default=False)
    has_loss_of_vision = serializers.BooleanField(default=False)
    has_textbox = serializers.BooleanField(default=False)
    has_title = serializers.BooleanField(default=False)


class ObservedFeatureSerializer(serializers.ModelSerializer):
    sub_features = ObservedSubFeatureSerializer(many=True)
    standardised_vals = serializers.SerializerMethodField(read_only=True)
    code_details = serializers.SerializerMethodField(read_only=True)
    scoring_details = serializers.SerializerMethodField(read_only=True)
    code_description = serializers.SerializerMethodField(read_only=True)

    @extend_schema_field(StandardisedReportValuesSerializer(allow_null=True))
    def get_standardised_vals(self, obj: ObservedFeature) -> dict | None:
        if not self.context.get("with_standardised_details", False):
            return None

        std_id = obj.observation.inspection.standard_id_prefetched

        characterisation_1 = None
        characterisation_2 = None
        quantity_1_value = None
        quantity_2_value = None
        quantity_1_units = None
        quantity_2_units = None

        for obs_sub_feature in obj.sub_features.all():
            standardised_option = None
            if obs_sub_feature.selected_option:
                standardised_option = next(
                    (
                        std_opt
                        for std_opt in obs_sub_feature.selected_option.standardised_set.all()
                        if std_opt.standard_id == std_id
                    ),
                    None,
                )
            selected_display_name = standardised_option.display_name if standardised_option else None

            for std_sub_feat in obs_sub_feature.sub_feature.standardised_set.all():
                if std_sub_feat.standard_id == std_id:
                    if std_sub_feat.characteristic_field_number == 1:
                        characterisation_1 = selected_display_name
                    elif std_sub_feat.characteristic_field_number == 2:
                        characterisation_2 = selected_display_name
                    elif std_sub_feat.quantity_field_number == 1:
                        quantity_1_value = obs_sub_feature.numeric_value
                        quantity_1_units = obs_sub_feature.numeric_unit
                    elif std_sub_feat.quantity_field_number == 2:
                        quantity_2_value = obs_sub_feature.numeric_value
                        quantity_2_units = obs_sub_feature.numeric_unit

                    break

        ser = StandardisedReportValuesSerializer(
            data={
                "characterisation_1": characterisation_1,
                "characterisation_2": characterisation_2,
                "quantity_1_value": quantity_1_value,
                "quantity_2_value": quantity_2_value,
                "quantity_1_units": quantity_1_units,
                "quantity_2_units": quantity_2_units,
            }
        )
        ser.is_valid(raise_exception=True)
        return ser.data

    @extend_schema_field(CodeSerializer(allow_null=True))
    def get_code_details(self, obj: ObservedFeature) -> dict | None:
        if obj.code_id is None:
            return None
        return CodeSerializer(instance=obj.code, context=self.context).data

    @extend_schema_field(CodeScoreSerializer(allow_null=True))
    def get_scoring_details(self, obj: ObservedFeature) -> str | None:
        if obj.scoring_id is None:
            return None
        return CodeScoreSerializer(instance=obj.scoring, context=self.context).data

    def get_code_description(self, obj: ObservedFeature) -> str | None:
        if not self.context.get("with_code_description", False):
            return None
        if obj.code is None:
            return None

        std_id = obj.observation.inspection.standard_id_prefetched
        sub_feature_details = [
            SubFeatureValDetails(
                val=observed_sf.as_sub_feature_value(),
                sub_feature=observed_sf.sub_feature,
                option=observed_sf.selected_option,
            )
            for observed_sf in obj.sub_features.all()
        ]
        description = obj.code.build_description_from_values(std_id, sub_feature_details)

        return description

    def validate(self, attrs) -> dict:
        attrs = super().validate(attrs)

        if self.instance:
            if "sub_features" in attrs:
                raise ValidationError(
                    "Cannot update sub-features through this endpoint. Use the sub-feature endpoint instead."
                )

        return attrs

    def create(self, validated_data) -> ObservedFeature:
        observation = self.context["observation"]
        observed_feature = observation.features.create(
            clock_position_from=validated_data.get("clock_position_from"),
            clock_position_to=validated_data.get("clock_position_to"),
            feature=validated_data["feature"],
            code=validated_data.get("code"),
            scoring=validated_data.get("scoring"),
            feature_class_prediction_certainty=validated_data.get("feature_class_prediction_certainty"),
        )
        ObservedSubFeatureSerializer(
            many=True,
            context={**self.context, "observed_feature": observed_feature},
        ).create(validated_data["sub_features"])

        return observed_feature

    def update(self, instance: ObservedFeature, validated_data: dict) -> ObservedFeature:
        # Update the observed feature itself
        instance.clock_position_from = validated_data.get("clock_position_from", instance.clock_position_from)
        instance.clock_position_to = validated_data.get("clock_position_to", instance.clock_position_to)
        instance.feature = validated_data.get("feature", instance.feature)
        instance.code = validated_data.get("code", instance.code)
        instance.scoring = validated_data.get("scoring", instance.scoring)
        instance.feature_class_prediction_certainty = validated_data.get(
            "feature_class_prediction_certainty", instance.feature_class_prediction_certainty
        )

        instance.save()
        return instance

    class Meta:
        model = ObservedFeature
        read_only_fields = [
            "id",
            "observation",
            "created_at",
            "updated_at",
            "standardised_vals",
            "code_details",
            "scoring_details",
            "code_description",
        ]
        fields = [
            "id",
            "created_at",
            "updated_at",
            "feature",
            "observation",
            "clock_position_from",
            "clock_position_to",
            "code",
            "scoring",
            "feature_class_prediction_certainty",
            "sub_features",
            "standardised_vals",
            "code_details",
            "scoring_details",
            "code_description",
        ]


class ObservationSerializer(serializers.ModelSerializer):
    keyframes = serializers.PrimaryKeyRelatedField(many=True, queryset=Keyframe.objects.all())
    features = ObservedFeatureSerializer(many=True)

    start_chainage = serializers.FloatField(read_only=True)
    end_chainage = serializers.FloatField(read_only=True)

    aggregated_flags = serializers.SerializerMethodField(read_only=True)
    keyframe_details = serializers.SerializerMethodField(read_only=True)

    @extend_schema_field(ObservationAggregatedFlagsSerializer(allow_null=True))
    def get_aggregated_flags(self, obj: Observation) -> dict | None:
        if not self.context.get("with_aggregated_flags", False):
            return None

        ser = ObservationAggregatedFlagsSerializer(
            data={
                "at_joint": obj.at_joint,
                "has_loss_of_vision": obj.has_loss_of_vision,
                "has_textbox": obj.has_textbox,
                "has_title": obj.has_title,
            }
        )
        ser.is_valid(raise_exception=True)
        return ser.data

    @extend_schema_field(KeyframeSerializer(many=True, allow_null=True))
    def get_keyframe_details(self, obj: Observation) -> list[dict] | None:
        if not self.context.get("with_keyframe_details", False):
            return None
        # Sort them in memory so that prefetching is not overridden
        keyframes = sorted(obj.keyframes.all(), key=lambda kf: kf.sequence_number)
        ser = KeyframeSerializer(
            instance=keyframes,
            many=True,
            context=self.context,
        )
        return ser.data

    def validate(self, attrs) -> dict:
        attrs = super().validate(attrs)
        if self.instance:  # Update
            inspection = self.instance.inspection
            if "keyframes" in attrs:
                keyframes = attrs["keyframes"]
                if not keyframes:
                    log.warning(
                        "Failed validation for Observation: no keyframes provided for update",
                    )
                    raise ValidationError("No keyframes provided for observation")

            if "features" in attrs:
                raise ValidationError(
                    "Cannot update features through this endpoint. Use the observed feature endpoint instead."
                )

        else:  # Create
            if not attrs["keyframes"]:
                log.warning("Failed validation for Observation: no keyframes provided for creation")
                raise ValidationError("No keyframes provided for observation")
            inspection = self.context["inspection"]

        # Shared validation
        if (keyframes := attrs.get("keyframes")) is not None:
            footage_ids = {k.footage_id for k in keyframes}
            if len(footage_ids) != 1 or footage_ids.pop() != inspection.footage_id:
                log.warning(
                    "Failed validation for Observation: keyframes do not link to the same footage as inspection",
                    extra={
                        "keyframes_footage_ids": footage_ids,
                        "inspection_footage_id": inspection.footage_id,
                    },
                )
                raise ValidationError("Keyframes provided for observation do not link to same footage as inspection")
            if len(keyframes) != len({k.id for k in keyframes}):
                log.warning(
                    "Failed validation for Observation: keyframes provided are not unique",
                    extra={
                        "keyframes_ids": [k.id for k in keyframes],
                    },
                )
                raise ValidationError("Keyframes provided for observation are not unique")

        return attrs

    def create(self, validated_data) -> Observation:
        inspection: Inspection = self.context["inspection"]
        observation = inspection.observations.create(
            remarks=validated_data.get("remarks", ""),
        )
        observation.keyframes.add(*validated_data["keyframes"])

        ObservedFeatureSerializer(
            many=True,
            context={**self.context, "observation": observation},
        ).create(validated_data["features"])

        return observation

    def update(self, instance: Observation, validated_data) -> Observation:
        if "keyframes" in validated_data:
            keyframes = validated_data["keyframes"]
            instance.keyframes.clear()
            instance.keyframes.add(*keyframes)
        if "remarks" in validated_data:
            instance.remarks = validated_data["remarks"]

        instance.save()
        return instance

    class Meta:
        model = Observation
        read_only_fields = [
            "id",
            "inspection_id",
            "start_chainage",
            "end_chainage",
            "created_at",
            "updated_at",
            "aggregated_flags",
            "keyframe_details",
        ]
        fields = [
            "id",
            "created_at",
            "updated_at",
            "inspection_id",
            "remarks",
            "keyframes",
            "features",
            "start_chainage",
            "end_chainage",
            "aggregated_flags",
            "keyframe_details",
        ]
