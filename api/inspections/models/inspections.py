import logging
import uuid
from collections import Counter

from django.conf import settings
from django.contrib.gis.db import models as geomodels
from django.contrib.gis.geos import Point
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from django.db.models import Q
from django.db.models.signals import post_save, m2m_changed
from django.dispatch import receiver
from django.utils import timezone
from django.utils.functional import cached_property
from vapar.constants.units import SubFeatureUnit
from vapar.core.coding import FeatureValue, FlagValues, SubFeatureValue

from api.base.models import Header
from api.common.enums import OperatorEnum, StatusEnum
from api.common.storage import get_platform_storage_region_base_url
from api.defects.models import (
    StandardHeader,
    Feature,
    Code,
    CodeScore,
    SubFeature,
    SubFeatureOption,
    Standard,
)
from api.files.models import FileList, VideoFrames, JobsTree, Jobs
from api.inspections.models.footage import Footage, Keyframe
from api.organisations.models import Organisations, AssetOwners, Contractors
from api.users.models import CustomUser

log = logging.getLogger(__name__)


def default_filter_model():
    return {"common_filters": {"status": {"value": [], "operator": "IN"}}, "header_filters": []}


def default_folder_filter_model():
    return []


class InspectionFilter(models.Model):
    FILTER_MODEL_SCHEMA = {
        "type": "object",
        "properties": {
            "common_filters": {
                "type": "object",
                "properties": {
                    "status": {
                        "type": "object",
                        "properties": {
                            "value": {"type": "array", "items": {"type": "string"}},
                            "operator": {
                                "type": "string",
                                "enum": [operator_enum.name for operator_enum in OperatorEnum],
                            },
                        },
                        "required": ["value", "operator"],
                    },
                },
                "required": ["status"],
            },
            "header_filters": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field": {"type": "string"},
                        "operator": {
                            "type": "string",
                            "enum": [operator_enum.name for operator_enum in OperatorEnum],
                        },
                        "value": {"type": "array", "items": {"type": "string"}},
                        "standard": {"type": "integer"},
                    },
                    "required": ["field", "operator", "value"],
                },
            },
        },
    }
    FOLDER_FILTER_MODEL_SCHEMA = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "id": {"type": "integer"},
                "name": {"type": "string"},
            },
            "required": ["id"],
        },
    }

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="inspection_filter",
    )
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE)
    filter_model = models.JSONField(default=default_filter_model)
    folder_filter_model = models.JSONField(default=default_folder_filter_model)
    last_modified = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "organisation")


class ImportedInspectionFile(models.Model):
    PROCESS_ACTIONS = [
        ("ignore_duplicates", "Ignore Duplicates"),
        ("overwrite_duplicates", "Overwrite Duplicates"),
        ("create_duplicates", "Create Duplicates"),
        ("awaiting_action", "Awaiting Action"),
    ]

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    blob_storage = models.CharField(max_length=250, null=True)
    file_name = models.CharField(max_length=50, null=True)
    folder = models.ForeignKey(JobsTree, on_delete=models.SET_NULL, null=True)
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    imported = models.BooleanField(null=True)
    import_action = models.CharField(choices=PROCESS_ACTIONS, null=True, max_length=25)

    def save(self, *args, **kwargs):
        base_url = get_platform_storage_region_base_url()
        self.blob_storage = f"{base_url}{settings.BLOB_STORAGE_DRT_CONTAINER}/{self.uuid}.csv"
        super().save(*args, **kwargs)


class Asset(models.Model):
    class AssetType(models.TextChoices):
        PIPE = "pipe", "Pipe"
        MANHOLE = "manhole", "Manhole"

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(choices=AssetType.choices, default=AssetType.PIPE, max_length=7)
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def get_asset_values(self, get_value_dict: bool = False, use_header_names: bool = False):
        asset_values = self.assetvalue_set.all()

        if get_value_dict:
            asset_details = {}
            for asset_value in asset_values:
                detail_key = (
                    asset_value.standard_header.header.name if use_header_names else asset_value.standard_header.name
                )
                asset_details[detail_key] = asset_value.value

            return asset_details
        else:
            return asset_values

    @cached_property
    def standard(self) -> int | None:
        """Returns a derived standard ID for the asset based on the most common standard header used."""
        asset_values = self.get_asset_values()
        if len(asset_values) > 0:
            cnt = Counter([av.standard_header.standard_id for av in asset_values])
            asset_standard_id = cnt.most_common()[0][0]
            return asset_standard_id
        return None


class InspectionQuerySet(models.QuerySet):
    def visible_to_org(self, organisation: Organisations):
        """
        Filter to inspections that are visible to the given organisation
        """
        qs = self.exclude(file__hidden=True)
        if organisation.is_contractor:
            qs = qs.filter(
                Q(file__target_org=organisation)
                | Q(asset__organisation=organisation)
                | Q(file__upload_org=organisation)
            )
        else:
            qs = qs.filter(Q(file__target_org=organisation) | Q(asset__organisation=organisation))
        return qs


class InspectionModelManager(models.Manager):
    def _generate_vapar_id(self, file_id: int | None) -> str | None:
        if not file_id:
            return
        # footage_owner = self.file.target_org
        footage_owner = FileList.objects.get(pk=file_id).target_org
        org_inspections = Inspection.objects.filter(file__target_org=footage_owner).exclude(legacy_id__isnull=True)
        inpsection_legacy_ids = org_inspections.values_list("legacy_id", flat=True)
        legacy_ids = [0]
        for legacy_id in inpsection_legacy_ids:
            # check old style id (<auto-increment>) or new (<org_id>-<org_specific_incremental>)
            try:
                legacy_ids.append(int(legacy_id))
            except ValueError:
                legacy_ids.append(int(legacy_id.split("-")[-1]))
        legacy_ids.sort(reverse=True)

        # build new style ID always
        increment = 1
        new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"
        # ensure the legacy ID doesn't exist
        while Inspection.objects.filter(legacy_id=new_vapar_id).exists():
            increment += 1
            new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"

        return new_vapar_id

    def get_queryset(self):
        return InspectionQuerySet(self.model, using=self._db)

    def create(self, **obj_data):
        if "legacy_id" not in obj_data or not obj_data["legacy_id"]:
            obj_data["legacy_id"] = self._generate_vapar_id(obj_data.get("file_id"))
        return super().create(**obj_data)

    def visible_to_org(self, organisation: Organisations):
        """
        Filter to inspections that are visible to the given organisation
        """
        return self.get_queryset().visible_to_org(organisation)


class Inspection(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    legacy_id = models.CharField(unique=True, null=True, max_length=20)
    asset = models.ForeignKey(Asset, on_delete=models.SET_NULL, null=True)
    folder = models.ForeignKey(JobsTree, on_delete=models.SET_NULL, null=True)
    status = models.CharField(
        max_length=50,
        null=False,
        default=StatusEnum.UPLOADED.value,
        choices=[(status.value, status.value) for status in StatusEnum],
    )
    file = models.OneToOneField(FileList, on_delete=models.SET_NULL, null=True, blank=True)
    service_grade = models.IntegerField(default=1, null=False)
    structural_grade = models.IntegerField(default=1, null=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    # A timestamp field which is updated through signals when frames, values, etc. are updated
    last_related_update = models.DateTimeField(auto_now=True)

    objects = InspectionModelManager.from_queryset(InspectionQuerySet)()
    footage = models.ForeignKey(Footage, on_delete=models.SET_NULL, null=True, related_name="inspections", default=None)

    @cached_property
    def standard(self) -> int | None:
        """Returns a derived standard ID for the inspection based on the most common standard header used."""
        inspection_values = self.get_inspection_values()
        if len(inspection_values) > 0:
            cnt = Counter([iv.standard_header.standard_id for iv in inspection_values])
            inspection_standard_id = cnt.most_common()[0][0]
            return inspection_standard_id
        return None

    @cached_property
    def standard_id_prefetched(self):
        """
        Return a derived standard ID for the inspection based on the most common standard header used.

        Differs from the 'standard' property is that it will not trigger a db query if the inspection's values and their
        standard headers have been prefetched.
        """
        cnt = Counter(iv.standard_header.standard_id for iv in self.inspectionvalue_set.all())
        if not cnt:
            return None
        inspection_standard_id = cnt.most_common()[0][0]
        return inspection_standard_id

    def __str__(self):
        return (
            f"<Inspection uuid='{self.uuid}' legacy_id='{self.legacy_id}' status='{self.status}' "
            f"asset='{self.asset_id}' file='{self.file_id}' folder={self.folder_id} footage={self.footage_id} "
            f"created_at='{self.created_at}'>"
        )

    def generate_vapar_id(self) -> str:
        footage_owner = self.file.target_org
        org_inspections = type(self).objects.filter(file__target_org=footage_owner).exclude(legacy_id__isnull=True)
        inpsection_legacy_ids = org_inspections.values_list("legacy_id", flat=True)
        legacy_ids = [0]
        for legacy_id in inpsection_legacy_ids:
            # check old style id (<auto-increment>) or new (<org_id>-<org_specific_incremental>)
            try:
                legacy_ids.append(int(legacy_id))
            except ValueError:
                legacy_ids.append(int(legacy_id.split("-")[-1]))
        legacy_ids.sort(reverse=True)

        # build new style ID always
        increment = 1
        new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"
        # ensure the legacy ID doesn't exist
        while type(self).objects.filter(legacy_id=new_vapar_id).exists():
            increment += 1
            new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"

        return new_vapar_id

    def create_inspection_value(
        self,
        standard_header: StandardHeader,
        value: str,
    ) -> "InspectionValue":
        inspection_value = InspectionValue(
            inspection=self,
            standard_header=standard_header,
            value=value,
            original_value=value,
            created_at=timezone.now(),
        )
        inspection_value.save()
        return inspection_value

    def get_inspection_values(self, get_value_dict=False, use_header_names=False):
        inspection_values = InspectionValue.objects.filter(inspection=self).prefetch_related(
            "standard_header", "standard_header__header"
        )

        if get_value_dict:
            inspection_details = {}

            for inspection_value in inspection_values:
                detail_key = (
                    inspection_value.standard_header.header.name
                    if use_header_names
                    else inspection_value.standard_header.name
                )
                inspection_details[detail_key] = inspection_value.value

            return inspection_details
        else:
            return inspection_values

    def combine_asset_inspection_values(self, get_value_dict=False):
        inspection_details = self.get_inspection_values(get_value_dict=get_value_dict)
        asset_details = self.asset.get_asset_values(get_value_dict=get_value_dict)
        inspection_details.update(asset_details)

        return inspection_details

    def get_start_node_inspection_value(self):
        start_node_header = Header.objects.filter(name="StartNodeRef").first()
        return InspectionValue.objects.filter(inspection=self, standard_header__header=start_node_header.uuid).first()

    def get_end_node_inspection_value(self):
        end_node_header = Header.objects.filter(name="FinishNodeRef").first()
        return InspectionValue.objects.filter(inspection=self, standard_header__header=end_node_header.uuid).first()


class AssetValue(models.Model):
    MAX_VALUE_LENGTH = 255
    ENFORCE_UNIQUE = False

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    standard_header = models.ForeignKey(StandardHeader, on_delete=models.CASCADE)
    value = models.CharField(max_length=MAX_VALUE_LENGTH, null=True, blank=True)
    original_value = models.CharField(max_length=MAX_VALUE_LENGTH, null=True)
    created_at = models.DateTimeField(auto_now_add=True)


class InspectionValue(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE)
    standard_header = models.ForeignKey(StandardHeader, on_delete=models.CASCADE)
    value = models.TextField(max_length=1000)
    original_value = models.TextField(max_length=1000, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def __str__(self):
        return f"InspectionValue<standard_header_name='{self.standard_header.name}' header_name='{self.standard_header.header.name}' value='{self.value}'>"


class KeyframeObservation(models.Model):
    """
    Join entity linking an observation to one or more keyframes.
    """

    keyframe = models.ForeignKey(Keyframe, on_delete=models.CASCADE, related_name="keyframe_observations")
    observation = models.ForeignKey("Observation", on_delete=models.CASCADE, related_name="keyframe_observations")


class ObservationQuerySet(models.QuerySet):
    def with_start_end_chainages(self):
        """
        Annotate each observation with _start_chainage and _end_chainage values.
        """
        return self.annotate(
            _start_chainage=models.Min("keyframes__chainage"),
            _end_chainage=models.Max("keyframes__chainage"),
        )

    def with_flags(self):
        """
        Annotate each observation with flag values derived from its keyframes.
        """
        return self.annotate(
            _at_joint=models.Exists(
                KeyframeObservation.objects.filter(observation=models.OuterRef("pk"), keyframe__at_joint=True)
            ),
            _has_loss_of_vision=models.Exists(
                KeyframeObservation.objects.filter(observation=models.OuterRef("pk"), keyframe__has_loss_of_vision=True)
            ),
            _has_textbox=models.Exists(
                KeyframeObservation.objects.filter(observation=models.OuterRef("pk"), keyframe__has_textbox=True)
            ),
            _has_title=models.Exists(
                KeyframeObservation.objects.filter(observation=models.OuterRef("pk"), keyframe__has_title=True)
            ),
        )

    def prefetch_details(self):
        return self.prefetch_related(
            "keyframes",
            "features",
            "features__sub_features",
            "features__code",
            "features__scoring",
        )

    def prefetch_keyframe_video_frames(self):
        """All the prefetches needed for efficiently serialising the video frames of the observations' keyframes"""
        return self.prefetch_related(
            "keyframes",
            "keyframes__video_frame",
            "keyframes__video_frame__parent_video",
            "keyframes__video_frame__defect_scores",
            "keyframes__video_frame__parent_video__mappointlist",
        )

    def prefetch_standardised(self):
        """Prefetch the StandardSubFeatures and StandardSubFeatureOptions for the features of the observations."""
        return self.prefetch_related(
            "features__sub_features__sub_feature__standardised_set",
            "features__sub_features__selected_option__standardised_set",
        )

    def prefetch_inspection_values(self):
        """Prefetch the inspection values related to the observations' parent inspection."""
        return self.select_related("inspection").prefetch_related(
            "inspection__inspectionvalue_set",
            "inspection__inspectionvalue_set__standard_header",
            "inspection__inspectionvalue_set__standard_header__header",
        )

    def visible_to_org(self, org: Organisations):
        return self.filter(inspection__in=Inspection.objects.visible_to_org(org))


class Observation(models.Model):
    """
    Represents an observation made during an inspection. An observation is composed of a set of observed features,
    which span a set of keyframes in the footage.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE, related_name="observations")

    keyframes = models.ManyToManyField(
        Keyframe,
        related_name="observations",
        through=KeyframeObservation,
    )
    """
    The keyframes that this observation spans.
    """

    remarks = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    objects = ObservationQuerySet.as_manager()

    def __str__(self):
        return f"<Observation id={self.id} inspection={self.inspection.uuid} keyframe_count={self.keyframes.count()} features={self.features.all()}'>"

    @cached_property
    def start_chainage(self) -> float | None:
        """
        The chainage at which the observation starts.
        """
        if hasattr(self, "_start_chainage"):
            return self._start_chainage
        agg = self.keyframes.all().aggregate(start_chainage=models.Min("chainage"))
        return agg["start_chainage"]

    @cached_property
    def end_chainage(self) -> float | None:
        """
        The chainage at which the observation ends. Same as the start_chainage if the observation is not continuous.
        """
        if hasattr(self, "_end_chainage"):
            return self._end_chainage
        agg = self.keyframes.all().aggregate(end_chainage=models.Max("chainage"))
        return agg["end_chainage"]

    @property
    def is_continuous(self) -> bool:
        return self.start_chainage != self.end_chainage

    @property
    def continuous_length(self) -> float:
        if self.start_chainage is None or self.end_chainage is None:
            return 0.0
        return self.end_chainage - self.start_chainage

    @cached_property
    def at_joint(self) -> bool:
        if hasattr(self, "_at_joint"):
            return self._at_joint
        return any(kf.at_joint for kf in self.keyframes.all())

    @cached_property
    def has_loss_of_vision(self) -> bool:
        if hasattr(self, "_has_loss_of_vision"):
            return self._has_loss_of_vision
        return any(kf.has_loss_of_vision for kf in self.keyframes.all())

    @cached_property
    def has_textbox(self) -> bool:
        if hasattr(self, "_has_textbox"):
            return self._has_textbox
        return any(kf.has_textbox for kf in self.keyframes.all())

    @cached_property
    def has_title(self) -> bool:
        if hasattr(self, "_has_title"):
            return self._has_title
        return any(kf.has_title for kf in self.keyframes.all())


class ObservedFeatureQuerySet(models.QuerySet):
    def prefetch_parents(self):
        return self.select_related("observation", "observation__inspection")


class ObservedFeature(models.Model):
    """
    An instance of a feature that is part of an observation.
    ObservedFeatures can be assigned codes and can have scoring applied to them.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    observation = models.ForeignKey(Observation, on_delete=models.CASCADE, related_name="features")
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="observed_instances")

    clock_position_from = models.IntegerField(
        null=True, default=None, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    clock_position_to = models.IntegerField(
        null=True, default=None, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    code = models.ForeignKey(Code, on_delete=models.SET_NULL, null=True, default=None, related_name="applications")
    scoring = models.ForeignKey(
        CodeScore, on_delete=models.SET_NULL, null=True, default=None, related_name="applications"
    )

    feature_class_prediction_certainty = models.FloatField(
        null=True, default=None, validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    objects = ObservedFeatureQuerySet.as_manager()

    def __str__(self):
        return f"<ObservedFeature feature='{self.feature.key}' from={self.clock_position_from} to={self.clock_position_to} certainty={self.feature_class_prediction_certainty} code={self.code} scoring={self.scoring} sub_features={self.sub_features.all()}>"

    def append_sub_feature(
        self,
        *,
        sub_feature: SubFeature,
        numeric_value: float | None = None,
        numeric_range_min: float | None = None,
        numeric_range_max: float | None = None,
        numeric_unit: SubFeatureUnit | None = None,
        selected_option: SubFeatureOption | None = None,
        prediction_certainty: float | None = None,
    ) -> "ObservedSubFeature":
        if self.sub_features.filter(sub_feature=sub_feature).exists():
            log.warning(
                "Attempted to append a sub-feature that already exists for the observed feature",
                extra={
                    "observed_feature_id": self.id,
                    "sub_feature_id": sub_feature.id,
                },
            )
            raise ValueError("The given sub-feature already exists for this observed feature")
        if sub_feature.feature != self.feature:
            log.warning(
                "Attempted to append a sub-feature that does not belong to the given feature",
                extra={
                    "observed_feature_id": self.id,
                    "sub_feature_id": sub_feature.id,
                    "feature_id": self.feature.id,
                },
            )
            raise ValueError("Sub-feature does not belong to the correct feature")
        return self.sub_features.create(
            sub_feature=sub_feature,
            numeric_value=numeric_value,
            numeric_range_min=numeric_range_min,
            numeric_range_max=numeric_range_max,
            numeric_unit=numeric_unit,
            selected_option=selected_option,
            sub_feature_class_prediction_certainty=prediction_certainty,
        )

    def as_feature_value(self) -> FeatureValue:
        sub_feat_vals = [sub_feat.as_sub_feature_value() for sub_feat in self.sub_features.all()]
        flags = FlagValues(
            at_joint=self.observation.at_joint,
            loss_of_vision=self.observation.has_loss_of_vision,
            has_textbox=self.observation.has_textbox,
            has_title=self.observation.has_title,
        )
        return FeatureValue(
            feature_id=self.feature_id,
            sub_features=sub_feat_vals,
            clock_position_from=self.clock_position_from,
            clock_position_to=self.clock_position_to,
            start_chainage=self.observation.start_chainage,
            end_chainage=self.observation.end_chainage,
            is_continuous=self.observation.is_continuous,
            remarks=self.observation.remarks,
            flag_values=flags,
        )


class ObservedSubFeatureQuerySet(models.QuerySet):
    def prefetch_parents(self):
        return self.select_related(
            "observed_feature",
            "observed_feature__observation",
            "observed_feature__observation__inspection",
        )


class ObservedSubFeature(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    observed_feature = models.ForeignKey(ObservedFeature, on_delete=models.CASCADE, related_name="sub_features")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="observed_instances")

    numeric_value = models.FloatField(null=True, default=None)
    numeric_range_min = models.FloatField(null=True, default=None)
    numeric_range_max = models.FloatField(null=True, default=None)
    numeric_unit = models.CharField(choices=SubFeatureUnit.as_choices(), max_length=10, null=True, default=None)

    selected_option = models.ForeignKey(
        SubFeatureOption, on_delete=models.SET_NULL, null=True, default=None, related_name="observed_instances"
    )
    """
    The option selected for this sub-feature. Only applicable to categorical sub-features.
    """

    sub_feature_class_prediction_certainty = models.FloatField(
        null=True, default=None, validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    objects = ObservedSubFeatureQuerySet.as_manager()

    def __str__(self):
        return f"<ObservedSubFeature sub_feature={self.sub_feature} val={self.numeric_value} min={self.numeric_range_min} max={self.numeric_range_max} unit={self.numeric_unit} selected_option={self.selected_option} certainty={self.sub_feature_class_prediction_certainty}>"

    def as_sub_feature_value(self) -> SubFeatureValue:
        return SubFeatureValue(
            sub_feature_id=self.sub_feature_id,
            numeric_value=self.numeric_value,
            numeric_range_min=self.numeric_range_min,
            numeric_range_max=self.numeric_range_max,
            numeric_unit=SubFeatureUnit(self.numeric_unit) if self.numeric_unit else None,
            option_id=self.selected_option_id,
        )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["observed_feature", "sub_feature"],  # No duplicate sub-features for the same observed feature
                name="unique_observed_sub_feature_per_feature",
            ),
            models.CheckConstraint(
                check=~(
                    Q(numeric_unit__isnull=True, numeric_value__isnull=False)
                    | Q(numeric_unit__isnull=True, numeric_range_min__isnull=False)
                    | Q(numeric_unit__isnull=True, numeric_range_max__isnull=False)
                ),
                name="observed_sub_feature_unit_required_if_numeric_value_or_range_set",
            ),
            models.CheckConstraint(
                check=~(
                    Q(selected_option__isnull=False, numeric_value__isnull=False)
                    | Q(selected_option__isnull=False, numeric_range_min__isnull=False)
                    | Q(selected_option__isnull=False, numeric_range_max__isnull=False)
                ),
                name="observed_sub_feature_cannot_have_numeric_value_or_range_if_option_selected",
            ),
            models.CheckConstraint(
                check=(
                    Q(numeric_value__isnull=False)
                    | Q(selected_option__isnull=False)
                    | Q(numeric_range_min__isnull=False)
                    | Q(numeric_range_max__isnull=False)
                ),
                name="observed_sub_feature_requires_numeric_or_categorical_value",
            ),
        ]


@receiver(post_save, sender=CustomUser)
def user_post_save(sender, instance, created, *args, **kwargs):
    if created and instance.organisation:
        InspectionFilter.objects.get_or_create(user=instance, organisation=instance.organisation)


@receiver(m2m_changed, sender=AssetOwners.contractor.through)
def linked_org_post_save(sender, instance, action, *args, **kwargs):
    if action == "post_add":
        link_record = AssetOwners.contractor.through.objects.filter(assetowners=instance).order_by("-id").first()
        contractor_org = Organisations.objects.get(id=Contractors.objects.get(id=link_record.contractors_id).org.id)
        asset_owner_org = Organisations.objects.get(id=instance.org.id)
        contractor_users = CustomUser.objects.filter(organisation=contractor_org)

        for user in contractor_users:
            InspectionFilter.objects.get_or_create(user=user, organisation=asset_owner_org)


class MapPointList(models.Model):
    # TODO we need a better solution here for storing enums.
    PLANNED_STATUS = "Planned"

    STATUS_CHOICE_LIST = [
        "Planned",
        "Uploaded",
        "Reviewed",
        "Decision",
        "Repair Plan",
        "Actioned",
        "Complete",
        "Archived",
    ]

    STATUS_CHOICES = [
        ("Planned", "Planned"),
        ("Uploaded", "Uploaded"),
        ("Reviewed", "Reviewed"),
        ("Decision", "Decision"),
        ("Repair Plan", "Repair Plan"),
        ("Actioned", "Actioned"),
        ("Complete", "Complete"),
        ("Archived", "Archived"),
    ]

    DIRECTION_CHOICES = [
        ("Downstream", "Downstream"),
        ("Upstream", "Upstream"),
        ("Unknown", "Unknown"),
    ]

    name = models.CharField(max_length=200, blank=False)
    geometry = geomodels.PointField(blank=True, null=True)
    chainage = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
        default=0.0,
        blank=False,
        null=False,
    )
    chainage_unit = models.CharField(max_length=10, default="m", blank=True, null=True)
    diameter = models.CharField(max_length=10, default="0", blank=True, null=True)
    condition_rating = models.IntegerField(choices=list(zip(range(1, 10), range(1, 10))), null=True)
    service_condition_rating = models.IntegerField(choices=list(zip(range(1, 5), range(1, 5))), null=True)
    date_captured = models.DateField(blank=True, null=True)
    asset_id = models.CharField(max_length=100, blank=True, null=True)
    first_frame = models.CharField(max_length=1000, blank=True, null=True)
    associated_file = models.OneToOneField(FileList, on_delete=models.SET_NULL, null=True)
    material = models.CharField(max_length=100, blank=True, null=True)
    start_node = models.CharField(max_length=100, blank=True, null=True)
    end_node = models.CharField(max_length=100, blank=True, null=True)
    direction = models.CharField(max_length=100, choices=DIRECTION_CHOICES, null=True)
    upstream_node = models.CharField(max_length=100, blank=True, null=True)
    downstream_node = models.CharField(max_length=100, blank=True, null=True)
    process_model_id = models.CharField(max_length=100, blank=True, null=True)
    cr_model_id = models.CharField(max_length=1000, blank=True, null=True)
    ser_cr_model_id = models.CharField(max_length=1000, blank=True, null=True)
    pipe_type = models.CharField(max_length=10, default="SS")
    is_accepted = models.BooleanField(default=False)
    reviewed_by = models.CharField(max_length=200, blank=False, null=True)
    review_timestamp = models.DateTimeField(blank=True, null=True)
    water_level_warning = models.BooleanField(blank=False, default=False)
    loss_of_vision_warning = models.BooleanField(blank=False, default=False)
    water_level_url = models.CharField(max_length=200, default="", blank=True)
    job = models.ForeignKey(Jobs, on_delete=models.SET_NULL, null=True)
    sewer_data = models.BooleanField(default=True, null=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="Uploaded")
    inspection_notes = models.CharField(max_length=1000, blank=True, null=True)
    ocr_error_detect = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    ocr_error_recog = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    ocr_used_azure = models.BooleanField(default=True, null=True)
    orig_str_grade = models.IntegerField(blank=True, null=True)
    orig_ser_grade = models.IntegerField(blank=True, null=True)
    repair_completed_date = models.DateField(blank=True, null=True)
    inspection = models.OneToOneField(Inspection, on_delete=models.SET_NULL, null=True, blank=True)
    deleted_at = models.DateTimeField(blank=True, null=True)

    def set_up_down_node(self, *args, **kwargs):
        if self.direction == "Upstream":
            self.upstream_node = self.end_node
            self.downstream_node = self.start_node

        elif self.direction == "Downstream":
            self.upstream_node = self.start_node
            self.downstream_node = self.end_node

    def set_start_end_node(self, *args, **kwargs):
        if self.direction == "Upstream":
            self.end_node = self.upstream_node
            self.start_node = self.downstream_node

        elif self.direction == "Downstream":
            self.start_node = self.upstream_node
            self.end_node = self.downstream_node

    def set_geometry(self, *args, **kwargs):
        self.geometry = Point(x=float(kwargs["longitude"]), y=float(kwargs["latitude"]))

    def get_inspection_with_standard_headers(self, *args, **kwargs):
        inspection = {}
        headers = StandardHeader.objects.filter(standard=self.standard_key)
        for header in headers:
            if header.header.mapped_mpl_field:
                inspection[header.name] = getattr(self, header.header.mapped_mpl_field)
        return inspection

    def validate(self):
        validation = {
            "is_valid": True,
            "inspection_id": self.id,
            "inspection_name": self.name,
            "errors": [],
        }
        is_valid = True
        standard = self.standard_key

        # --- INSPECTION VALIDATION ---

        # --- Common inspection validation checks ---

        # Retrieve all frames
        frames = (
            VideoFrames.objects.prefetch_related("parent_video", "defect_model", "defect_scores")
            .filter(
                parent_video=self.associated_file,
                is_hidden=False,
                defect_scores__isnull=False,
                defect_scores__is_shown=True,
            )
            .order_by("frame_id")
        )

        # if len(frames) > 0:
        #     # start_survey → exactly 1 present per inspection, must be the very first code
        #     if not frames.first().defect_scores.start_survey:
        #         error = CustomValidationError(
        #             title="Start Survey",
        #             message="First defect not marked as 'Start Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.first().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     if frames.filter(defect_scores__start_survey=True).count() > 1:
        #         error = CustomValidationError(
        #             title="Start Survey",
        #             message="Multiple defects marked as 'Start Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.first().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     # end_survey → exactly 1 present per inspection, must be the very last code
        #     if not frames.last().defect_scores.end_survey:
        #         error = CustomValidationError(
        #             title="End Survey",
        #             message="Finish node or Survey Abandoned code missing / is not the last code",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.last().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     if frames.filter(defect_scores__end_survey=True).count() > 1:
        #         error = CustomValidationError(
        #             title="End Survey",
        #             message="Multiple defects marked as 'End Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.last().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        if self.inspection is not None:
            validation["errors"].extend(self.inspection.validate())

        # --- ASSET VALIDATION ---

        if self.inspection is not None:
            asset = self.inspection.asset
            asset_values = AssetValue.objects.filter(asset=asset)
            validation["errors"].extend(standard.validate_asset(asset=asset, asset_values=asset_values))

        # --- DEFECT VALIDATION ---

        for frame in frames:
            validation["errors"].extend(frame.validate())

        is_valid = len(validation["errors"]) == 0
        validation["is_valid"] = is_valid
        return is_valid, validation

    def get_folder_id(self):
        if self.associated_file and self.associated_file.job_tree:
            return self.associated_file.job_tree.id
        elif self.inspection:
            return self.inspection.folder.id

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name_plural = "points"
        db_table = "service_mappointlist"
