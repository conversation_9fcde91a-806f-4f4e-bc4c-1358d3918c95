import uuid

from django.conf import settings
from django.core.validators import Min<PERSON><PERSON>ueValida<PERSON>, MaxValueValidator
from django.db import models

from api.files.models import FileList, VideoFrames
from api.organisations.models import Organisations


class ChainageUnit(models.TextChoices):
    METRES = "M", "Metres"
    FEET = "FT", "Feet"


class FootageManager(models.Manager):
    def create_for_org(
        self,
        org: Organisations,
        file: FileList | None = None,
        chainage_unit: ChainageUnit = ChainageUnit.METRES,
    ) -> "Footage":
        """
        Create a new Footage instance for the given organisation.
        """
        if file and file.target_org_id != org.id:
            raise ValueError("The provided file does not belong to the specified organisation.")
        footage = self.create(target_org=org, video_file=file, chainage_unit=chainage_unit)
        return footage


class FootageQuerySet(models.QuerySet):
    def prefetch_keyframe_video_frames(self):
        """
        Prefetch related keyframes and their video frames' details for this footage.
        """
        return self.prefetch_related(
            "keyframes",
            "keyframes__video_frame",
            "keyframes__video_frame__parent_video",
            "keyframes__video_frame__defect_scores",
            "keyframes__video_frame__parent_video__mappointlist",
        )

    def for_org(self, org: Organisations):
        """
        Filter to footage owned by a single organisation.
        """
        return self.filter(target_org=org)


class Footage(models.Model):
    """
    The logical representation of footage recorded during an inspection
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    video_file = models.ForeignKey(
        FileList, on_delete=models.SET_NULL, related_name="derived_footage", null=True, default=None
    )
    """
    The physical video file that this footage is derived from.
    Optional as we do not have a video file for some inspections (eg. newly imported).
    """

    chainage_unit = models.CharField(
        max_length=2,
        choices=ChainageUnit.choices,
        default=ChainageUnit.METRES,
    )

    target_org = models.ForeignKey(Organisations, on_delete=models.CASCADE, related_name="footage_set")
    """
    The organisation that this footage belongs to.
    """

    total_frames = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    objects = FootageManager.from_queryset(FootageQuerySet)()

    def __str__(self):
        return f"<Footage id='{self.id}' video__file={self.video_file} total_frames={self.total_frames} chainage_unit='{self.chainage_unit}'>"

    def clear_keyframes(self):
        self.keyframes.all().delete()
        self.total_frames = 0
        self.save(update_fields=["total_frames"])

    def append_new_keyframe(
        self,
        time_reference_milliseconds: int,
        chainage: float,
        is_hidden: bool = False,
        at_joint: bool = False,
        has_loss_of_vision: bool = False,
        has_textbox: bool = False,
        has_title: bool = False,
        video_frame: int | VideoFrames | None = None,
    ) -> "Keyframe":
        """
        Append a new keyframe to this footage.
        """
        if isinstance(video_frame, VideoFrames):
            video_frame = video_frame.id
        self.total_frames += 1
        kf = self.keyframes.create(
            video_frame_id=video_frame,
            time_reference_milliseconds=time_reference_milliseconds,
            sequence_number=self.total_frames,
            chainage=chainage,
            is_hidden=is_hidden,
            at_joint=at_joint,
            has_loss_of_vision=has_loss_of_vision,
            has_textbox=has_textbox,
            has_title=has_title,
        )
        self.save(update_fields=["total_frames"])
        return kf

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(chainage_unit__in=[v for v, _ in ChainageUnit.choices]),
                name="footage_allowed_chainage_units",
            )
        ]


class Keyframe(models.Model):
    """
    The logical representation of a moment in time in inspection footage
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    footage = models.ForeignKey(Footage, on_delete=models.CASCADE, related_name="keyframes")
    """
    The footage from which this key frame is taken.
    """

    video_frame = models.ForeignKey(
        VideoFrames, null=True, default=None, on_delete=models.SET_NULL, related_name="keyframes"
    )
    """
    The physical image this keyframe is derived from. Optional as we may not have a saved image for every keyframe.
    """

    time_reference_milliseconds = models.PositiveIntegerField()
    sequence_number = models.PositiveIntegerField()
    chainage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
    )
    """
    Distance from the start of the inspection. Unit is specified on the Footage.
    """

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    is_hidden = models.BooleanField(default=False)

    # TODO:
    # Should is_matched, is_accepted and reviewed_by be added here?

    # Flag fields:
    at_joint = models.BooleanField(default=False)
    has_loss_of_vision = models.BooleanField(default=False)
    has_textbox = models.BooleanField(default=False)
    has_title = models.BooleanField(default=False)

    def __str__(self):
        return (
            f"<Keyframe id='{self.id}' footage={self.footage_id} video_frame={self.video_frame_id} "
            f"time_reference_milliseconds={self.time_reference_milliseconds} "
            f"sequence_number={self.sequence_number} chainage={self.chainage}>"
        )

    class Meta:
        ordering = ["footage", "sequence_number"]
        constraints = [
            models.UniqueConstraint(
                fields=["footage", "sequence_number"], name="footage_keyframe_unique_sequence_number"
            ),
        ]
