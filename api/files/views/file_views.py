import json
import logging
import re
import time
import uuid
from datetime import datetime

import jsonschema
import requests
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.core.cache import caches
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q
from django.db.transaction import atomic
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiRequest, OpenApiParameter
from rest_framework import status
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied, ParseError
from rest_framework.filters import OrderingFilter
from rest_framework.generics import (
    ListCreateAPIView,
    RetrieveUpdateDestroyAPIView,
    CreateAPIView,
    UpdateAPIView,
    RetrieveAPIView,
    ListAPIView,
    RetrieveUpdateAPIView,
)
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from vapar.constants.processing import ProcessingStatusEnum

from api.actions.models import AuditList
from api.common.enums import UserLevelEnum
from api.common.pagination import StandardResultsSetPagination
from api.common.permissions import (
    IsAuthenticated,
    OrgCanUpload,
    IsStandardUser,
    HasInspectionAccess,
    HasOrganisationAccess,
    IsServiceUser,
    HasAccessToOrgScopedObject,
    IsProductOwner,
    HasFileAccess,
)
from api.common.storage import (
    get_processing_storage_sas_token,
    get_processing_storage_region_base_url,
    get_platform_storage_sas_token,
    get_platform_blob_url_with_sas,
    get_processing_blob_client,
)
from api.files.models import ProcessingList, JobsTree, FileList
from api.files.serializers import (
    ProcessingInspectionsListSerializer,
    ProcessingFileCreateSerializer,
    ProcessingFilePatchSerializer,
    FileSerializer,
    FileCreateSerializer,
    ProcessingFileRetrySerializer,
    InspectionFilePatchSerializer,
    InspectionFileUploadMediaSerializer,
)
from api.inspections import schemas
from api.inspections.models import  Inspection
from api.inspections.permissions import IsOrgOwnerOfProcessingFile, IsOrgUploaderOfProcessingFile
from api.inspections.queue_trigger import enqueue_message
from api.organisations.models import Organisations
from api.users.models import CustomUser

log = logging.getLogger(__name__)

sas_cache = caches["default"]
inspection_cache = caches["inspections"]


class ProcessingFileListCreate(ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProcessingInspectionsListSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ProcessingList.objects.all().order_by("-created_time")
    pagination_class = StandardResultsSetPagination

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["associated_file_id"]

    def list(self, request, *args, **kwargs):
        """
        Get a list of processing files for the organisation.

        If the user is an upload only user, only files uploaded by the user are returned.
        """
        queryset = self.get_queryset()
        is_standard_user = request.user.user_level in ("standard", "Standard")

        # Filter - based on user type
        if is_standard_user:
            # Get all processing files for org if a standard user
            self.queryset = queryset.filter(
                Q(target_org=request.organisation) | Q(associated_file__upload_org=request.organisation)
            )
        else:
            # Get only my uploaded files if an upload only user
            self.queryset = queryset.filter(associated_file__uploaded_by=request.user)

        return super().list(request, *args, **kwargs)

    @extend_schema(
        request=OpenApiRequest(ProcessingFileCreateSerializer()),
    )
    def post(self, request, *args, **kwargs):
        """
        Create a ProcessingList record.

        This differs from the files/upload endpoint in that the file should already exist
        """

        serializer = ProcessingFileCreateSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)
        processing_record = serializer.save()

        AuditList.objects.create(
            event_type="Create",
            table="ProcessingList",
            row_id=processing_record.id,
            column="all",
            description=f"Create processing file {processing_record.filename} independent of upload",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        response_serializer = ProcessingInspectionsListSerializer(processing_record, context={"request": request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


class ProcessingFileDetail(RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsOrgOwnerOfProcessingFile | IsOrgUploaderOfProcessingFile]
    serializer_class = ProcessingInspectionsListSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ProcessingList.objects.all()

    http_method_names = ["patch", "delete"]
    lookup_field = "id"

    @extend_schema(
        request=OpenApiRequest(ProcessingFilePatchSerializer),
        responses={status.HTTP_204_NO_CONTENT: None},
    )
    def patch(self, request, id):
        processing_record: ProcessingList = self.get_object()
        serializer = ProcessingFilePatchSerializer(processing_record, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        if serializer.data.get("upload_completed"):
            description = f"Processing file upload complete {processing_record.associated_file.filename}"
            AuditList.objects.create(
                event_type="Update",
                table="ProcessingList",
                row_id=processing_record.id,
                column="Multi",
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)

    def delete(self, request, id):
        """
        Delete a processing file.
        """
        instance: ProcessingList = self.get_object()

        file_exists = False
        if instance.associated_file is not None and instance.associated_file.processing_completed_time is None:
            if instance.status in (ProcessingStatusEnum.PROCESSING, ProcessingStatusEnum.STORING_RESULTS):
                instance.associated_file.hidden = True
                instance.associated_file.save()
                file_exists = True
            else:
                instance.associated_file.delete()
        instance.delete()

        if instance.associated_file:
            if instance.associated_file.job_tree:
                folder = instance.associated_file.job_tree.id
            else:
                folder = "N/A - No associated folder object"
        else:
            folder = "N/A - No associated file object"

        if file_exists and len(Inspection.objects.filter(file=instance.associated_file)) > 0:
            inspection_status = Inspection.objects.filter(file=instance.associated_file).first().status
        else:
            inspection_status = "N/A - No associated inspection object"

        if instance.status_reason:
            fail_reason = f"Fail reason: {instance.status_reason}\n"
        else:
            fail_reason = ""

        if len(CustomUser.objects.filter(full_name=instance.upload_user, organisation=instance.target_org)) > 0:
            uploaded_by = (
                CustomUser.objects.filter(full_name=instance.upload_user, organisation=instance.target_org).first().id
            )
        else:
            uploaded_by = "N/A - No associated user object"

        description = (
            "Delete a processing file:\n"
            f"File name: {instance.filename}\n"
            f"Folder: {folder}\n"
            f"Inspection status: {inspection_status}\n"
            f"Processing status: {instance.status}\n"
            f"{fail_reason}"
            f"Created date: {instance.created_time}\n"
            f"Uploaded by: {uploaded_by}\n"
        )

        AuditList.objects.create(
            event_type="Delete",
            table="JobTree",
            row_id=id,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        response = {"detail": "Successfully deleted"}
        return Response(response)


class InspectionFileList(CreateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload]
    serializer_class = FileSerializer

    @extend_schema(
        request=OpenApiRequest(schemas.file_upload_request_schema),
        responses={status.HTTP_202_ACCEPTED: dict},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a FileList record.
        """
        job_id = request.data["job_id"]
        file_name = request.data["file_name"].replace("#", "")
        upload_file_name = request.data["upload_file_name"].replace("#", "")

        if not upload_file_name:
            raise ValidationError("Must send an upload file name with prepended date")

        job = JobsTree.objects.filter(id=job_id).first()

        if job is None:
            raise NotFound("Job not found")

        top_job = job.get_root()
        upload_org = request.organisation

        if not job.can_contain_inspections:
            raise PermissionDenied(
                "Cannot upload to the root folder, Recycle Bin, or folders that contain other folders"
            )

        if not job.allows_uploads_from(upload_org):
            raise PermissionDenied("Your organisation does not have permission to upload to this folder")

        target_org = top_job.primary_org
        file_url = f"{settings.BLOB_STORAGE_VIDEOS_CONTAINER}/{upload_file_name}"

        user_full_name = f"{request.user.first_name} {request.user.last_name}"
        file_size = 0

        file_data = {
            "filename": file_name,
            "file_type": "",
            "file_size": file_size,
            "url": file_url,
            "target_org": target_org.id,
            "upload_org": upload_org.id,
            "upload_user": user_full_name,
            "job_tree": job.id,
            "uploaded_by": request.user.id,
            "storage_region": target_org.country.code,
        }
        serializer = FileSerializer(data=file_data)
        serializer.is_valid(raise_exception=True)

        new_file = FileList.objects.create(**serializer.validated_data)

        description = "Created a new row into FileList table from platform user"
        now = timezone.now()

        AuditList.objects.create(
            event_type="Create",
            table="FileList",
            row_id=new_file.id,
            column="all",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        processing_file_data = {
            "filename": file_name,
            "file_size": file_size,
            "status": "Uploading",
            "created_time": str(datetime.now()),
            "upload_user": user_full_name,
            "target_org": target_org.id,
            "associated_file": new_file.id,
            "manual_qa_required": target_org.manual_qa_required,
            "sewer_data": job.pipe_type_sewer,
            "standard_key": job.standard_key.id,
            "status_reason": None,
            "times_retried": 0,
        }

        serializer = ProcessingInspectionsListSerializer(data=processing_file_data)
        serializer.is_valid(raise_exception=True)

        processing_file = ProcessingList.objects.create(**serializer.validated_data)

        description = "Created a new row into ProcessingList table from platform user"
        now = timezone.now()

        AuditList.objects.create(
            event_type="Create",
            table="ProcessingList",
            row_id=processing_file.id,
            column="all",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        blob_location = request.data.get("location", "")

        if blob_location:
            container = blob_location[: blob_location.find("/")]
        else:
            container = settings.BLOB_STORAGE_VIDEOS_CONTAINER

        sas_token = get_processing_storage_sas_token(container, write_access=True, region=target_org.country.code)
        base_url = get_processing_storage_region_base_url(region=target_org.country.code)
        base_url_with_sas = f"{base_url}?{sas_token}"

        log.info(f"Using container: {container}")
        log.info(f"Using base url with sas: {base_url_with_sas}")

        return Response(
            {
                "file_id": new_file.id,
                "processing_file_id": processing_file.id,
                "blob_url_with_sas": base_url_with_sas,
                "upload_file_name": upload_file_name,
            },
            status=status.HTTP_202_ACCEPTED,
        )


class InspectionFileUploadComplete(UpdateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload]
    queryset = FileList.objects.filter(hidden=False)
    http_method_names = ["patch"]

    @extend_schema(
        request=None,
        responses={status.HTTP_200_OK: dict},
    )
    def patch(self, request, pk):
        """
        Signal completion of upload. Blocks until the file is found in storage.
        """

        # This function is essentially a no-op.
        # The status change and enqueueing should be handled by the file blob trigger.

        # At least verify the file record exists and the user has access to it
        _file_object: FileList = self.get_object()
        return Response(
            {"success": "Upload is complete. The file has been queued for processing"},
            status=status.HTTP_200_OK,
        )


class FileDownload(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("inspectionId", OpenApiTypes.INT, required=True),
        ],
        responses={status.HTTP_200_OK: schemas.file_download_response_schema},
    )
    def get(self, request, *args, **kwargs):
        """
        Get the download details for a FileList record.
        """
        inspection_id = request.query_params.get("inspectionId")

        try:
            uuid.UUID(inspection_id, version=4)
            inspection = get_object_or_404(Inspection.objects.all(), uuid=inspection_id)
        except ValueError:
            inspection = get_object_or_404(Inspection.objects.all(), legacy_id=inspection_id)

        if (file := inspection.file) is not None:
            if (
                file.target_org == request.organisation
                or file.upload_org == request.organisation
                or inspection.file.uploaded_by == request.user
            ):
                cache_key = f"{file.id}:{settings.BLOB_STORAGE_VIDEOS_CONTAINER}"
                if (sas_token := sas_cache.get(cache_key)) is None:
                    sas_token = get_platform_storage_sas_token(
                        container_name=settings.BLOB_STORAGE_VIDEOS_CONTAINER, region=file.storage_region
                    )
                    sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)
                resp = {
                    "name": file.filename,
                    "url": get_platform_blob_url_with_sas(
                        blob_path=file.url, sas_token=sas_token, region=file.storage_region
                    ),
                    "play": get_platform_blob_url_with_sas(
                        blob_path=file.play_url, sas_token=sas_token, region=file.storage_region
                    ),
                }
                return Response(resp, status=status.HTTP_200_OK)

            raise PermissionDenied("Access Denied. User does not have access to this file.")

        return Response(None, status=status.HTTP_404_NOT_FOUND)


class BulkDeleteFiles(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    request_schema = schemas.base_bulk_inspection_request_schema

    def update_audit_list(self, request, description: str, table: str) -> None:
        """
        Update the audit list with the export event.
        """
        AuditList.objects.create(
            event_type="Bulk delete",
            table=table,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

    # OpenApi3 doesn't permit body on DELETE requests so the schema on this is not generated correctly
    @extend_schema(
        request=OpenApiRequest(schemas.base_bulk_inspection_request_schema),
        responses={status.HTTP_200_OK: str},
    )
    def delete(self, request):
        """
        Bulk delete FileList records
        """

        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection_ids = request.data["inspection_ids"]
        inspections = Inspection.objects.filter(uuid__in=inspection_ids)

        for inspection in inspections:
            self.check_object_permissions(request, inspection)
            inspection_cache.delete(inspection.uuid)

            if inspection.file:
                processing_file = ProcessingList.objects.filter(associated_file=inspection.file).first()

                if processing_file:
                    processing_file_id = processing_file.id
                    processing_file.delete()
                    inspection.associated_file.delete()

                    processing_description = f"Processing file {processing_file_id} was deleted."
                    self.update_audit_list(request, processing_description, "ProcessingList")
                else:
                    inspection.file.hidden = True
                    inspection.file.save()

                filelist_description = f"File {inspection.file.id} was deleted."
                self.update_audit_list(request, filelist_description, "FileList")

            else:
                # if the record does not have an associated file, delete it
                inspection_description = f"Inspection {inspection.uuid} was deleted."
                inspection.delete()
                self.update_audit_list(request, inspection_description, "Inspection")

        return Response("success")


class FileListView(ListCreateAPIView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = StandardResultsSetPagination
    # We never want to show the user hidden files, if we need
    # them from an admin standpoint we can use AllFilesView
    queryset = FileList.objects.filter(hidden=False)

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = [
        "upload_completed_time",
        "created_time",
        "filename",
        "uploaded_by_id",
    ]
    ordering = ["-upload_completed_time", "id"]  # Default ordering is newest first

    filterset_fields = {
        "upload_completed": ["exact"],
        "filename": ["icontains", "exact"],
        "upload_completed_time": ["gte", "lte"],
        "created_time": ["gte", "lte"],
    }

    def get_serializer_class(self):
        if self.request.method == "POST":
            return FileCreateSerializer
        return FileSerializer

    def get_permissions(self):
        if self.request.method == "POST":
            return [IsAuthenticated(), HasOrganisationAccess(), IsServiceUser()]
        return [IsAuthenticated(), HasOrganisationAccess()]

    def get_queryset(self):
        qs = super().get_queryset()
        if isinstance(self.request.user, AnonymousUser):
            return qs.none()  # For schema introspection - real requests have authenticated users

        if self.request.user.is_asset_owner():
            qs = qs.filter(target_org=self.request.user.organisation)

        else:  # Case where contractor
            target_org_id = int(self.request.query_params.get("target_org_id"))
            qs = qs.filter(target_org=target_org_id, upload_org=self.request.user.organisation)

        if self.request.user.user_level == UserLevelEnum.UPLOAD_ONLY:
            qs = qs.filter(uploaded_by=self.request.user)

        include_processing = self.request.query_params.get("include_processing", "false").lower() == "true"
        if not include_processing:
            qs = qs.exclude(processing_completed_time__isnull=True)

        return qs

    def list(self, request, *args, **kwargs):
        target_org_id = request.query_params.get("target_org_id")
        if target_org_id is not None:
            self.check_object_permissions(request, get_object_or_404(Organisations, id=int(target_org_id)))
        return super().list(request, *args, **kwargs)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "target_org_id",
                OpenApiTypes.INT,
                required=False,
                description="The organisation whose files should be listed. Only required if user is a contractor",
            ),
            OpenApiParameter(
                "include_processing",
                OpenApiTypes.BOOL,
                required=False,
                description="Whether to include files that are currently being processed in the results.",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """
        Create a standalone video file record, allowing upload to occur at a later time.
        """
        return super().post(request, *args, **kwargs)


class ProcessingFileRetryView(APIView):
    """
    Retry processing of a file that has already been uploaded.
    """

    permission_classes = [IsAuthenticated, HasOrganisationAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "target_org_id",
                OpenApiTypes.INT,
                required=False,
                description="The organisation whose files should be listed. Only required if user is a contractor",
            ),
        ],
        request=None,
        responses={
            status.HTTP_200_OK: ProcessingFileRetrySerializer(),
            status.HTTP_400_BAD_REQUEST: ProcessingFileRetrySerializer(),
        },
    )
    def post(self, request, *args, **kwargs):
        if request.user.is_asset_owner():
            target_org = request.user.organisation
        else:
            target_org = get_object_or_404(Organisations, id=request.query_params.get("target_org_id"))
            self.check_object_permissions(request, target_org)

        file_id = kwargs.get("file_id")
        file = get_object_or_404(FileList, target_org=target_org, id=file_id)

        if not HasAccessToOrgScopedObject().has_object_permission(
            request, view=self, obj=file, org_field_name="target_org"
        ):
            raise PermissionDenied("User does not have access to the given file")

        try:
            processing_record = file.processing_record
        except ObjectDoesNotExist:
            return Response(
                status=status.HTTP_404_NOT_FOUND,
            )
        if not processing_record.is_retryable:
            return Response(
                ProcessingFileRetrySerializer(instance=processing_record).data,
                status=status.HTTP_400_BAD_REQUEST,
            )

        processing_record.retry()

        AuditList.objects.create(
            event_type="Update",
            table="ProcessingList",
            user_id=request.user.id,
            description=f"Processing of file {file_id} retried",
            column="Multi",
        )

        enqueue_message(file.url, region=file.storage_region)

        return Response(
            data=ProcessingFileRetrySerializer(instance=processing_record).data,
            status=status.HTTP_200_OK,
        )


class AllFilesView(ListAPIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = StandardResultsSetPagination
    serializer_class = FileSerializer

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = {
        "id": ["exact"],
        "filename": ["exact"],
        "url": ["exact"],
        "file_type": ["exact"],
        "created_time": ["lte", "gte"],
        "target_org_id": ["exact"],
        "upload_org_id": ["exact"],
        "uploaded_by_id": ["exact"],
        "job_id": ["exact"],
        "job_tree_id": ["exact"],
        "hidden": ["exact"],
        "upload_completed": ["exact"],
        "updated_at": ["lte", "gte"],
        "storage_region": ["exact"],
    }
    ordering_fields = [
        "filename",
        "url",
        "file_size",
        "created_time",
        "upload_completed_time",
    ]

    queryset = FileList.objects.all().select_related("job_tree")

    def get_queryset(self):
        qs = super().get_queryset()

        if statuses := self.request.query_params.get("status__in"):
            qs = qs.filter(processing_record__status__in=statuses.split(","))

        if storage_regions := self.request.query_params.get("storage_region__in"):
            qs = qs.filter(storage_region__in=storage_regions.split(","))

        return qs

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "status__in",
                OpenApiTypes.STR,
                required=False,
                description="Filter by a set of comma separated processing statuses",
            ),
            OpenApiParameter(
                "storage_region__in",
                OpenApiTypes.STR,
                required=False,
                description="Filter by a set of comma separated storage regions",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class InspectionFileDetail(RetrieveUpdateAPIView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get", "patch"]

    queryset = FileList.objects.all()
    lookup_field = "id"

    def get_queryset(self):
        return super().get_queryset().filter(target_org=self.request.organisation)

    def get_permissions(self):
        if self.request.method == "GET":
            return [IsAuthenticated(), HasAccessToOrgScopedObject()]
        return [IsAuthenticated(), HasAccessToOrgScopedObject(), IsServiceUser()]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return FileSerializer
        else:
            return InspectionFilePatchSerializer

    def check_object_permissions(self, request, obj):
        if not HasAccessToOrgScopedObject().has_object_permission(request, self, obj, "target_org"):
            raise PermissionDenied("User does not have access to retrieve or update file metadata")

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: None})
    def patch(self, request, *args, **kwargs):
        super().partial_update(request, *args, **kwargs)
        if request.data.get("upload_completed_time") or request.data.get("upload_completed"):
            instance = self.get_object()
            description = f"File upload complete {instance.filename}"
            AuditList.objects.create(
                event_type="Update",
                table="FileList",
                row_id=instance.id,
                column="upload_completed",
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)


class InspectionFileUploadMediaView(CreateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload, HasFileAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionFileUploadMediaSerializer
    queryset = FileList.objects.filter(hidden=False)

    lookup_field = "id"

    def get_queryset(self):
        return super().get_queryset().for_org(self.request.organisation)

    @extend_schema(responses={status.HTTP_200_OK: InspectionFileUploadMediaSerializer()})
    def post(self, request, *args, **kwargs):
        """
        Endpoint for uploading media files to an existing file record.
        """
        file_obj = self.get_object()
        serializer = self.get_serializer(instance=file_obj, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)


class ProcessingFileCheckView(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    schema = None

    def remove_domain_from_url(self, url):
        pattern = r"^https?://[^/]+(/.*)$"
        match = re.match(pattern, url)
        if match:
            return match.group(1).lstrip("/")
        return url

    def post(self, request):
        # Parse the incoming Event Grid event
        event_data = json.loads(request.body.decode("utf-8"))
        event = event_data[0]

        if event["eventType"] == "Microsoft.EventGrid.SubscriptionValidationEvent":
            requests.get(event["data"]["validationUrl"])
            return JsonResponse({"validationResponse": event["data"]["validationCode"]}, status=200)

        # Check if the eventType is Microsoft.Storage.BlobCreated
        if event["eventType"] == "Microsoft.Storage.BlobCreated":
            url = event["data"]["url"]
            url_path = self.remove_domain_from_url(url)

            # Query the ProcessingList for a matching file_name
            try:
                processing_item = (
                    ProcessingList.objects.filter(
                        associated_file__url=url_path, upload_completed=False, status="Waiting to process"
                    )
                    .order_by("-id")
                    .first()
                )

                if processing_item is None:
                    return JsonResponse(
                        {"message": f"ProcessingList entry not found for file {url}"},
                        status=404,
                    )

                file_item = FileList.objects.filter(
                    id=processing_item.associated_file.id, upload_completed=False
                ).first()
                if file_item is None:
                    return JsonResponse(
                        {"message": f"ProcessingList entry not found for file {url}"},
                        status=404,
                    )

                blob_client = get_processing_blob_client(
                    file_item.url,
                    container_name=settings.BLOB_STORAGE_VIDEOS_CONTAINER,
                    region=file_item.target_org.country,
                )

                retries = 3
                file_found_in_storage = False
                while retries:
                    if blob_client.exists():
                        file_found_in_storage = True
                        break
                    retries -= 1
                    time.sleep(5)

                if not file_found_in_storage:
                    processing_item.status = "Upload Failed"
                    processing_item.save()
                    return Response(
                        {"error": "Failed to find file in storage - Upload failed"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                file_item.upload_completed = True
                file_item.upload_completed_time = timezone.now()
                file_item.save()

                processing_item.status = "Waiting to process"
                processing_item.upload_completed = True
                processing_item.save()

                enqueue_message(file_item.url, region=file_item.storage_region)

                description = f"Azure BlobCreated webhook fired for {url}"

                AuditList.objects.create(
                    event_type="Update",
                    table="ProcessingList",
                    column="Multi",
                    row_id=processing_item.associated_file.id,
                    description=description,
                    date_of_modification=timezone.now(),
                )

                return JsonResponse({"message": f"ProcessingList updated for file {url}"}, status=200)
            except ProcessingList.DoesNotExist:
                return JsonResponse(
                    {"message": f"ProcessingList entry not found for file {url}"},
                    status=404,
                )
        else:
            return JsonResponse({"message": "Invalid event type"}, status=400)
