import logging

from django.conf import settings
from django.core.cache import caches
from django.db.models import Q
from django.db.transaction import atomic
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import Camel<PERSON><PERSON>J<PERSON>NParser
from djangorestframework_camel_case.render import CamelCaseJ<PERSON>NRenderer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiRequest
from rest_framework import serializers, status
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.filters import OrderingFilter
from rest_framework.generics import ListAPIView, CreateAPIView, UpdateAPIView
from rest_framework.response import Response

from api.actions.models import AuditList
from api.common.pagination import StandardResultsSetPagination
from api.common.permissions import (
    IsAuthenticated,
    IsStandardUser,
    RelatesToInspectionAccessibleByOrg,
    RelatesToVideoFileAccessibleByOrg,
    IsServiceUser,
)
from api.common.storage import get_platform_storage_sas_token
from api.defects.models import DefectScores
from api.files.models import VideoFrames, FileList
from api.files.serializers import (
    FramesListSerializer,
    VideoFrameCreateSerializer,
    FrameExtendedEditSerializer,
    FramesEditSerializer,
)
from api.inspections import analytics
from api.inspections.gradings import update_gradings
from api.inspections.models import MapPointList, Inspection

log = logging.getLogger(__name__)

sas_cache = caches["default"]
inspection_cache = caches["inspections"]


class FrameList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def list(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by legacy map point list id
        """
        is_asset_owner = request.organisation.is_asset_owner
        inspection_id = kwargs["inspection_id"]

        if not inspection_id:
            raise serializers.ValidationError("You need to supply an inspection id to fetch frames")

        reported_frames = self.request.query_params.get("reported_frames")

        if reported_frames in ["False", "false", None]:
            reported_frames = False
        else:
            reported_frames = True

        # check permissions [v2 logic]
        try:
            if is_asset_owner:
                inspection = MapPointList.objects.get(
                    associated_file__target_org=request.organisation, pk=inspection_id
                )
            else:
                inspection = MapPointList.objects.get(
                    associated_file__upload_org=request.organisation, pk=inspection_id
                )
        except MapPointList.DoesNotExist:
            raise PermissionDenied({"message": "You don't have permission to access"})

        # build query
        queryset = self.get_queryset()
        query_filter = Q(parent_video__mappointlist__id=inspection_id)

        if reported_frames:
            query_filter &= Q(is_hidden=False) & (Q(defect_scores__is_shown=True))

        self.queryset = queryset.filter(query_filter).order_by("frame_id", "chainage_number")

        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{inspection_id}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, region=inspection.associated_file.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        serializer = self.serializer_class(self.queryset, context={"sas_url": sas_token}, many=True)
        return Response(serializer.data)


class FrameList2(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, RelatesToInspectionAccessibleByOrg]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def get_queryset(self):
        qs = self.queryset.filter(parent_video__inspection=self.kwargs.get("uuid"))

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.only_reported()

        return qs

    def get_serializer_context(self):
        inspection = Inspection.objects.get(uuid=self.kwargs.get("uuid"))
        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{str(inspection.uuid)}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, inspection.file.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        context = super().get_serializer_context()
        context["sas_url"] = sas_token
        return context

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by inspection uuid
        """
        return super().get(request, *args, **kwargs)


class FrameListByVideo(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, RelatesToVideoFileAccessibleByOrg]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def get_queryset(self):
        qs = self.queryset.filter(parent_video=self.kwargs.get("id"))

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.only_reported()

        return qs

    def get_serializer_context(self):
        video = FileList.objects.get(id=self.kwargs.get("id"))
        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{str(video.id)}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, video.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        context = super().get_serializer_context()
        context["sas_url"] = sas_token
        return context

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by video id
        """
        return super().get(request, *args, **kwargs)


class FrameGlobalList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    pagination_class = StandardResultsSetPagination

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ["id", "frame_id", "created_at", "updated_at"]
    ordering = ["parent_video", "frame_id"]  # default ordering is to group by video

    queryset = (
        VideoFrames.objects.prefetch_related("parent_video", "parent_video__mappointlist", "defect_model")
        .with_defect_score_severity()
        .all()
    )

    def get_queryset(self):
        if self.request.organisation.is_contractor:
            qs = (
                super()
                .get_queryset()
                .filter(
                    Q(parent_video__target_org=self.request.organisation)
                    | Q(parent_video__upload_org=self.request.organisation)
                )
            )
        else:
            qs = super().get_queryset().filter(parent_video__target_org=self.request.organisation)

        if parent_video_ids := self.request.query_params.get("parent_video__in"):
            ids = parent_video_ids.split(",")
            qs = qs.filter(parent_video__in=ids)

        if pks := self.request.query_params.get("id__in"):
            ids = pks.split(",")
            qs = qs.filter(id__in=ids)

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.filter(is_hidden=False)
            qs = qs.filter(defect_scores__is_shown=True)

        return qs

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
            OpenApiParameter(
                "parent_video__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of video ids to filter by",
            ),
            OpenApiParameter(
                "id__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of ids to filter by",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of video frames which can span multiple videos or inspections.
        """
        return super().get(request, *args, **kwargs)


class VideoFramesCreateUpdateDestroy(CreateAPIView):
    permission_classes = [IsAuthenticated, IsServiceUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = VideoFrameCreateSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model", "defect_scores").all()
    pagination_class = None

    http_method_names = ["post", "delete", "patch"]

    @extend_schema(
        request=OpenApiRequest(VideoFrameCreateSerializer(many=True)),
        responses={status.HTTP_201_CREATED: VideoFrameCreateSerializer(many=True)},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a sequence of frames, for a video, clearing any existing frames.
        """
        frames = request.data
        if not isinstance(frames, list):
            raise ValidationError("Expected body as a list")
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        parent_video.videoframes_set.all().delete()

        seen_ids, all_frames_serializer_data = set(), []
        for frame in frames:
            if frame["frame_id"] in seen_ids:
                raise ValidationError(f"Duplicate id provided: {frame['frame_id']}")

            seen_ids.add(frame["frame_id"])
            serializer = self.serializer_class(data=frame, context={"request": request})
            serializer.is_valid(raise_exception=True)
            new_frame = serializer.save()
            all_frames_serializer_data.append({"id": new_frame.id, **serializer.data})

        return Response(all_frames_serializer_data, status=status.HTTP_201_CREATED)

    def delete(self, request, *args, **kwargs):
        """
        Delete all frames for a video.
        """
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        parent_video.videoframes_set.all().delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        request=OpenApiRequest(FrameExtendedEditSerializer(many=True)),
        responses={status.HTTP_200_OK: FrameExtendedEditSerializer(many=True)},
    )
    @atomic
    def patch(self, request, *args, **kwargs):
        """
        Patch the list of given frames for a video. Frames not provided will be unm
        """
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        # First validate provided data itself
        FrameExtendedEditSerializer(data=request.data, many=True, partial=True, context={"request": request}).is_valid(
            raise_exception=True
        )

        ids = [f.get("id") for f in request.data]
        frames_by_id = {f.id: f for f in parent_video.videoframes_set.filter(id__in=ids)}
        if len(frames_by_id) != len(ids):
            raise ValidationError("Invalid frame ids provided")
        for frame_data in request.data:
            frame = frames_by_id[frame_data["id"]]
            # Now validate and save each frame
            serializer = self.serializer_class(
                instance=frame, data=frame_data, partial=True, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

        resp_serializer = FrameExtendedEditSerializer(
            instance=parent_video.videoframes_set.all(), many=True, context={"request": request}
        )
        return Response(resp_serializer.data)


class FrameDetail(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesEditSerializer
    queryset = VideoFrames.objects.with_defect_score_severity().all()

    def update(self, request, *args, **kwargs):
        """
        Update a VideoFrame record.
        """
        frame = self.get_object()
        userobj = request.user
        is_asset_owner = request.organisation.is_asset_owner

        access_denied_response = Response(
            {"error": "Access denied. Frame does not belong to this user"},
            status=status.HTTP_403_FORBIDDEN,
        )

        if is_asset_owner and not frame.parent_video.target_org == request.organisation:
            return access_denied_response

        if not is_asset_owner and not frame.parent_video.upload_org == request.organisation:
            return access_denied_response

        at_joint = request.data.get("at_joint")

        # if at joint possible = False, make sure at_joint is False
        if at_joint and frame.defect_scores.at_joint_required is False:
            request.data["at_joint"] = False

        # ensure chainage is a number
        if "chainage_number" in request.data:
            chainage_number = request.data.get("chainage_number")
            if isinstance(chainage_number, str):
                try:
                    chainage_number = float(chainage_number)
                except ValueError:
                    chainage_number = 0.0
            request.data["chainage_number"] = chainage_number
            request.data["chainage"] = chainage_number

        description = f"Frame has been updated: {frame.id}\n"

        inspection = Inspection.objects.filter(file=frame.parent_video.id).first()
        str_grade = inspection.structural_grade
        ser_grade = inspection.service_grade

        response = super().update(request, *args, **kwargs)

        if "is_hidden" in request.data:
            str_grade, ser_grade = update_gradings(inspection)
            inspection_cache.delete(inspection.uuid)

        description += "\n".join([f"{k} updated to {v}" for k, v in request.data.items()])

        now = timezone.now()
        AuditList.objects.create(
            event_type="Update",
            table="VideoFrames",
            row_id=frame.id,
            column="Multi",
            description=description,
            date_of_modification=now,
            user=userobj,
        )

        response.data["inspection_str_grade"] = str_grade
        response.data["inspection_ser_grade"] = ser_grade

        return response


class FrameDefectUpdate(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FramesEditSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = VideoFrames.objects.all()

    @extend_schema(
        request=OpenApiRequest(
            {
                "type": "object",
                "properties": {
                    "defectId": {"type": "integer"},
                    "pageContext": {
                        "type": "object",
                        "properties": {
                            "viewMode": {"type": "string", "enum": ["all", "reported"]},
                        },
                    },
                },
                "required": ["defectId"],
            }
        ),
    )
    def patch(self, request, pk):
        """
        Update a defect on a single videoFrame
        """
        if "defect_id" not in request.data:
            raise ValidationError("No defect id provided")

        frame = get_object_or_404(VideoFrames.objects.prefetch_related("defect_scores", "parent_video"), pk=pk)
        existing_defect = frame.defect_scores

        defect = DefectScores.objects.filter(id=request.data["defect_id"]).first()

        if defect is None:
            return Response({"Error": "Defect not found"}, status=status.HTTP_404_NOT_FOUND)

        if request.user.is_asset_owner():
            if frame.parent_video.target_org_id != request.organisation.id:
                return Response(
                    {"Error": "Access denied. Please check if this frame belongs to this user."},
                    status.HTTP_403_FORBIDDEN,
                )
        else:
            if frame.parent_video.upload_org_id != request.organisation.id:
                return Response(
                    {"Error": "Access denied. Please check if this frame belongs to this user."},
                    status.HTTP_403_FORBIDDEN,
                )

        if not defect:
            return Response({"Error": "failure"}, status=status.HTTP_400_BAD_REQUEST)

        inspection = Inspection.objects.filter(file=frame.parent_video.id).first()

        # Remove non-required fields
        if not defect.clock_position_required:
            frame.at_clock = None

        if not defect.clock_spread_possible:
            frame.to_clock = None

        if not defect.at_joint_required:
            frame.at_joint = False

        new_classification = defect.defect_description
        defect_model = defect.defect_key

        if defect_model:
            new_classification = defect_model.name

        if frame.class_label != new_classification:
            description = f"Update defect from {str(frame.class_label)} to {str(defect.defect_description)}"
            if not frame.original_fixed_label:
                frame.original_fixed_label = frame.class_label
                frame.class_certainty = 0.00
            frame.class_label = new_classification
            frame.defect_model = defect_model
            frame.defect_scores = defect
            frame.fix_user = request.user.full_name

            frame.quantity1_value = defect.quantity_1_default_val
            frame.quantity2_value = defect.quantity_2_default_val
            frame.quantity1_units = defect.quantity_1_units
            frame.quantity2_units = defect.quantity_2_units

            frame.save()

            now = timezone.now()

            AuditList.objects.create(
                event_type="Update",
                table="VideoFrames",
                row_id=frame.id,
                column="Multi",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

            page_context = request.data.get("page_context", {})
            view_mode = page_context.get("view_mode")
            view_mode_enum = analytics.FramesPageViewModeEnum(view_mode) if view_mode in ("all", "reported") else None

            analytics.send_frame_defect_updated_event(
                frame=frame,
                user=request.user,
                owner_org=frame.parent_video.target_org,
                updater_org=request.organisation,
                updated_at=now,
                previous_defect=existing_defect,
                new_defect=defect,
                inspection_status=inspection.status if inspection else None,
                frames_page_view_mode=view_mode_enum,
            )

        frame.is_hidden = False
        frame.save()

        str_grade, ser_grade = update_gradings(inspection)
        inspection_cache.delete(inspection.uuid)

        serializer = FramesEditSerializer(frame)
        response = serializer.data

        response["inspection_str_grade"] = str_grade
        response["inspection_ser_grade"] = ser_grade
        response["defect_class"] = defect.defect_description
        response["defect_code"] = defect.defect_code
        response["defect_id"] = defect.id

        return Response(response, status=status.HTTP_200_OK)
