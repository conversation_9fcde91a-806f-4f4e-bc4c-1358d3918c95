from django.conf import settings
from django.db import migrations, models
import django.core.validators
import django.db.models.deletion
import treebeard.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("inspections", "0033_remove_observation_keyframes"),
    ]

    state_operations = [
        migrations.CreateModel(
            name='Jobs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_name', models.CharField(max_length=200)),
                ('Created_by', models.CharField(max_length=200)),
                ('Created_date', models.DateTimeField()),
                ('pipe_type_sewer', models.BooleanField(blank=True, null=True)),
                ('Organisation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='organisations.organisations')),
                ('standard_key', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='defects.standard')),
            ],
            options={
                'db_table': 'service_jobs',
            },
        ),
        migrations.CreateModel(
            name='JobsTree',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', treebeard.fields.PathField(max_length=255, unique=True)),
                ('depth', treebeard.fields.DepthField()),
                ('numchild', treebeard.fields.NumChildField(default=0)),
                ('job_name', models.CharField(max_length=200)),
                ('created_date', models.DateTimeField()),
                ('pipe_type_sewer', models.BooleanField(blank=True, null=True)),
                ('primary_org', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='root_folders', to='organisations.organisations')),
                ('secondary_org', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='secondary_org', to='organisations.organisations')),
                ('standard_key', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='defects.standard')),
            ],
            options={
                'db_table': 'service_jobstree',
            },
        ),
        migrations.CreateModel(
            name='FileList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(blank=True, max_length=200)),
                ('url', models.CharField(blank=True, max_length=1000)),
                ('file_size', models.CharField(blank=True, max_length=200)),
                ('file_type', models.CharField(blank=True, max_length=50)),
                ('created_time', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('upload_user', models.CharField(max_length=200)),
                ('total_frames', models.IntegerField(default=0)),
                ('hidden', models.BooleanField(default=False)),
                ('request_endpoint', models.CharField(blank=True, default='', max_length=500)),
                ('play_url', models.CharField(blank=True, max_length=1000)),
                ('storage_region', models.CharField(default='AU', max_length=20)),
                ('upload_completed', models.BooleanField(default=False)),
                ('upload_completed_time', models.DateTimeField(blank=True, null=True)),
                ('processing_started_time', models.DateTimeField(blank=True, null=True)),
                ('processing_completed_time', models.DateTimeField(blank=True, null=True)),
                ('job', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.jobs')),
                ('job_tree', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='jt', to='files.jobstree')),
                ('target_org', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='target_org', to='organisations.organisations')),
                ('upload_org', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='upload_org', to='organisations.organisations')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'service_filelist',
            },
        ),
        migrations.CreateModel(
            name='ResultsFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(blank=True, max_length=200)),
                ('file_location', models.CharField(blank=True, max_length=1000)),
                ('file_size', models.CharField(blank=True, max_length=200)),
                ('header_only', models.BooleanField(default=False)),
                ('organisation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='organisations.organisations')),
            ],
            options={
                'db_table': 'service_resultsfile',
            },
        ),
        migrations.AddField(
            model_name='filelist',
            name='results_file',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.resultsfile'),
        ),
        migrations.CreateModel(
            name='VideoFrames',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image_location', models.CharField(blank=True, max_length=200)),
                ('frame_id', models.IntegerField(default=0)),
                ('time_reference', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('chainage', models.CharField(blank=True, default='0', max_length=10, null=True)),
                ('chainage_number', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(settings.MAX_CHAINAGE)])),
                ('class_label', models.CharField(blank=True, max_length=200)),
                ('class_certainty', models.DecimalField(blank=True, decimal_places=2, max_digits=5)),
                ('duplicate_of', models.IntegerField(default=-1)),
                ('all_class_breakdown', models.CharField(blank=True, max_length=1000)),
                ('fix_user', models.CharField(blank=True, max_length=200, null=True)),
                ('original_fixed_label', models.CharField(blank=True, max_length=200, null=True)),
                ('is_accepted', models.BooleanField(default=False)),
                ('reviewed_by', models.CharField(max_length=200, null=True)),
                ('review_timestamp', models.DateTimeField(blank=True, null=True)),
                ('is_hidden', models.BooleanField(default=False)),
                ('at_joint', models.BooleanField(blank=True, default=None, null=True)),
                ('at_clock', models.IntegerField(blank=True, default=None, null=True)),
                ('to_clock', models.IntegerField(blank=True, default=None, null=True)),
                ('cont_defect_start', models.BooleanField(default=False)),
                ('cont_defect_end', models.DecimalField(blank=True, decimal_places=2, default=None, max_digits=10, null=True)),
                ('quantity1_value', models.DecimalField(blank=True, decimal_places=2, default=None, max_digits=10, null=True)),
                ('quantity1_units', models.CharField(blank=True, default=None, max_length=200, null=True)),
                ('quantity2_value', models.DecimalField(blank=True, decimal_places=2, default=None, max_digits=10, null=True)),
                ('quantity2_units', models.CharField(blank=True, default=None, max_length=200, null=True)),
                ('is_matched', models.BooleanField(blank=True, default=None, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('defect_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='defects.defectmodellist')),
                ('defect_scores', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='defects.defectscores')),
                ('parent_video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='files.filelist')),
            ],
            options={
                'db_table': 'service_videoframes',
                'ordering': ['frame_id'],
            },
        ),
        migrations.CreateModel(
            name='ResultsFileAssets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_filename', models.CharField(blank=True, max_length=200)),
                ('asset_id', models.CharField(blank=True, max_length=100, null=True)),
                ('start_node', models.CharField(blank=True, max_length=100, null=True)),
                ('end_node', models.CharField(blank=True, max_length=100, null=True)),
                ('direction', models.CharField(blank=True, max_length=100, null=True)),
                ('chainage', models.CharField(blank=True, default='0', max_length=10, null=True)),
                ('date_captured', models.DateField(blank=True, null=True)),
                ('associated_file', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.filelist')),
                ('results_file', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.resultsfile')),
            ],
            options={
                'db_table': 'service_resultsfileassets',
            },
        ),
        migrations.CreateModel(
            name='ResultsFileDefects',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('time_reference', models.CharField(blank=True, default='', max_length=20, null=True)),
                ('chainage_number', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('comment', models.CharField(blank=True, max_length=200)),
                ('defect_code', models.CharField(blank=True, max_length=200)),
                ('associated_video_frame', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.videoframes')),
                ('results_file_asset', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.resultsfileassets')),
            ],
            options={
                'db_table': 'service_resultsfiledefects',
            },
        ),
        migrations.CreateModel(
            name='ProcessingList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(blank=True, max_length=200)),
                ('file_size', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(blank=True, choices=[('uploading', 'UPLOADING'), ('waiting_to_process', 'WAITING_TO_PROCESS'), ('processing', 'PROCESSING'), ('processed', 'PROCESSED'), ('failed_to_process', 'FAILED_TO_PROCESS')], default='uploading', max_length=200)),
                ('status_reason', models.CharField(blank=True, choices=[('GE', 'GENERIC_ERROR'), ('FC', 'FILE_CORRUPTED'), ('TW', 'TIMEOUT_WHILE_WAITING'), ('TP', 'TIMEOUT_WHILE_PROCESSING')], default=None, max_length=2, null=True)),
                ('times_retried', models.PositiveIntegerField(default=0)),
                ('created_time', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('upload_user', models.CharField(max_length=200)),
                ('request_endpoint', models.CharField(blank=True, default='', max_length=500)),
                ('manual_qa_required', models.BooleanField(default=False)),
                ('sewer_data', models.BooleanField(default=True)),
                ('upload_completed', models.BooleanField(default=False)),
                ('associated_file', models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processing_record', to='files.filelist')),
                ('standard_key', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='defects.standard')),
                ('target_org', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='organisations.organisations')),
            ],
            options={
                'db_table': 'service_processinglist',
            },
        ),
        migrations.AddConstraint(
            model_name='videoframes',
            constraint=models.CheckConstraint(check=models.Q(('at_clock__gte', 1), ('at_clock__lte', 12), _connector='AND') | models.Q(('at_clock__isnull', True)), name='video_frame_at_clock_range_constraint'),
        ),
        migrations.AddConstraint(
            model_name='videoframes',
            constraint=models.CheckConstraint(check=models.Q(('to_clock__gte', 1), ('to_clock__lte', 12), _connector='AND') | models.Q(('to_clock__isnull', True)), name='video_frame_to_clock_range_constraint'),
        ),
    ]

    operations = []

    def apply_migration(self, project_state, schema_editor, collect_sql=False):
        # Apply only the state operations, not the database operations
        for operation in self.state_operations:
            operation.state_forwards(self.app_label, project_state)
        return project_state