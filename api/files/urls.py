from django.urls import path

from .views.file_views import (
    ProcessingFileListCreate,
    ProcessingFileDetail,
    InspectionFileList,
    InspectionFileUploadComplete,
    FileDownload,
    BulkDeleteFiles,
    FileListView,
    ProcessingFileRetryView,
    AllFilesView,
    InspectionFileDetail,
    InspectionFileUploadMediaView,
    ProcessingFileCheckView,
)
from .views.folder_tree_views import FolderList, FolderDetail, FolderFilter
from .views.frame_views import (
    FrameList,
    FrameList2,
    FrameListByVideo,
    FrameGlobalList,
    VideoFramesCreateUpdateDestroy,
    FrameDetail,
    FrameDefectUpdate,
)

urlpatterns = [
    path("inspections/<int:inspection_id>/frames", FrameList.as_view(), name="frame_list"),
    path("inspections2/<uuid:uuid>/frames", FrameList2.as_view(), name="frame_list_by_inspection_2"),
    path(
        "inspections/videos/<int:video_id>/frames",
        VideoFramesCreateUpdateDestroy.as_view(),
        name="video_frames_create_destroy",
    ),
    path("inspections/frames/<int:pk>", FrameDetail.as_view(), name="frame_detail"),
    path("inspections/frames", FrameGlobalList.as_view(), name="frame_global_list"),
    path(
        "inspections/frames/<int:pk>/update-defect",
        FrameDefectUpdate.as_view(),
        name="frame_detail",
    ),
    path("inspections/folders", FolderList.as_view()),
    path("inspections/folders/<int:id>", FolderDetail.as_view()),
    path("inspections/folders/search", FolderFilter.as_view()),
    path("inspections/bulk-delete", BulkDeleteFiles.as_view()),
    path("files", FileListView.as_view(), name="files_list"),
    path("files/processing/<int:id>", ProcessingFileDetail.as_view()),
    path("files/download", FileDownload.as_view(), name="file_download"),
    path("files/processing", ProcessingFileListCreate.as_view(), name="file_processing_list_create"),
    path("files/processing/<int:file_id>/retry", ProcessingFileRetryView.as_view()),
    path("files/upload", InspectionFileList.as_view(), name="file_upload"),
    path("files/<int:pk>/upload-complete", InspectionFileUploadComplete.as_view(), name="file_upload_complete"),
    path("files/<int:id>", InspectionFileDetail.as_view(), name="file_detail"),
    path("files/<int:id>/upload", InspectionFileUploadMediaView.as_view(), name="file_upload_media"),
    path("files/<int:id>/frames", FrameListByVideo.as_view(), name="frame_list_by_video_file"),
    path("files/all", AllFilesView.as_view(), name="all_files"),
    path(
        "processing/check",
        ProcessingFileCheckView.as_view(),
        name="processing_file_check",
    ),
]
