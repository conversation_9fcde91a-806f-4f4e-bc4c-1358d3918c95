from django.conf import settings

from vapar.core.standards import get_peak_score, get_mean_score

from api.files.models import VideoFrames
from api.inspections.models import Inspection
from api.inspections.pydantic_models.inspection_model import get_inspection_representation
from api.organisations.models import AssetOwners, Organisations
from api.common.encrypt_url import encrypt, build_encrypted_url


def MapPointLinkJSON(inspection: Inspection, user_org):
    assoc_file = "None"
    video_id = ""
    viduser = ""
    url = ""
    x = ""
    y = ""
    str_grade = inspection.structural_grade
    ser_grade = inspection.service_grade

    vid_file = inspection.file
    if vid_file is not None:
        assoc_file = vid_file.filename
        video_id = vid_file.pk
        viduser = vid_file.upload_user
        url = vid_file.url

    job_det = vid_file.job_tree
    if job_det is not None:
        job_id = job_det.pk
        job_name = job_det.job_name

    report_frames = VideoFrames.objects.filter(parent_video=vid_file, is_hidden=False)

    str_scores = list(map(int, report_frames.values_list("defect_scores__structural_score", flat=True)))
    ser_scores = list(map(int, report_frames.values_list("defect_scores__service_score", flat=True)))

    # same logic as shareable links
    vid_uid = encrypt(inspection.file.pk, inspection.file.target_org.pk)
    vid_url = build_encrypted_url(settings.VIDEO_DOMAIN, vid_uid)

    is_ao_exist = AssetOwners.objects.filter(org=user_org).exists()
    if is_ao_exist:
        related_contractor_count = user_org.assetowners.contractor.count()
        all_related_contractors = user_org.assetowners.contractor.all()
        all_contractor_orgs = Organisations.objects.filter(contractors__in=all_related_contractors).values_list(
            "id", flat=True
        )
    else:
        related_contractor_count = 0

    # If uploaded status and the files uploaded by contractor, hide pdfs for this asset_owner
    pdf_uid = encrypt(inspection.legacy_id, inspection.file.target_org.pk)

    if (
        related_contractor_count > 0
        and inspection.status == "Uploaded"
        and inspection.file.upload_org.pk in all_contractor_orgs
    ):
        pdf_url = ""
    else:
        pdf_url = build_encrypted_url(settings.PDF_DOMAIN, pdf_uid)

    details = get_inspection_representation(inspection=inspection, skip_cache=True)

    inspection_details = details.model_dump()

    ret_json = {
        "id": inspection.uuid,
        "name": str(inspection_details["location_street"]) + " " + str(inspection_details["location_town"]),
        "latitude": str(y),
        "longitude": str(x),
        "asset_id": inspection_details["asset_id"],
        "chainage": inspection_details["chainage"],
        "chainage_unit": inspection_details["chainage_unit"],
        "diameter": inspection_details["diameter"],
        "date_captured": inspection_details["date_captured"],
        "pipe_type": inspection_details["pipe_type"],
        "condition_rating": str_grade,
        "service_condition_rating": ser_grade,
        "str_peak": get_peak_score(str_scores),
        "str_mean": get_mean_score(str_scores, inspection_details["chainage"]),
        "ser_peak": get_peak_score(ser_scores),
        "ser_mean": get_mean_score(ser_scores, inspection_details["chainage"]),
        "material": inspection_details["material"],
        "upstream_node": inspection_details["upstream_node"],
        "downstream_node": inspection_details["downstream_node"],
        "direction": inspection_details["direction"],
        "reviewed_by": inspection_details["reviewed_by"],
        "review_timestamp": inspection_details["review_timestamp"],
        "video": assoc_file,
        "videourl": url,
        "video_user": viduser,
        "video_id": str(video_id),
        "video_has_results": (vid_file.results_file is not None),
        "job_id": job_id,
        "job_name": job_name,
        "sewer_data": inspection.folder.pipe_type_sewer,
        "standard_key": inspection.folder.standard_key.pk if inspection.folder.standard_key else None,
        "pdf_link": pdf_url,
        "video_link": vid_url,
        "status": inspection.status,
    }
    return ret_json


def VideoFrameJSON(frame):
    ret_json = {
        "id": str(frame.pk),
        "chainage": frame.chainage,
        "chainage number": float(frame.chainage_number) if frame.chainage_number is not None else None,
        "parent_video_id": frame.parent_video.pk,
        "standard_description": frame.defect_scores.defect_description
        if frame.defect_scores is not None
        else frame.class_label,
        "standard_code": frame.defect_scores.defect_code if frame.defect_scores is not None else "",
        "time_reference": frame.time_reference,
        "at_joint": frame.at_joint,
        "at_clock": frame.at_clock,
        "to_clock": frame.to_clock,
        "continuous_start": frame.cont_defect_start,
        "continuous_end": float(frame.cont_defect_end) if frame.cont_defect_end is not None else None,
        "quantity1_value": float(frame.quantity1_value) if frame.quantity1_value is not None else None,
        "quantity1_units": frame.quantity1_units,
        "quantity2_value": float(frame.quantity2_value) if frame.quantity2_value is not None else None,
        "quantity2_units": frame.quantity2_units,
        "defect_id": frame.defect_scores.pk if frame.defect_scores is not None else None,
        "structural_score": frame.defect_scores.structural_score if frame.defect_scores is not None else 0,
        "service_score": frame.defect_scores.service_score if frame.defect_scores is not None else 0,
    }
    if frame.time_reference == "":
        try:
            frame_no = int(frame.frame_id) - 1
            total_seconds = int(frame_no * 1.5)
            report_hours = int(total_seconds / 3600)
            report_minutes = int((total_seconds % 3600) / 60)
            report_seconds = int((total_seconds % 3600) % 60)
            report_ms = int(frame_no * 15 % 10)
            ret_json["time_reference"] = "{}:{}:{}.{}".format(
                report_hours, str(report_minutes).zfill(2), str(report_seconds).zfill(2), str(report_ms)
            )
        except Exception:
            print("Couldnt convert frame id to time ref: " + frame.frame_id)

    return ret_json
