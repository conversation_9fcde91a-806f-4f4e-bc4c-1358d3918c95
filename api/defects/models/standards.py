from typing import NamedTuple

import vapar.core.coding
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django_countries.fields import CountryField
import uuid

from vapar.constants.units import SubFeatureUnit, SubFeatureNumericKind

from api.base.models import Header
from api.defects.models.features import (
    SubFeatureKind,
    Feature,
    SubFeature,
    SubFeatureOption,
    get_unit_display_suffix,
)


def default_repair_param():
    return {
        "Minimum roots class": 15,
        "Debris build up class": 15,
        "Debris build up length m": 1,
        "Debris single instance class": 20,
        "Patch if score over length >=": 50,
        "Patch length for scoring m": 1,
        "Maximum number of patches over distance": 2,
        "Maximum distance for patch": 30,
        "Maximum number of patches in total": 3,
    }


class Standard(models.Model):
    name = models.CharField(max_length=200, blank=False)
    display_name = models.CharField(max_length=200, blank=False, default="Australia 2020")
    default_repair_param = models.JSONField(blank=True, null=True, default=default_repair_param)

    def __str__(self):
        return str(self.display_name)

    class Meta:
        db_table = "service_scoringstandardslist"


class StandardSubcategory(models.Model):
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    material_type = models.CharField(max_length=100, blank=True, null=True)
    region = CountryField(blank=True, null=True)
    comment = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return str(self.comment)

    class Meta:
        db_table = "service_standard_subcategory"


class _StandardHeaderDataType(models.TextChoices):
    NUMBER = "number", "Number"
    STRING = "string", "String"
    BOOLEAN = "boolean", "Boolean"
    OPTIONS = "options", "Options"
    DATE = "date", "Date"


class StandardHeader(models.Model):
    DataType = _StandardHeaderDataType

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    header = models.ForeignKey(Header, on_delete=models.CASCADE)
    standard = models.ForeignKey(Standard, on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, null=True)
    required = models.BooleanField()
    shown_by_default = models.BooleanField()
    description = models.TextField(blank=True, null=True)
    data_type = models.CharField(max_length=7, choices=DataType.choices, null=False)
    options_selections = models.JSONField(blank=True, null=False)
    options_description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def get_mapped_mpl_field(self):
        return self.header.mapped_mpl_field

    class Meta:
        db_table = "inspections_standardheader"
        constraints = [
            models.CheckConstraint(
                check=models.Q(data_type__in=list(_StandardHeaderDataType)),
                name="standard_header_data_type_allowed_constraint",
            )
        ]


class StandardSubFeature(models.Model):
    """
    A standard-specific representation of a sub-feature attached to a feature.
    """

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="sub_features")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="standardised_set")

    display_name = models.CharField(max_length=255)
    """
    The user-facing name that should be used for the sub-feature under this standard
    """

    display_order = models.PositiveIntegerField()
    """
    The order the sub-features should appear in when building a description string for a feature under a code
    """

    default_option = models.ForeignKey(
        SubFeatureOption, on_delete=models.CASCADE, related_name="default_for", null=True, default=None
    )

    numeric_min = models.FloatField(null=True, blank=True)
    """
    Min value for this sub-feature, inclusive
    """
    numeric_max = models.FloatField(null=True, blank=True)
    """
    Max value for this sub-feature, inclusive
    """
    numeric_unit = models.CharField(max_length=50, null=True, blank=True, choices=SubFeatureUnit.as_choices())
    """
    Unit that this standard uses by default for this feature - if blank, defaults to the base unit for this subfeature
    """

    numeric_default = models.FloatField(null=True, blank=True)
    numeric_display_decimal_places = models.PositiveSmallIntegerField(null=True, blank=True)
    """
    How many decimal places to round to when displaying this sub-feature. Zero to always display as an integer
    """

    quantity_field_number = models.PositiveSmallIntegerField(null=True, default=None)
    """
    Which 'quantity' field this sub feature should be used to populate in reports under this standard
    """
    characteristic_field_number = models.PositiveSmallIntegerField(null=True, default=None)
    """
    Which 'characteristic' field this sub feature should be used to populate in reports under this standard
    """

    is_report_percent_field = models.BooleanField(default=False)
    """
    Whether to treat this sub-feature as the 'Percentage' field in standardised reports
    """

    maps_numeric_ranges_to_labels = models.BooleanField(default=False)
    """
    Whether this sub feature uses range labels in its representation. Ie. Treating this numeric sub feature
    as categorical
    """

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"<StandardSubFeature: id={self.id} standard={self.standard} sub_feature={self.sub_feature} display_name='{self.display_name}'>"

    def get_default_numeric_unit(self) -> SubFeatureUnit | None:
        """
        Returns the default numeric unit for this sub-feature under this standard.
        If no specific unit is set, returns the base unit for the sub-feature.
        """
        if self.sub_feature.kind != SubFeatureKind.NUMERIC:
            return None
        if self.numeric_unit:
            return SubFeatureUnit(self.numeric_unit)

        kind = SubFeatureNumericKind(self.sub_feature.numeric_kind)
        return kind.get_base_unit()

    class Meta:
        constraints = [
            # Prevent mixing fields intended only for categorical or numerical variables
            models.CheckConstraint(
                name="standardsubfeature_numeric_xor_categorical",
                check=(
                    models.Q(default_option__isnull=True)  # Case where numerical
                    | models.Q(  # Case where categorical
                        default_option__isnull=False,
                        numeric_default__isnull=True,
                        numeric_display_decimal_places__isnull=True,
                        numeric_unit__isnull=True,
                        numeric_min__isnull=True,
                        numeric_max__isnull=True,
                        maps_numeric_ranges_to_labels=False,
                        is_report_percent_field=False,
                    )
                ),
            ),
        ]


class StandardSubFeatureOption(models.Model):
    """
    A standard-specific representation of an option available for a sub-feature.
    """

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="sub_feature_options")
    option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, related_name="standardised_set")
    display_code = models.CharField(max_length=10)
    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    created_at = models.DateTimeField(auto_now_add=True)


class StandardSubFeatureRangeLabel(models.Model):
    """
    A label to apply to a range on a numeric sub feature, under a standard.

    For cases where the underlying data is numeric, but the standard chooses to represent it as binned categories.
    """

    standard_sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="range_labels")
    display_code = models.CharField(max_length=10)
    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    numeric_range_min = models.FloatField()
    numeric_range_max = models.FloatField()

    created_at = models.DateTimeField(auto_now_add=True)


class CodeStandardSubcategory(models.Model):
    code = models.ForeignKey("Code", on_delete=models.CASCADE)
    standard_subcategory = models.ForeignKey("StandardSubcategory", on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_codestandardsubcategory"
        unique_together = ("code", "standard_subcategory")


class SubFeatureValDetails(NamedTuple):
    """Wrapper for passing a sub-feature value DTO alongside its corresponding sub-feature and option entities"""

    val: vapar.core.coding.SubFeatureValue
    sub_feature: SubFeature
    option: SubFeatureOption | None


class CodeType(models.TextChoices):
    STR_DEFECT = "STR", "Structural Defect"
    SER_DEFECT = "SER", "Service Defect"
    MISC = "MISC", "Miscellaneous"


class CodeQuerySet(models.QuerySet):
    def for_substandard(self, substd: StandardSubcategory):
        return self.filter(codestandardsubcategory__standard_subcategory=substd)

    def prefetch_requirements_related(self):
        """
        Prefetch entities related to the requirements of the codes
        """

        return self.prefetch_related(
            "requirements",
            "requirements__required_option",
            "requirements__required_option__standardised_set",
            "requirements__sub_feature",
            "requirements__sub_feature__standardised_set",
            "requirements__sub_feature__options",
        )

    def prefetch_scoring_related(self):
        """
        Prefetch entities needed for scoring codes within a substandard
        """

        return self.prefetch_related(
            "scores",
            "scores__standard_subcategories",
            "scores__requirements",
            "scores__requirements__sub_feature",
            "scores__requirements__sub_feature__standardised_set",
            "scores__requirements__required_option",
            "scores__requirements__required_option__standardised_set",
        )

    def get_applicable_list(self, feature_val: vapar.core.coding.FeatureValue) -> "list[Code]":
        """
        Filter the queryset to a list of codes that can be applied to the provided feature.

        Assumes the queryset is already filtered to the desired feature.
        """
        codes_by_id = {c.id: c for c in self}

        code_defs = [c.as_code_dto() for c in codes_by_id.values()]
        applicable_code_defs = vapar.core.coding.find_matching_codes(code_defs, feature_val)

        applicable_codes = [codes_by_id[c.code_id] for c in applicable_code_defs]
        return applicable_codes


class Code(models.Model):
    """
    A code that can be applied to a given feature if it meets its set of requirements
    """

    CodeType = CodeType

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="codes")
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="codes")

    code_type = models.CharField(choices=CodeType.choices, max_length=10)

    display_code = models.CharField(max_length=20)
    """The user facing and standard compliant identifier for the classification"""

    display_name = models.CharField(max_length=255)

    standard_subcategories = models.ManyToManyField(
        StandardSubcategory, through="CodeStandardSubcategory", related_name="codes"
    )
    """Which subcategories of the standard this code is applicable to"""

    at_joint_required = models.BooleanField(default=False)
    at_joint_allowed = models.BooleanField(default=True)

    continuous_required = models.BooleanField(default=False)
    continuous_allowed = models.BooleanField(default=True)

    percent_field_required = models.BooleanField(default=False)
    percent_field_allowed = models.BooleanField(default=True)

    remarks_required = models.BooleanField(default=False)

    clock_position_from_required = models.BooleanField(default=False)
    clock_position_from_min = models.SmallIntegerField(
        default=1, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    clock_position_from_max = models.SmallIntegerField(
        default=12, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    clock_position_to_required = models.BooleanField(default=False)
    clock_position_to_min = models.SmallIntegerField(
        default=1, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    clock_position_to_max = models.SmallIntegerField(
        default=12, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    is_start_code = models.BooleanField(default=False)
    """Whether this code is allowed to appear as the first code in an inspection"""
    is_end_code = models.BooleanField(default=False)
    """Whether this code is allowed to appear as the final code in an inspection"""

    created_at = models.DateTimeField(auto_now_add=True)

    objects = CodeQuerySet.as_manager()

    def __str__(self):
        return f"<Code: display_code='{self.display_code}' display_name='{self.display_name}'>"

    def build_description_from_values(
        self,
        standard_id: int,
        sub_feature_details: list[SubFeatureValDetails],
    ) -> str | None:
        sub_feature_parts: list[tuple[str, int]] = []
        for sub_feature_detail in sub_feature_details:
            part = _build_sub_feature_description_fragment(
                standard_id, sub_feature_detail.sub_feature, sub_feature_detail.val, sub_feature_detail.option
            )
            if part is None:
                continue
            sub_feature_parts.append(part)

        sub_feature_parts.sort(key=lambda x: x[1])
        sub_feature_descriptions = (x[0] for x in sub_feature_parts)

        all_parts = [self.display_code, self.display_name, *sub_feature_descriptions]
        joined = " - ".join(all_parts)
        return joined

    def as_code_dto(self) -> vapar.core.coding.Code:
        reqs = [req.as_requirement_dto() for req in self.requirements.all()]
        dto = vapar.core.coding.Code(
            code_id=self.id,
            feature_id=self.feature_id,
            standard_id=self.standard_id,
            code_type=self.code_type,  # type:ignore - Consists of the right literal values
            display_code=self.display_code,
            display_name=self.display_name,
            at_joint_required=self.at_joint_required,
            at_joint_allowed=self.at_joint_allowed,
            continuous_required=self.continuous_required,
            continuous_allowed=self.continuous_allowed,
            percent_field_required=self.percent_field_required,
            percent_field_allowed=self.percent_field_allowed,
            remarks_required=self.remarks_required,
            clock_position_from_required=self.clock_position_from_required,
            clock_position_from_min=self.clock_position_from_min,
            clock_position_from_max=self.clock_position_from_max,
            clock_position_to_required=self.clock_position_to_required,
            clock_position_to_min=self.clock_position_to_min,
            clock_position_to_max=self.clock_position_to_max,
            is_start_code=self.is_start_code,
            is_end_code=self.is_end_code,
            requirements=reqs,
        )

        return dto

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="code_code_type_allowed_vals",
                check=models.Q(code_type__in=[v for v, _ in CodeType.choices]),
            )
        ]


def _build_sub_feature_description_fragment(
    standard_id: int,
    sub_feature: SubFeature,
    sub_feature_value: vapar.core.coding.SubFeatureValue,
    option: SubFeatureOption | None,
) -> tuple[str, int] | None:
    """
    Returns a description fragment and its order relative to other fragments, or None if the sub-feature is not
    applicable to the sub-standard.
    """

    # Ensure the standardised sub-features are prefetched
    standardised_sf = next(
        (std_sf for std_sf in sub_feature.standardised_set.all() if std_sf.standard_id == standard_id),
        None,
    )
    if standardised_sf is None:
        return None  # This sub-feature is not applicable to the given standard

    def _format_num(num: float, dp: int | None) -> str:
        if dp is None:
            return str(num)
        else:
            return "{:.{}f}".format(num, dp)

    if sub_feature.kind == SubFeatureKind.NUMERIC:
        numeric_unit = SubFeatureUnit(sub_feature_value.numeric_unit)
        decimal_places = standardised_sf.numeric_display_decimal_places

        if sub_feature_value.numeric_value is not None:  # Single value
            formatted_num = _format_num(sub_feature_value.numeric_value, decimal_places)
        else:  # A range
            # TODO: Handle cases where using range labels (StandardSubFeatureRangeLabel)
            lower_bound = (
                _format_num(sub_feature_value.numeric_range_min, decimal_places)
                if sub_feature_value.numeric_range_min is not None
                else None
            )
            upper_bound = (
                _format_num(sub_feature_value.numeric_range_max, decimal_places)
                if sub_feature_value.numeric_range_max is not None
                else None
            )
            if lower_bound is None and lower_bound is None:
                raise ValueError("Neither end of the range is present")
            if lower_bound is None:
                formatted_num = "<" + upper_bound
            elif upper_bound is None:
                formatted_num = ">" + lower_bound
            else:
                formatted_num = f"{lower_bound}-{upper_bound}"

        formatted_with_suffix = formatted_num + get_unit_display_suffix(numeric_unit)

        return formatted_with_suffix, standardised_sf.display_order

    else:  # CATEGORICAL
        if option is None:
            return None
        # Ensure the standard sub feature options are prefetched
        standardised_option = next(
            (opt for opt in option.standardised_set.all() if opt.standard_id == standard_id),
            None,
        )
        if not standardised_option:
            return None  # No standard-specific representation for this option

        return standardised_option.display_name, standardised_sf.display_order


class CodeRequirement(models.Model):
    """
    A requirement for a code to be applicable to a feature
    """

    code = models.ForeignKey(Code, on_delete=models.CASCADE, related_name="requirements")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, null=True)
    required_option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, null=True)

    numeric_option_unit = models.CharField(choices=SubFeatureUnit.as_choices(), max_length=50, null=True)
    numeric_option_range_min = models.FloatField(null=True, blank=True)
    numeric_option_range_max = models.FloatField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def as_requirement_dto(self) -> vapar.core.coding.CodeRequirement:
        return vapar.core.coding.CodeRequirement(
            code_id=self.code_id,
            feature_id=self.code.feature_id,
            sub_feature_id=self.sub_feature_id,
            required_option_id=self.required_option_id,
            numeric_unit=SubFeatureUnit(self.numeric_option_unit),
            numeric_option_range_min=self.numeric_option_range_min,
            numeric_option_range_max=self.numeric_option_range_max,
        )

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="coderequirement_categorical_or_numeric_exclusive",
                check=(
                    models.Q(numeric_option_range_min__isnull=True, numeric_option_range_max__isnull=True)
                    | models.Q(required_option__isnull=True)
                ),
            )
        ]


class CodeScoreStandardSubcategory(models.Model):
    code_score = models.ForeignKey("CodeScore", on_delete=models.CASCADE)
    standard_subcategory = models.ForeignKey("StandardSubcategory", on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_codescorestandardsubcategory"
        unique_together = ("code_score", "standard_subcategory")


class CodeScoreQuerySet(models.QuerySet):
    def find_applicable_scores(self, feature_val: vapar.core.coding.FeatureValue) -> "list[CodeScore]":
        """
        Find all scores that are applicable to the given feature value. Assumes the queryset is already filtered
        to the desired code + feature.
        """
        scores_by_id = {s.id: s for s in self}
        score_defs = [s.as_score_dto() for s in scores_by_id.values()]

        applicable_score_defs = vapar.core.coding.find_matching_scoring(score_defs, feature_val)
        applicable_scores = [scores_by_id[s.code_score_id] for s in applicable_score_defs]
        return applicable_scores


class CodeScore(models.Model):
    """
    A set of scores that are applied to a given code under particular substandards, given that their ranges are met
    """

    standard_subcategories = models.ManyToManyField(
        StandardSubcategory, through="CodeScoreStandardSubcategory", related_name="code_scores"
    )

    code = models.ForeignKey(Code, on_delete=models.CASCADE, related_name="scores")

    service_score = models.FloatField(null=True, blank=True)
    structural_score = models.FloatField(null=True, blank=True)
    repair_priority = models.FloatField(null=True, blank=True)

    # Note that some PACP codes use clock position for determining score
    required_clock_position_min = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    required_clock_position_max = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    required_clock_position_min_spread = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    required_clock_position_max_spread = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    added_service_score_per_metre = models.FloatField(default=0.0)
    """The amount to add to the service score for each metre a continuous defect extends."""
    added_structural_score_per_metre = models.FloatField(default=0.0)
    """The amount to add to the structural score for each metre a continuous defect extends."""

    created_at = models.DateTimeField(auto_now_add=True)

    def as_score_dto(self) -> vapar.core.coding.CodeScore:
        reqs = [req.as_requirement_dto() for req in self.requirements.all()]
        return vapar.core.coding.CodeScore(
            code_score_id=self.id,
            code_id=self.code_id,
            service_score=self.service_score,
            structural_score=self.structural_score,
            required_clock_position_min=self.required_clock_position_min,
            required_clock_position_max=self.required_clock_position_max,
            required_clock_position_min_spread=self.required_clock_position_min_spread,
            required_clock_position_max_spread=self.required_clock_position_max_spread,
            added_service_score_per_metre=self.added_service_score_per_metre,
            added_structural_score_per_metre=self.added_structural_score_per_metre,
            requirements=reqs,
        )

    def __str__(self):
        return (
            f"<CodeScore: code='{self.code.display_code}' svc='{self.service_score}' str='{self.structural_score}'"
            f" rp='{self.repair_priority}'>"
        )


class CodeScoreRequirement(models.Model):
    """
    A requirement that must be met for a set of scores to be applicable to a code, under particular sub-standards
    """

    code_score = models.ForeignKey(CodeScore, on_delete=models.CASCADE, related_name="requirements")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, null=True)
    required_option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, null=True)

    numeric_option_unit = models.CharField(choices=SubFeatureUnit.as_choices(), max_length=50, null=True)
    numeric_option_min_breakpoint = models.FloatField(null=True, blank=True)
    """Min value for this sub-feature, inclusive"""
    numeric_option_max_breakpoint = models.FloatField(null=True, blank=True)
    """Max value for this sub-feature, inclusive"""

    created_at = models.DateTimeField(auto_now_add=True)

    def as_requirement_dto(self) -> vapar.core.coding.CodeScoreRequirement:
        return vapar.core.coding.CodeScoreRequirement(
            code_id=self.code_score.code_id,
            sub_feature_id=self.sub_feature_id,
            required_option_id=self.required_option_id,
            numeric_unit=SubFeatureUnit(self.numeric_option_unit),
            numeric_option_min_breakpoint=self.numeric_option_min_breakpoint,
            numeric_option_max_breakpoint=self.numeric_option_max_breakpoint,
        )
