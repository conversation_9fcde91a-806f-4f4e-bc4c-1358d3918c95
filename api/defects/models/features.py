from django.db import models
from django.db.models import Prefetch
from vapar.constants.units import SubFeatureUnit, SubFeatureNumericKind


class FeatureQuerySet(models.QuerySet):
    def prefetch_for_standard_id(self, standard_id: int):
        """
        Prefetch the standardised versions of sub-features and their options, filtered down to a specific standard.
        """
        from api.defects.models import standards

        return self.prefetch_related(
            Prefetch(
                "sub_features__standardised_set",
                queryset=standards.StandardSubFeature.objects.filter(standard_id=standard_id),
            ),
            Prefetch(
                "sub_features__options__standardised_set",
                queryset=standards.StandardSubFeatureOption.objects.filter(standard_id=standard_id),
            ),
        )


class Feature(models.Model):
    """
    A Feature is a top-level element that can be observed during an inspection.

    Features are standard-independent.
    """

    key = models.CharField(max_length=50, unique=True, editable=False)
    """A stable identifier for internal programmatic access"""

    display_name = models.CharField(max_length=255)
    """
    User facing name of the feature
    """

    created_at = models.DateTimeField(auto_now_add=True)

    objects = FeatureQuerySet.as_manager()


class SubFeatureKind(models.TextChoices):
    CATEGORICAL = "CAT", "Categorical"
    NUMERIC = "NUM", "Numeric"


def get_unit_display_suffix(unit: SubFeatureUnit) -> str:
    match unit:
        case SubFeatureUnit.PERCENTAGE:
            return "%"
        case SubFeatureUnit.METRES:
            return "m"
        case SubFeatureUnit.MILLIMETRES:
            return "mm"
        case SubFeatureUnit.FEET:
            return "ft"
        case SubFeatureUnit.INCHES:
            return "in"
        case SubFeatureUnit.COUNT:
            return ""
        case SubFeatureUnit.CUBIC_METRES:
            return "m³"
        case SubFeatureUnit.CUBIC_FEET:
            return "ft³"
        case SubFeatureUnit.DEGREES:
            return "°"
        case _:
            raise ValueError(f"Unexpected SubFeatureUnit: {unit}")


class SubFeature(models.Model):
    """
    A SubFeature is a categorical or numeric variable that can be applied to a feature.

    SubFeatures are standard-independent.
    """

    Kind = SubFeatureKind
    NumericKind = SubFeatureNumericKind

    key = models.CharField(max_length=50, unique=True, editable=False)
    """
    A stable identifier for internal programmatic access
    """

    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    feature = models.ForeignKey(Feature, on_delete=models.PROTECT, related_name="sub_features")

    kind = models.CharField(max_length=3, choices=SubFeatureKind.choices)
    """
    Is this a numeric or categorical sub-feature?
    """

    # Fields only relevant to numeric sub-features:
    numeric_kind = models.CharField(max_length=10, null=True, blank=True, choices=SubFeatureNumericKind.as_choices())
    """
    What kind of numeric measurement this sub-feature represents
    """

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return (
            f"<SubFeature: key='{self.key}' display_name='{self.display_name}' kind={self.kind}"
            f" numeric_kind={self.numeric_kind}>"
        )

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="subfeature_kind_allowed_vals",
                check=models.Q(kind__in=[v[0] for v in SubFeatureKind.choices]),
            ),
            models.CheckConstraint(
                name="subfeature_numeric_kind_allowed_vals",
                check=(
                    models.Q(kind="CAT", numeric_kind__isnull=True)
                    | models.Q(kind="NUM", numeric_kind__in=[v[0] for v in SubFeatureNumericKind.as_choices()])
                ),
            ),
        ]


class SubFeatureOption(models.Model):
    """
    A possible option for a categorical sub-feature.
    """

    key = models.CharField(max_length=100, unique=True, editable=False)
    """
    A stable identifier for internal programmatic access
    """

    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="options")

    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    created_at = models.DateTimeField(auto_now_add=True)


class MLLabelFeatureMapping(models.Model):
    """
    Which feature does a raw ML class label correspond to?
    """

    ml_label = models.CharField(blank=False, unique=True, max_length=255)  # Assumed to be a primary model label
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="mapped_ml_labels")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class MLLabelModelKind(models.TextChoices):
    PRIMARY_MODEL = "PRI", "Primary model"
    SUB_MODEL = "SUB", "Sub model"


class MLLabelSubFeatureMapping(models.Model):
    """
    Which option (or numeric value / range) does a raw ML class label correspond to for a sub-feature?
    """

    ml_label = models.CharField(blank=False, max_length=255)
    ml_label_model_kind = models.CharField(
        default=MLLabelModelKind.SUB_MODEL, max_length=3, choices=MLLabelModelKind.choices
    )
    """Is this label from a primary model or a sub-model?"""

    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="mapped_sub_feature_ml_labels")
    option = models.ForeignKey(
        SubFeatureOption,
        null=True,
        default=None,
        on_delete=models.CASCADE,
        related_name="mapped_ml_labels",
    )

    numeric_min = models.FloatField(null=True, default=None)
    numeric_max = models.FloatField(null=True, default=None)
    numeric_value = models.FloatField(null=True, default=None)
    numeric_unit = models.CharField(max_length=10, null=True, default=None, choices=SubFeatureUnit.as_choices())

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["ml_label", "ml_label_model_kind", "sub_feature"],
                name="unique_ml_label_sub_feature_mapping",
            ),
            # Either option is set, or one of numeric_value or numeric_min/max is set
            models.CheckConstraint(
                name="ml_label_sub_feature_mapping_option_or_numeric",
                check=(
                    models.Q(option__isnull=False)
                    | (
                        models.Q(numeric_value__isnull=False)
                        | models.Q(numeric_min__isnull=False)
                        | models.Q(numeric_max__isnull=False)
                    )
                ),
            ),
            models.CheckConstraint(
                name="ml_label_sub_feature_mapping_numeric_unit",
                check=models.Q(numeric_unit__isnull=True)
                | models.Q(numeric_unit__in=[v[0] for v in SubFeatureUnit.as_choices()]),
            ),
        ]
