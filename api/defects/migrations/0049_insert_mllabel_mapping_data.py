# Generated by Django 5.0.8 on 2025-08-20 01:40
from pathlib import Path

import pandas as pd
from django.db import migrations


MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0049_insert_mllabel_mapping_data"


def add_feature_data(apps, schema_editor):
    MLLabelFeatureMapping = apps.get_model("defects", "MLLabelFeatureMapping")
    Feature = apps.get_model("defects", "Feature")

    file_path = DATA_FOLDER / "ml_label_feature_mapping.csv"
    df = pd.read_csv(file_path).replace({pd.NA: None})

    features_by_key = {feature.key: feature for feature in Feature.objects.all()}

    to_create = []
    for _, row in df.iterrows():
        feat = features_by_key.get(row["feature_key"])
        if not feat:
            print(f"Feature with key '{row['feature_key']}' not found, skipping row...")
            continue

        feature_mapping = MLLabelFeatureMapping(
            ml_label=row["ml_label"],
            feature=feat,
        )
        to_create.append(feature_mapping)

    MLLabelFeatureMapping.objects.bulk_create(to_create)


def unset_feature_data(apps, schema_editor):
    MLLabelFeatureMapping = apps.get_model("defects", "MLLabelFeatureMapping")

    file_path = DATA_FOLDER / "ml_label_feature_mapping.csv"
    df = pd.read_csv(file_path).replace({pd.NA: None})

    for _, row in df.iterrows():
        MLLabelFeatureMapping.objects.filter(
            ml_label=row["ml_label"],
            feature__key=row["feature_key"],
        ).delete()


def add_sub_feature_data(apps, schema_editor):
    MLLabelSubFeatureMapping = apps.get_model("defects", "MLLabelSubFeatureMapping")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")

    file_path = DATA_FOLDER / "ml_label_sub_feature_mapping.csv"
    df = pd.read_csv(file_path).replace({pd.NA: None})

    sub_features_by_key = {sub_feature.key: sub_feature for sub_feature in SubFeature.objects.all()}
    options_by_key = {
        option.key: option
        for option in SubFeatureOption.objects.filter(key__in=df[df["option_key"].notna()]["option_key"].to_list())
    }

    to_create = []
    for _, row in df.iterrows():
        sub_feat = sub_features_by_key.get(row["sub_feature_key"])
        if not sub_feat:
            print(f"SubFeature with key '{row['sub_feature_key']}' not found, skipping row...")
            continue

        option = options_by_key.get(row["option_key"])
        if row["option_key"] and not option:
            print(f"SubFeatureOption with key '{row['option_key']}' not found, skipping row...")
            continue

        sub_feature_mapping = MLLabelSubFeatureMapping(
            ml_label=row["ml_label"],
            ml_label_model_kind=row["ml_label_model_kind"],
            sub_feature=sub_feat,
            option=option,
            numeric_min=float(row["numeric_min"]) if pd.notna(row["numeric_min"]) else None,
            numeric_max=float(row["numeric_max"]) if pd.notna(row["numeric_max"]) else None,
            numeric_value=float(row["numeric_value"]) if pd.notna(row["numeric_value"]) else None,
            numeric_unit=row["numeric_unit"] if pd.notna(row["numeric_unit"]) else None,
        )
        to_create.append(sub_feature_mapping)

    MLLabelSubFeatureMapping.objects.bulk_create(to_create)


def unset_sub_feature_data(apps, schema_editor):
    MLLabelSubFeatureMapping = apps.get_model("defects", "MLLabelSubFeatureMapping")

    file_path = DATA_FOLDER / "ml_label_sub_feature_mapping.csv"
    df = pd.read_csv(file_path).replace({pd.NA: None})

    for _, row in df.iterrows():
        MLLabelSubFeatureMapping.objects.filter(
            ml_label=row["ml_label"],
            ml_label_model_kind=row["ml_label_model_kind"],
            sub_feature__key=row["sub_feature_key"],
        ).delete()


class Migration(migrations.Migration):
    dependencies = [
        (
            "defects",
            "0048_remove_mllabelsubfeaturemapping_unique_ml_label_sub_feature_mapping_and_more",
        ),
    ]

    operations = [
        migrations.RunPython(code=add_feature_data, reverse_code=unset_feature_data),
        migrations.RunPython(code=add_sub_feature_data, reverse_code=unset_sub_feature_data),
    ]
