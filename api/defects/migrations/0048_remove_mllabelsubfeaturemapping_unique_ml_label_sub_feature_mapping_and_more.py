# Generated by Django 5.0.8 on 2025-08-20 01:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0047_create_remaining_static_defect_data"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="mllabelsubfeaturemapping",
            name="unique_ml_label_sub_feature_mapping",
        ),
        migrations.AddField(
            model_name="mllabelsubfeaturemapping",
            name="ml_label_model_kind",
            field=models.CharField(
                choices=[("PRI", "Primary model"), ("SUB", "Sub model")],
                default="SUB",
                max_length=3,
            ),
        ),
        migrations.AddConstraint(
            model_name="mllabelsubfeaturemapping",
            constraint=models.UniqueConstraint(
                fields=("ml_label", "ml_label_model_kind", "sub_feature"),
                name="unique_ml_label_sub_feature_mapping",
            ),
        ),
    ]
