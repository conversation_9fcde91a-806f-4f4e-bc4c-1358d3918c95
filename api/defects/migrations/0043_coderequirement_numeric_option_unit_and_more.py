# Generated by Django 5.0.8 on 2025-07-22 00:13
import csv
from pathlib import Path

from django.db import migrations, models


MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0038_defect_mapping_one_substandard"


def add_code_score_req_units(apps, schema_editor):
    CodeScoreRequirement = apps.get_model("defects", "CodeScoreRequirement")

    csv_path = DATA_FOLDER / "CodeScoreRequirement.csv"
    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            CodeScoreRequirement.objects.filter(pk=row["id"]).update(
                numeric_option_unit=row["numeric_option_unit"] or None,
            )


def unset_code_score_req_units(apps, schema_editor):
    CodeScoreRequirement = apps.get_model("defects", "CodeScoreRequirement")
    csv_path = DATA_FOLDER / "CodeScoreRequirement.csv"
    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        ids = [int(row["id"]) for row in reader]
        CodeScoreRequirement.objects.filter(pk__in=ids).update(numeric_option_unit=None)


class Migration(migrations.Migration):
    dependencies = [
        (
            "defects",
            "0042_remove_standardsubfeature_standardsubfeature_numeric_xor_categorical_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="coderequirement",
            name="numeric_option_unit",
            field=models.CharField(
                choices=[
                    ("PERC", "Percentage"),
                    ("METRE", "Metres"),
                    ("MM", "Millimetres"),
                    ("FEET", "Feet"),
                    ("INCH", "Inches"),
                    ("COUNT", "Count"),
                    ("CUBIC_M", "Cubic Metres"),
                    ("CUBIC_FT", "Cubic Feet"),
                    ("DEGREES", "Degrees"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="codescorerequirement",
            name="numeric_option_unit",
            field=models.CharField(
                choices=[
                    ("PERC", "Percentage"),
                    ("METRE", "Metres"),
                    ("MM", "Millimetres"),
                    ("FEET", "Feet"),
                    ("INCH", "Inches"),
                    ("COUNT", "Count"),
                    ("CUBIC_M", "Cubic Metres"),
                    ("CUBIC_FT", "Cubic Feet"),
                    ("DEGREES", "Degrees"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.RunPython(code=add_code_score_req_units, reverse_code=unset_code_score_req_units),
    ]
