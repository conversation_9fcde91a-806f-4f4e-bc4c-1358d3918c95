# Generated by Django 5.0.8 on 2025-07-24 01:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0043_coderequirement_numeric_option_unit_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="coderequirement",
            name="numeric_option_unit",
            field=models.CharField(
                choices=[
                    ("PERC", "PERCENTAGE"),
                    ("METRE", "METRES"),
                    ("MM", "MILLIMETRES"),
                    ("FEET", "FEET"),
                    ("INCH", "INCHES"),
                    ("COUNT", "COUNT"),
                    ("CUBIC_M", "CUBIC_METRES"),
                    ("CUBIC_FT", "CUBIC_FEET"),
                    ("DEGREES", "DEGREES"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="codescorerequirement",
            name="numeric_option_unit",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                choices=[
                    ("PERC", "PERCENTAGE"),
                    ("METRE", "METRES"),
                    ("MM", "MILLIMETRES"),
                    ("FEET", "FEET"),
                    ("INCH", "INCHES"),
                    ("COUNT", "COUNT"),
                    ("CUBIC_M", "CUBIC_METRES"),
                    ("CUBIC_FT", "CUBIC_FEET"),
                    ("DEGREES", "DEGREES"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="mllabelsubfeaturemapping",
            name="numeric_unit",
            field=models.CharField(
                choices=[
                    ("PERC", "PERCENTAGE"),
                    ("METRE", "METRES"),
                    ("MM", "MILLIMETRES"),
                    ("FEET", "FEET"),
                    ("INCH", "INCHES"),
                    ("COUNT", "COUNT"),
                    ("CUBIC_M", "CUBIC_METRES"),
                    ("CUBIC_FT", "CUBIC_FEET"),
                    ("DEGREES", "DEGREES"),
                ],
                default=None,
                max_length=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="standardsubfeature",
            name="numeric_unit",
            field=models.CharField(
                blank=True,
                choices=[
                    ("PERC", "PERCENTAGE"),
                    ("METRE", "METRES"),
                    ("MM", "MILLIMETRES"),
                    ("FEET", "FEET"),
                    ("INCH", "INCHES"),
                    ("COUNT", "COUNT"),
                    ("CUBIC_M", "CUBIC_METRES"),
                    ("CUBIC_FT", "CUBIC_FEET"),
                    ("DEGREES", "DEGREES"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="subfeature",
            name="numeric_kind",
            field=models.CharField(
                blank=True,
                choices=[
                    ("DIST", "DISTANCE"),
                    ("LEN", "LENGTH"),
                    ("PERC", "PERCENTAGE"),
                    ("COUNT", "COUNT"),
                    ("VOL", "VOLUME"),
                    ("ANGLE", "ANGLE"),
                ],
                max_length=10,
                null=True,
            ),
        ),
    ]
