# Generated by Django 4.1.2 on 2025-06-05 06:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0034_mllabelsubfeaturemapping_mllabelfeaturemapping_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="mllabelsubfeaturemapping",
            name="unique_ml_label_sub_feature_mapping",
        ),
        migrations.RemoveField(
            model_name="mllabelsubfeaturemapping",
            name="feature",
        ),
        migrations.AlterField(
            model_name="mllabelsubfeaturemapping",
            name="sub_feature",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="mapped_sub_feature_ml_labels",
                to="defects.subfeature",
            ),
        ),
        migrations.AddConstraint(
            model_name="mllabelsubfeaturemapping",
            constraint=models.UniqueConstraint(
                fields=("ml_label", "sub_feature"),
                name="unique_ml_label_sub_feature_mapping",
            ),
        ),
    ]
