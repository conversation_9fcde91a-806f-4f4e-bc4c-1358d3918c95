# Generated by Django 5.0.8 on 2025-07-04 03:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0041_code_at_joint_required_alter_code_at_joint_allowed"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="standardsubfeature",
            name="standardsubfeature_numeric_xor_categorical",
        ),
        migrations.AddField(
            model_name="code",
            name="percent_field_allowed",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="code",
            name="percent_field_required",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="standardsubfeature",
            name="is_report_percent_field",
            field=models.BooleanField(default=False),
        ),
        migrations.AddConstraint(
            model_name="standardsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("default_option__isnull", True),
                    models.Q(
                        ("default_option__isnull", False),
                        ("is_report_percent_field", False),
                        ("maps_numeric_ranges_to_labels", False),
                        ("numeric_default__isnull", True),
                        ("numeric_display_decimal_places__isnull", True),
                        ("numeric_max__isnull", True),
                        ("numeric_min__isnull", True),
                        ("numeric_unit__isnull", True),
                    ),
                    _connector="OR",
                ),
                name="standardsubfeature_numeric_xor_categorical",
            ),
        ),
    ]
