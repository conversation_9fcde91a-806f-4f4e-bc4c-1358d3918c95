import csv
import json
from pathlib import Path

from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0046_create_new_defect_schema_static_data"
FIXTURES_FOLDER = MIGRATIONS_FOLDER.parent / "fixtures"


def create_code_score_requirements(apps, schema_editor):
    CodeScoreRequirement = apps.get_model("defects", "CodeScoreRequirement")
    CodeScore = apps.get_model("defects", "CodeScore")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    csv_path = DATA_FOLDER / "code_score_requirements.csv"

    code_scores = list(CodeScore.objects.all())
    sub_features = list(SubFeature.objects.all())
    sub_feature_options = list(SubFeatureOption.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        code_score_requirements = [
            CodeScoreRequirement(
                code_score=[
                    cs
                    for cs in code_scores
                    if cs.code.standard.name == row["code_score_code_standard_name"]
                    and cs.code.display_code == row["code_score_code_display_code"]
                    and cs.code.display_name == row["code_score_code_display_name"]
                    and cs.service_score == (float(row["code_score_service_score"]) if row["code_score_service_score"] else None)
                    and cs.structural_score == (float(row["code_score_structural_score"]) if row["code_score_structural_score"] else None)
                ][0],
                sub_feature=[sf for sf in sub_features if sf.key == row["sub_feature_key"]][0],
                required_option=(
                    [sfo for sfo in sub_feature_options if sfo.key == row["required_option_key"]][0]
                    if row["required_option_key"]
                    else None
                ),
                numeric_option_min_breakpoint=(
                    float(row["numeric_option_min_breakpoint"]) if row["numeric_option_min_breakpoint"] else None
                ),
                numeric_option_max_breakpoint=(
                    float(row["numeric_option_max_breakpoint"]) if row["numeric_option_max_breakpoint"] else None
                ),
            )
            for row in reader
        ]
        CodeScoreRequirement.objects.bulk_create(code_score_requirements)


def create_code_scores(apps, schema_editor):
    CodeScore = apps.get_model("defects", "CodeScore")
    StandardSubCategory = apps.get_model("defects", "StandardSubcategory")
    CodeScoreStandardSubcategory = apps.get_model("defects", "CodeScoreStandardSubcategory")
    Code = apps.get_model("defects", "Code")
    csv_path = DATA_FOLDER / "code_scores.csv"

    standard_subcategories = list(StandardSubCategory.objects.all())
    codes = list(Code.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = list(csv.DictReader(csvfile))

    code_scores = []
    for row in reader:
        code_score = CodeScore(
            code=[
                c
                for c in codes
                if c.standard.name == row["code_standard_name"]
                and c.display_code == row["code_display_code"]
                and c.display_name == row["code_display_name"]
            ][0],
            service_score=float(row["service_score"]) if row["service_score"] else None,
            structural_score=float(row["structural_score"]) if row["structural_score"] else None,
            repair_priority=float(row["repair_priority"]) if row["repair_priority"] else None,
            required_clock_position_min=(
                int(row["required_clock_position_min"]) if row["required_clock_position_min"] else None
            ),
            required_clock_position_max=(
                int(row["required_clock_position_max"]) if row["required_clock_position_max"] else None
            ),
            required_clock_position_min_spread=(
                int(row["required_clock_position_min_spread"]) if row["required_clock_position_min_spread"] else None
            ),
            required_clock_position_max_spread=(
                int(row["required_clock_position_max_spread"]) if row["required_clock_position_max_spread"] else None
            ),
            added_service_score_per_metre=(
                float(row["added_service_score_per_metre"]) if row["added_service_score_per_metre"] else 0.0
            ),
            added_structural_score_per_metre=(
                float(row["added_structural_score_per_metre"]) if row["added_structural_score_per_metre"] else 0.0
            ),
        )
        code_scores.append(code_score)
    CodeScore.objects.bulk_create(code_scores)

    code_score_standard_subcategories = []
    code_scores = list(CodeScore.objects.all())
    for row in reader:
        code_score = [
            cs
            for cs in code_scores
            if cs.code.standard.name == row["code_standard_name"]
            and cs.code.display_code == row["code_display_code"]
            and cs.code.display_name == row["code_display_name"]
            and cs.service_score == (float(row["service_score"]) if row["service_score"] else None)
            and cs.structural_score == (float(row["structural_score"]) if row["structural_score"] else None)
            and cs.repair_priority == (float(row["repair_priority"]) if row["repair_priority"] else None)
        ][0]
        for s in row["standard_subcategories_id"].split(", "):
            standard_subcategory = [ssc for ssc in standard_subcategories if ssc.id == int(s)][0]
            if len([
                csss
                for csss in code_score_standard_subcategories
                if csss.code_score == code_score and csss.standard_subcategory == standard_subcategory
            ]) == 0:
                code_score_standard_subcategories.append(
                    CodeScoreStandardSubcategory(code_score=code_score, standard_subcategory=standard_subcategory)
                )
    CodeScoreStandardSubcategory.objects.bulk_create(code_score_standard_subcategories)


def create_code_requirements(apps, schema_editor):
    CodeRequirement = apps.get_model("defects", "CodeRequirement")
    Code = apps.get_model("defects", "Code")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    csv_path = DATA_FOLDER / "code_requirements.csv"

    codes = list(Code.objects.all())
    sub_features = list(SubFeature.objects.all())
    sub_feature_options = list(SubFeatureOption.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        code_requirements = [
            CodeRequirement(
                code=[
                    c
                    for c in codes
                    if c.standard.name == row["code_standard_name"]
                    and c.display_code == row["code_display_code"]
                    and c.display_name == row["code_display_name"]
                ][0],
                sub_feature=(
                    [sf for sf in sub_features if sf.key == row["sub_feature_key"]][0]
                    if row["sub_feature_key"]
                    else None
                ),
                required_option=(
                    [sfo for sfo in sub_feature_options if sfo.key == row["required_option_key"]][0]
                    if row["required_option_key"]
                    else None
                ),
                numeric_option_range_min=(
                    float(row["numeric_option_range_min"]) if row["numeric_option_range_min"] else None
                ),
                numeric_option_range_max=(
                    float(row["numeric_option_range_max"]) if row["numeric_option_range_max"] else None
                ),
            )
            for row in reader
        ]
        CodeRequirement.objects.bulk_create(code_requirements)


def create_codes(apps, schema_editor):
    Code = apps.get_model("defects", "Code")
    Standard = apps.get_model("defects", "Standard")
    Feature = apps.get_model("defects", "Feature")
    StandardSubCategory = apps.get_model("defects", "StandardSubcategory")
    CodeStandardSubcategory = apps.get_model("defects", "CodeStandardSubcategory")
    csv_path = DATA_FOLDER / "codes.csv"

    standards = list(Standard.objects.all())
    features = list(Feature.objects.all())

    with open(FIXTURES_FOLDER / "standard_subcategories.json") as f:
        ssc_fixture = json.load(f)
    
    # Migrations are run before fixtures are loaded on pipeline,
    # and subcategory ids 13-16 are not created via migration
    for ssc in ssc_fixture:
        StandardSubCategory.objects.get_or_create(
            id=ssc["pk"],
            standard_key_id=ssc["fields"]["standard_key"],
            pipe_type_sewer=ssc["fields"]["pipe_type_sewer"],
            material_type=ssc["fields"]["material_type"],
            region=ssc["fields"]["region"],
            comment=ssc["fields"]["comment"],
        )
    standard_subcategories = list(StandardSubCategory.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = list(csv.DictReader(csvfile))

    codes = []
    for row in reader:
        code = Code(
            standard=[s for s in standards if s.name == row["standard_name"]][0],
            feature=[f for f in features if f.key == row["feature_key"]][0],
            code_type=row["code_type"],
            display_code=row["display_code"],
            display_name=row["display_name"],
            at_joint_required=row["at_joint_required"] == "TRUE",
            at_joint_allowed=row["at_joint_allowed"] == "TRUE",
            continuous_required=row["continuous_required"] == "TRUE",
            continuous_allowed=row["continuous_allowed"] == "TRUE",
            percent_field_required=row["percent_field_required"] == "TRUE",
            percent_field_allowed=row["percent_field_allowed"] == "TRUE",
            remarks_required=row["remarks_required"] == "TRUE",
            clock_position_from_required=row["clock_position_from_required"] == "TRUE",
            clock_position_from_min=int(row["clock_position_from_min"]),
            clock_position_from_max=int(row["clock_position_from_max"]),
            clock_position_to_required=row["clock_position_to_required"] == "TRUE",
            clock_position_to_min=int(row["clock_position_to_min"]),
            clock_position_to_max=int(row["clock_position_to_max"]),
            is_start_code=row["is_start_code"] == "TRUE",
            is_end_code=row["is_end_code"] == "TRUE",
        )
        codes.append(code)
    Code.objects.bulk_create(codes)

    code_standard_subcategories = []
    codes = list(Code.objects.all())
    for row in reader:
        code = [
            c
            for c in codes
            if c.standard.name == row["standard_name"]
            and c.display_code == row["display_code"]
            and c.display_name == row["display_name"]
        ][0]
        for s in row["standard_subcategories_id"].split(", "):
            standard_subcategory = [ssc for ssc in standard_subcategories if ssc.id == int(s)][0]
            code_standard_subcategories.append(
                CodeStandardSubcategory(code=code, standard_subcategory=standard_subcategory)
            )
    CodeStandardSubcategory.objects.bulk_create(code_standard_subcategories)


def create_standard_sub_features(apps, schema_editor):
    StandardSubFeature = apps.get_model("defects", "StandardSubFeature")
    Standard = apps.get_model("defects", "Standard")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    csv_path = DATA_FOLDER / "standard_sub_features.csv"

    standards = list(Standard.objects.all())
    sub_features = list(SubFeature.objects.all())
    sub_feature_options = list(SubFeatureOption.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        standard_sub_features = [
            StandardSubFeature(
                standard=[s for s in standards if s.name == row["standard_name"]][0],
                sub_feature=[sf for sf in sub_features if sf.key == row["sub_feature_key"]][0],
                display_name=row["display_name"],
                display_order=int(row["display_order"]),
                default_option=(
                    [sfo for sfo in sub_feature_options if sfo.key == row["default_option_key"]][0]
                    if row["default_option_key"]
                    else None
                ),
                numeric_min=float(row["numeric_min"]) if row["numeric_min"] else None,
                numeric_max=float(row["numeric_max"]) if row["numeric_max"] else None,
                numeric_unit=row["numeric_unit"] if row["numeric_unit"] else None,
                numeric_default=float(row["numeric_default"]) if row["numeric_default"] else None,
                numeric_display_decimal_places=(
                    int(row["numeric_display_decimal_places"]) if row["numeric_display_decimal_places"] else None
                ),
                quantity_field_number=int(row["quantity_field_number"]) if row["quantity_field_number"] else None,
                characteristic_field_number=(
                    int(row["characteristic_field_number"]) if row["characteristic_field_number"] else None
                ),
                maps_numeric_ranges_to_labels=row["maps_numeric_ranges_to_labels"] == "TRUE",
            )
            for row in reader
        ]
        StandardSubFeature.objects.bulk_create(standard_sub_features)


def create_sub_feature_options(apps, schema_editor):
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    SubFeature = apps.get_model("defects", "SubFeature")
    csv_path = DATA_FOLDER / "sub_feature_options.csv"

    sub_features = list(SubFeature.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        sub_feature_options = [
            SubFeatureOption(
                key=row["key"],
                sub_feature=[sf for sf in sub_features if sf.key == row["sub_feature_key"]][0],
                display_name=row["display_name"],
                display_order=int(row["display_order"]),
            )
            for row in reader
        ]
        SubFeatureOption.objects.bulk_create(sub_feature_options)


def create_sub_features(apps, schema_editor):
    SubFeature = apps.get_model("defects", "SubFeature")
    Feature = apps.get_model("defects", "Feature")
    csv_path = DATA_FOLDER / "sub_features.csv"

    features = list(Feature.objects.all())

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        sub_features = [
            SubFeature(
                key=row["key"],
                display_name=row["display_name"],
                display_order=int(row["display_order"]),
                feature=[f for f in features if f.key == row["feature_key"]][0],
                kind=row["kind"],
                numeric_kind=row["numeric_kind"] if row["numeric_kind"] else None,
            )
            for row in reader
        ]
        SubFeature.objects.bulk_create(sub_features)


def create_features(apps, schema_editor):
    Feature = apps.get_model("defects", "Feature")
    csv_path = DATA_FOLDER / "features.csv"

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        features = [Feature(key=row["key"], display_name=row["display_name"]) for row in reader if row["key"]]
        Feature.objects.bulk_create(features)


def delete_old_data(apps, schema_editor):
    # The only existing data in the new schema currently was sample data created in migration 0040
    # We want to delete this data as it is changed/recreated in the data coming in with this migration

    Feature = apps.get_model("defects", "Feature")
    # StandardFeature no longer exists so we don't need to delete from it
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    StandardSubFeature = apps.get_model("defects", "StandardSubFeature")
    Code = apps.get_model("defects", "Code")
    CodeStandardSubcategory = apps.get_model("defects", "CodeStandardSubcategory")
    CodeRequirement = apps.get_model("defects", "CodeRequirement")
    CodeScore = apps.get_model("defects", "CodeScore")
    CodeScoreStandardSubcategory = apps.get_model("defects", "CodeScoreStandardSubcategory")
    CodeScoreRequirement = apps.get_model("defects", "CodeScoreRequirement")

    CodeScoreRequirement.objects.all().delete()
    CodeScoreStandardSubcategory.objects.all().delete()
    CodeScore.objects.all().delete()
    CodeRequirement.objects.all().delete()
    CodeStandardSubcategory.objects.all().delete()
    Code.objects.all().delete()
    StandardSubFeature.objects.all().delete()
    SubFeatureOption.objects.all().delete()
    SubFeature.objects.all().delete()
    Feature.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        (
            "defects",
            "0045_delete_standardfeature",
        ),
    ]

    operations = [
        migrations.RunPython(delete_old_data, migrations.RunPython.noop),
        migrations.RunPython(create_features, migrations.RunPython.noop),
        migrations.RunPython(create_sub_features, migrations.RunPython.noop),
        migrations.RunPython(create_sub_feature_options, migrations.RunPython.noop),
        migrations.RunPython(create_standard_sub_features, migrations.RunPython.noop),
        migrations.RunPython(create_codes, migrations.RunPython.noop),
        migrations.RunPython(create_code_requirements, migrations.RunPython.noop),
        migrations.RunPython(create_code_scores, migrations.RunPython.noop),
        migrations.RunPython(create_code_score_requirements, migrations.RunPython.noop),
    ]
