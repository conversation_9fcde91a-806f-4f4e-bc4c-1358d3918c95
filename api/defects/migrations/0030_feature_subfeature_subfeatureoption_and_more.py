# Generated by Django 4.1.2 on 2025-05-19 00:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0029_defect_scores_missing_mappings"),
    ]

    operations = [
        migrations.CreateModel(
            name="Feature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("key", models.Char<PERSON>ield(editable=False, max_length=50, unique=True)),
                ("display_name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="SubFeature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("key", models.Char<PERSON>ield(editable=False, max_length=50, unique=True)),
                ("display_name", models.CharField(max_length=255)),
                ("display_order", models.PositiveIntegerField()),
                (
                    "kind",
                    models.CharField(
                        choices=[("CAT", "Categorical"), ("NUM", "Numeric")],
                        max_length=3,
                    ),
                ),
                (
                    "numeric_kind",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("DIST", "Distance"),
                            ("PERC", "Percentage"),
                            ("COUNT", "Count"),
                            ("VOL", "Volume"),
                            ("ANGLE", "Rotational Angle"),
                        ],
                        max_length=10,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_features",
                        to="defects.feature",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SubFeatureOption",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("key", models.CharField(editable=False, max_length=50, unique=True)),
                ("display_name", models.CharField(max_length=255)),
                ("display_order", models.PositiveIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "sub_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="options",
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StandardSubFeatureRangeLabel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("display_code", models.CharField(max_length=10)),
                ("display_name", models.CharField(max_length=255)),
                ("display_order", models.PositiveIntegerField()),
                ("numeric_range_min", models.FloatField()),
                ("numeric_range_max", models.FloatField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "standard_sub_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="range_labels",
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StandardSubFeatureOption",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("display_code", models.CharField(max_length=10)),
                ("display_name", models.CharField(max_length=255)),
                ("display_order", models.PositiveIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "option",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="standardised_set",
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "standard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_feature_options",
                        to="defects.standard",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StandardSubFeature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("display_name", models.CharField(max_length=255)),
                ("display_order", models.PositiveIntegerField()),
                ("numeric_min", models.FloatField(blank=True, null=True)),
                ("numeric_max", models.FloatField(blank=True, null=True)),
                (
                    "numeric_unit",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("PERC", "Percentage"),
                            ("METRE", "Metres"),
                            ("MM", "Millimetres"),
                            ("FEET", "Feet"),
                            ("INCH", "Inches"),
                            ("COUNT", "Count"),
                            ("CUBIC_M", "Cubic Metres"),
                            ("CUBIC_FT", "Cubic Feet"),
                            ("DEGREES", "Degrees"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("numeric_default", models.FloatField(blank=True, null=True)),
                (
                    "numeric_display_decimal_places",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "quantity_field_number",
                    models.PositiveSmallIntegerField(default=None, null=True),
                ),
                (
                    "characteristic_field_number",
                    models.PositiveSmallIntegerField(default=None, null=True),
                ),
                ("maps_numeric_ranges_to_labels", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "default_option",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="default_for",
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "standard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_features",
                        to="defects.standard",
                    ),
                ),
                (
                    "sub_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="standardised_set",
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StandardFeature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("display_name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="standardised_set",
                        to="defects.feature",
                    ),
                ),
                (
                    "standard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="features",
                        to="defects.standard",
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="subfeature",
            constraint=models.CheckConstraint(
                check=models.Q(("kind__in", ["CAT", "NUM"])),
                name="subfeature_kind_allowed_vals",
            ),
        ),
        migrations.AddConstraint(
            model_name="subfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("kind", "CAT"), ("numeric_kind__isnull", True)),
                    models.Q(
                        ("kind", "NUM"),
                        ("numeric_kind__in", ["DIST", "PERC", "COUNT", "VOL", "ANGLE"]),
                    ),
                    _connector="OR",
                ),
                name="subfeature_numeric_kind_allowed_vals",
            ),
        ),
        migrations.AddConstraint(
            model_name="standardsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("default_option__isnull", True),
                    models.Q(
                        ("default_option__isnull", False),
                        ("maps_numeric_ranges_to_labels", False),
                        ("numeric_default__isnull", True),
                        ("numeric_display_decimal_places__isnull", True),
                        ("numeric_max__isnull", True),
                        ("numeric_min__isnull", True),
                        ("numeric_unit__isnull", True),
                    ),
                    _connector="OR",
                ),
                name="standardsubfeature_numeric_xor_categorical",
            ),
        ),
    ]
