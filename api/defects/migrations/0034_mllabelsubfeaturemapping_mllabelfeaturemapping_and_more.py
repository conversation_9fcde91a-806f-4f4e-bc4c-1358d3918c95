# Generated by Django 4.1.2 on 2025-06-05 03:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0033_alter_standardsubfeature_default_option"),
    ]

    operations = [
        migrations.CreateModel(
            name="MLLabelSubFeatureMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ml_label", models.CharField(max_length=255)),
                ("numeric_min", models.FloatField(default=None, null=True)),
                ("numeric_max", models.FloatField(default=None, null=True)),
                ("numeric_value", models.FloatField(default=None, null=True)),
                (
                    "numeric_unit",
                    models.CharField(
                        choices=[
                            ("PERC", "Percentage"),
                            ("METRE", "Metres"),
                            ("MM", "Millimetres"),
                            ("FEET", "Feet"),
                            ("INCH", "Inches"),
                            ("COUNT", "Count"),
                            ("CUBIC_M", "Cubic Metres"),
                            ("CUBIC_FT", "Cubic Feet"),
                            ("DEGREES", "Degrees"),
                        ],
                        default=None,
                        max_length=10,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mapped_ml_sub_features",
                        to="defects.feature",
                    ),
                ),
                (
                    "option",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mapped_ml_labels",
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "sub_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mapped_sub_feature_ml_labels",
                        to="defects.subfeatureoption",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MLLabelFeatureMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ml_label", models.CharField(max_length=255, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mapped_ml_labels",
                        to="defects.feature",
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="mllabelsubfeaturemapping",
            constraint=models.UniqueConstraint(
                fields=("ml_label", "feature"),
                name="unique_ml_label_sub_feature_mapping",
            ),
        ),
        migrations.AddConstraint(
            model_name="mllabelsubfeaturemapping",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("option__isnull", False),
                    ("numeric_value__isnull", False),
                    ("numeric_min__isnull", False),
                    ("numeric_max__isnull", False),
                    _connector="OR",
                ),
                name="ml_label_sub_feature_mapping_option_or_numeric",
            ),
        ),
        migrations.AddConstraint(
            model_name="mllabelsubfeaturemapping",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("numeric_unit__isnull", True),
                    (
                        "numeric_unit__in",
                        [
                            "PERC",
                            "METRE",
                            "MM",
                            "FEET",
                            "INCH",
                            "COUNT",
                            "CUBIC_M",
                            "CUBIC_FT",
                            "DEGREES",
                        ],
                    ),
                    _connector="OR",
                ),
                name="ml_label_sub_feature_mapping_numeric_unit",
            ),
        ),
    ]
