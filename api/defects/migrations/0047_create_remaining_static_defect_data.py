import csv
from pathlib import Path

from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0047_create_remaining_static_defect_data"


def create_code_score_requirements(apps, schema_editor):
    CodeScoreRequirement = apps.get_model("defects", "CodeScoreRequirement")
    CodeScore = apps.get_model("defects", "CodeScore")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    csv_path = DATA_FOLDER / "code_score_requirements.csv"

    code_scores = list(CodeScore.objects.all())
    sub_features = list(SubFeature.objects.all())
    sub_feature_options = list(SubFeatureOption.objects.all())
    existing = set(
        (csr.code_score.id,
        csr.numeric_option_min_breakpoint if csr.numeric_option_min_breakpoint else None,
        csr.numeric_option_max_breakpoint if csr.numeric_option_max_breakpoint else None)
        for csr in CodeScoreRequirement.objects.all()
    )

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        code_score_requirements = []
        for row in reader:
            try:
                obj = CodeScoreRequirement(
                    code_score=[
                        cs
                        for cs in code_scores
                        if cs.code.standard.name == row["code_score_code_standard_name"]
                        and cs.code.display_code == row["code_score_code_display_code"]
                        and cs.code.display_name == row["code_score_code_display_name"]
                        and cs.service_score == (float(row["code_score_service_score"]) if row["code_score_service_score"] else None)
                        and cs.structural_score == (float(row["code_score_structural_score"]) if row["code_score_structural_score"] else None)
                    ][0],
                    sub_feature=[sf for sf in sub_features if sf.key == row["sub_feature_key"]][0],
                    required_option=(
                        [sfo for sfo in sub_feature_options if sfo.key == row["required_option_key"]][0]
                        if row["required_option_key"]
                        else None
                    ),
                    numeric_option_min_breakpoint=(
                        float(row["numeric_option_min_breakpoint"]) if row["numeric_option_min_breakpoint"] else None
                    ),
                    numeric_option_max_breakpoint=(
                        float(row["numeric_option_max_breakpoint"]) if row["numeric_option_max_breakpoint"] else None
                    ),
                )
                ident = (
                    obj.code_score.id,
                    obj.numeric_option_min_breakpoint if obj.numeric_option_min_breakpoint else None,
                    obj.numeric_option_max_breakpoint if obj.numeric_option_max_breakpoint else None,
                )
                if ident not in existing:
                    existing.add(ident)
                    code_score_requirements.append(obj)
            except Exception:
                raise Exception(row)
        CodeScoreRequirement.objects.bulk_create(code_score_requirements)


def create_code_scores(apps, schema_editor):
    CodeScore = apps.get_model("defects", "CodeScore")
    StandardSubCategory = apps.get_model("defects", "StandardSubcategory")
    CodeScoreStandardSubcategory = apps.get_model("defects", "CodeScoreStandardSubcategory")
    Code = apps.get_model("defects", "Code")
    csv_path = DATA_FOLDER / "code_scores.csv"

    standard_subcategories = list(StandardSubCategory.objects.all())
    codes = list(Code.objects.all())
    existing_cs = set(
        (cs.code.id, cs.service_score, cs.structural_score)
        for cs in CodeScore.objects.all()
    )

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = list(csv.DictReader(csvfile))

    code_scores = []
    for row in reader:
        code = [
            c
            for c in codes
            if c.standard.name == row["code_standard_name"]
            and c.display_code == row["code_display_code"]
            and c.display_name == row["code_display_name"]
        ][0]
        obj = CodeScore(
            code=code,
            service_score=float(row["service_score"]) if row["service_score"] else None,
            structural_score=float(row["structural_score"]) if row["structural_score"] else None,
            repair_priority=float(row["repair_priority"]) if row["repair_priority"] else None,
            required_clock_position_min=(
                int(row["required_clock_position_min"]) if row["required_clock_position_min"] else None
            ),
            required_clock_position_max=(
                int(row["required_clock_position_max"]) if row["required_clock_position_max"] else None
            ),
            required_clock_position_min_spread=(
                int(row["required_clock_position_min_spread"]) if row["required_clock_position_min_spread"] else None
            ),
            required_clock_position_max_spread=(
                int(row["required_clock_position_max_spread"]) if row["required_clock_position_max_spread"] else None
            ),
            added_service_score_per_metre=(
                float(row["added_service_score_per_metre"]) if row["added_service_score_per_metre"] else 0.0
            ),
            added_structural_score_per_metre=(
                float(row["added_structural_score_per_metre"]) if row["added_structural_score_per_metre"] else 0.0
            ),
        )
        ident = (
            obj.code.id,
            obj.service_score,
            obj.structural_score,
        )
        if ident not in existing_cs:
            existing_cs.add(ident)
            code_scores.append(obj)
    CodeScore.objects.bulk_create(code_scores)

    code_score_standard_subcategories = []
    code_scores = list(CodeScore.objects.all())
    existing_csss = set(
        (csss.code_score.id, csss.standard_subcategory.id)
        for csss in CodeScoreStandardSubcategory.objects.all()
    )
    for row in reader:
        code_score = [
            cs
            for cs in code_scores
            if cs.code.standard.name == row["code_standard_name"]
            and cs.code.display_code == row["code_display_code"]
            and cs.code.display_name == row["code_display_name"]
            and cs.service_score == (float(row["service_score"]) if row["service_score"] else None)
            and cs.structural_score == (float(row["structural_score"]) if row["structural_score"] else None)
            and cs.repair_priority == (float(row["repair_priority"]) if row["repair_priority"] else None)
        ][0]
        for s in row["standard_subcategories_id"].split(", "):
            standard_subcategory = [ssc for ssc in standard_subcategories if ssc.id == int(s)][0]
            ident = (code_score.id, standard_subcategory.id)
            if ident not in existing_csss:
                existing_csss.add(ident)
                code_score_standard_subcategories.append(
                    CodeScoreStandardSubcategory(code_score=code_score, standard_subcategory=standard_subcategory)
                )
    CodeScoreStandardSubcategory.objects.bulk_create(code_score_standard_subcategories)


def create_standard_sub_features(apps, schema_editor):
    StandardSubFeature = apps.get_model("defects", "StandardSubFeature")
    Standard = apps.get_model("defects", "Standard")
    SubFeature = apps.get_model("defects", "SubFeature")
    SubFeatureOption = apps.get_model("defects", "SubFeatureOption")
    csv_path = DATA_FOLDER / "standard_sub_features.csv"

    standards = list(Standard.objects.all())
    sub_features = list(SubFeature.objects.all())
    sub_feature_options = list(SubFeatureOption.objects.all())
    existing = set(
        (ssf.standard.id, ssf.sub_feature.id)
        for ssf in StandardSubFeature.objects.all()
    )

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        standard_sub_features = []
        for row in reader:
            obj = StandardSubFeature(
                standard=[s for s in standards if s.name == row["standard_name"]][0],
                sub_feature=[sf for sf in sub_features if sf.key == row["sub_feature_key"]][0],
                display_name=row["display_name"],
                display_order=int(row["display_order"]),
                default_option=(
                    [sfo for sfo in sub_feature_options if sfo.key == row["default_option_key"]][0]
                    if row["default_option_key"]
                    else None
                ),
                numeric_min=float(row["numeric_min"]) if row["numeric_min"] else None,
                numeric_max=float(row["numeric_max"]) if row["numeric_max"] else None,
                numeric_unit=row["numeric_unit"] if row["numeric_unit"] else None,
                numeric_default=float(row["numeric_default"]) if row["numeric_default"] else None,
                numeric_display_decimal_places=(
                    int(row["numeric_display_decimal_places"]) if row["numeric_display_decimal_places"] else None
                ),
                quantity_field_number=int(row["quantity_field_number"]) if row["quantity_field_number"] else None,
                characteristic_field_number=(
                    int(row["characteristic_field_number"]) if row["characteristic_field_number"] else None
                ),
                maps_numeric_ranges_to_labels=row["maps_numeric_ranges_to_labels"] == "TRUE",
            )
            ident = (obj.standard.id, obj.sub_feature.id)
            if ident not in existing:
                existing.add(ident)
                standard_sub_features.append(obj)
        StandardSubFeature.objects.bulk_create(standard_sub_features)


def create_sub_features(apps, schema_editor):
    SubFeature = apps.get_model("defects", "SubFeature")
    Feature = apps.get_model("defects", "Feature")
    csv_path = DATA_FOLDER / "sub_features.csv"

    features = list(Feature.objects.all())
    existing = set(
        (sf.key)
        for sf in SubFeature.objects.all()
    )

    with csv_path.open(newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        sub_features = []
        for row in reader:
            obj = SubFeature(
                key=row["key"],
                display_name=row["display_name"],
                display_order=int(row["display_order"]),
                feature=[f for f in features if f.key == row["feature_key"]][0],
                kind=row["kind"],
                numeric_kind=row["numeric_kind"] if row["numeric_kind"] else None,
            )
            ident = (obj.key)
            if ident not in existing:
                existing.add(ident)
                sub_features.append(obj)
        SubFeature.objects.bulk_create(sub_features)


class Migration(migrations.Migration):

    dependencies = [
        (
            "defects",
            "0046_create_new_defect_schema_static_data",
        ),
    ]

    operations = [
        migrations.RunPython(create_sub_features, migrations.RunPython.noop),
        migrations.RunPython(create_standard_sub_features, migrations.RunPython.noop),
        migrations.RunPython(create_code_scores, migrations.RunPython.noop),
        migrations.RunPython(create_code_score_requirements, migrations.RunPython.noop),
    ]