from django.core.management import BaseCommand
from django.db import transaction

from api.defects.models import StandardSubFeature


class Command(BaseCommand):
    help = """
    Fixes StandardSubFeature numeric_units that are mistyped.
    This management script is made to be edited and re-run as data issues are found
    """

    @transaction.atomic
    def handle(self, *args, **options):
        StandardSubFeature.objects.filter(numeric_unit="%").update(numeric_unit="PERC")
        StandardSubFeature.objects.filter(
            numeric_unit="PERC", sub_feature__key="water_level_depth"
        ).update(numeric_unit="MM")
        StandardSubFeature.objects.filter(numeric_unit="mm").update(numeric_unit="MM")
        StandardSubFeature.objects.filter(numeric_unit="i").update(numeric_unit="INCH")
        StandardSubFeature.objects.filter(
            sub_feature__key="crack_fracture_width", numeric_unit__isnull=True
        ).update(numeric_unit="MM")
