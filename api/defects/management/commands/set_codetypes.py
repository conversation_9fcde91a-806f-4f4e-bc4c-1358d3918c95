from django.core.management import BaseCommand
from django.db import transaction
from tqdm import tqdm

from api.defects.models.standards import Code, CodeType


class Command(BaseCommand):
    help = """
    Uses SER/STR scores to infer CodeType across Codes
    """

    @transaction.atomic
    def handle(self, *args, **options):
        codes = Code.objects.all()
        print(f"Setting code types for {codes.count()} codes")
        for code in tqdm(codes):
            code_scores = code.scores.all()
            str, ser = False, False
            if code_scores.filter(structural_score__gte=1).exists():
                str = True
            if code_scores.filter(service_score__gte=1).exists():
                ser = True
            if str and not ser:
                code.code_type = CodeType.STR_DEFECT
            elif ser and not str:
                code.code_type = CodeType.SER_DEFECT
            else:
                code.code_type = CodeType.MISC
            code.save()
        print("\nSuccess!")
