from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import Q

from api.common.enums import StatusEnum
from api.defects.models import DefectModelList, DefectScores, StandardSubcategory
from api.files.models import VideoFrames


class Command(BaseCommand):
    help = """
    Script that iterates over frames with no defect set, and attempts to find a defect and defect_model to link them
    to. Only handles frames that are not hidden.
    
    See AD-3008 (https://teamvapar.atlassian.net/jira/software/c/projects/AD/boards/36?selectedIssue=AD-3008)
    for context.
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            default=False,
            help="Do not commit any changes to DB",
        )

    @transaction.atomic
    def handle(self, *args, **options):
        is_dry_run = options["dry_run"]

        standard_subcategories = {
            (s.standard_key_id, "SS" if s.pipe_type_sewer else "SW"): s for s in StandardSubcategory.objects.all()
        }

        frames_qs = VideoFrames.objects.filter(
            is_hidden=False,
            defect_scores__isnull=True,
            parent_video__job_tree__isnull=False,  # Need folder for finding standard and pipe_type to use
            parent_video__inspection__isnull=False,  # Need inspection for determining whether to set as hidden
        ).select_related(
            "defect_model",
            "parent_video",
            "parent_video__inspection",
            "parent_video__job_tree",
        )

        for frame in frames_qs.iterator():
            pipe_type = "SS" if frame.parent_video.job_tree.pipe_type_sewer else "SW"
            defect_model = frame.defect_model
            if defect_model is None:  # Need to set defect model as well
                defect_model = (
                    DefectModelList.objects.filter(name=frame.class_label)
                    .filter(Q(defect_model="Both") | Q(defect_model=pipe_type))
                    .first()
                )
                if defect_model is None:  # Couldn't find a matching defect model, give up
                    f"frame {frame.id}: Couldn't find a matching defect_model for '{frame.class_label}', '{pipe_type}'"
                    continue
                else:
                    self.stdout.write(f"frame {frame.id}: matched to defect_model={defect_model.id}")

            frame.defect_model = defect_model

            subcat_key = (frame.parent_video.job_tree.standard_key_id, pipe_type)
            subcat = standard_subcategories.get(subcat_key)
            if subcat is None:
                self.stderr.write(
                    f"frame {frame.id}: No substandard found for (std={frame.parent_video.job_tree.standard_key_id}, pipe_type='{pipe_type}')"
                )
                continue

            defect = DefectScores.objects.filter(
                defect_key=defect_model,
                sub_standard=subcat,
            ).first()

            if defect is None:
                self.stderr.write(
                    f"frame {frame.id}: Couldn't find a matching defect for defect_model={defect_model.id}, substandard={subcat.id}"
                )
            else:
                self.stdout.write(f"frame {frame.id}: matched to defect={defect.id}")

            frame.defect_scores = defect
            if frame.parent_video.inspection.status != StatusEnum.UPLOADED:
                frame.is_hidden = True
            frame.save()

        if is_dry_run:
            transaction.set_rollback(True)
