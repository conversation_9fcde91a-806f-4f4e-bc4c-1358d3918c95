import csv
import json
import pandas as pd
from decimal import Decimal
from pathlib import Path
from reportlab.lib import colors
from reportlab.lib.pagesizes import landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from tqdm import tqdm

from django.core.management import BaseCommand
from django.db import transaction
from django.db.models import QuerySet, Prefetch

from api.defects.models import CodeScore, StandardSubcategory, CodeRequirement, CodeScoreRequirement


MANAGEMENT_FOLDER = Path(__file__).parent.parent
FIXTURES_FOLDER = MANAGEMENT_FOLDER.parent / "fixtures"
OUTPUT_FOLDER = MANAGEMENT_FOLDER / "baseline_data"


def export_to_pdf(df, filename):
    data_list = [df.columns.tolist()] + df.values.tolist()
    
    table = Table(data_list)
    table.repeatRows = 1
    table.setStyle(TableStyle([
        ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
        ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
        ("ALIGN", (0, 0), (-1, -1), "CENTER"),
        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
        ("BACKGROUND", (0, 1), (-1, -1), colors.beige),
        ("GRID", (0, 0), (-1, -1), 1, colors.black),
    ]))

    table_width, table_height = table.wrap(float("inf"), float("inf"))

    doc = SimpleDocTemplate(
        filename=str(filename),
        pagesize=landscape((table_width + 20, (table_width + 20) / 1.4)),
        topMargin=5,
        bottomMargin=5,
    )
    doc.build([table])


def create_tables(df):
    tables = []
    for substandard, group in df.groupby("Substandard"):
        sub_df = group.drop(columns=["Substandard"])

        sub_df = sub_df.rename(columns={
            "Code": "Code",
            "Description": "Desc.",
            "Cat 1": "C1",
            "Cat 2": "C2",
            "Val 1": "V1",
            "Val 2": "V2",
            "Joint": "Joint",
            "Continuous": "Cont.",
            "Percentage": "%",
            "Start/End": "S/E",
            "STR Score": "STR",
            "SER Score": "SER"
        })

        sub_df["Desc."] = sub_df["Desc."].apply(lambda x: x.split(" - ", 1)[1] if " - " in x else x)

        ordered_cols = [
            "Code", "Desc.", "C1", "C2", "V1", "V2", "Joint", "Cont.", "%", "S/E", "STR", "SER"
        ]
        sub_df = sub_df[ordered_cols]
        sub_df = sub_df.sort_values(by=["Code", "STR", "SER"])

        # Replace nulls and NaNs with blank strings
        sub_df = sub_df.fillna("")

        # Remove empty rows
        sub_df = sub_df[sub_df["Code"] != ""]

        # Remove names from numeric values for this representation
        sub_df["V1"] = sub_df["V1"].apply(
            lambda x: " ".join([item for item in x.split(" ") if any(c.isdigit() for c in item)])
        )
        sub_df["V2"] = sub_df["V2"].apply(
            lambda x: " ".join([item for item in x.split(" ") if any(c.isdigit() for c in item)])
        )

        tables.append((substandard, sub_df))

    return tables     


def normalise_val(value):
    if value is None or value == "":
        return None
    if isinstance(value, (float, Decimal)) and value == int(value):
        return int(value)
    return value


def create_numeric_req_string(req) -> str:
    ssf = getattr(req.sub_feature, "_cached_ssf", None)
    name = ssf.display_name if ssf else ""
    if isinstance(req, CodeRequirement):
        min_val, max_val = req.numeric_option_range_min, req.numeric_option_range_max
    else:
        min_val, max_val = req.numeric_option_min_breakpoint, req.numeric_option_max_breakpoint
    min_val, max_val = normalise_val(min_val), normalise_val(max_val)
    if ssf is None and req.sub_feature:
        ssf = req.sub_feature.standardised_set.first()
        req.sub_feature._cached_ssf = ssf
    unit = ssf.numeric_unit if ssf and ssf.numeric_unit else ""
    if min_val and max_val:
        val_str = f"{min_val}-{max_val}"
    elif min_val:
        val_str = f">{min_val}"
    elif max_val:
        val_str = f"<{max_val}"
    else:
        return ""
    return f"{name} {val_str}{unit}"


def build_codescore_description(ss, cs, cats, vals):
    parts = [f"{cs.code.display_code} - {cs.code.display_name}"]
    parts.extend(f"{cat.required_option.display_name}" for cat in cats)
    for val in vals:
        val_str = create_numeric_req_string(val)
        if val_str:
            parts.append(val_str)
    return " - ".join(parts)


def get_ordered_cats_and_vals(cs):
    c_reqs = cs.code.requirements.all().order_by("sub_feature__standardised_set__display_order")
    cs_reqs = cs.requirements.all().order_by("sub_feature__standardised_set__display_order")
    cats, nums = [], []
    seen_cats, seen_vals = set(), set()
    code_name = cs.code.display_name

    for req in list(c_reqs) + list(cs_reqs):
        if req.required_option:
            rname = req.required_option.display_name
            if rname not in seen_cats and rname not in code_name:
                cats.append(req)
                seen_cats.add(rname)
        else:
            val_str = create_numeric_req_string(req)
            if val_str and val_str not in seen_vals:
                nums.append(req)
                seen_vals.add(val_str)

    return cats[:2], nums[:2]


def flatten_code_scores(code_scores: QuerySet[CodeScore]):
    flattened_data = []
    for cs in tqdm(code_scores, desc="Flattening CodeScores"):
        for ss in cs.standard_subcategories.all():
            cats, vals = get_ordered_cats_and_vals(cs)

            flattened_data.append({
                "Substandard": ss.comment or "",
                "Code": cs.code.display_code or "",
                "Description": build_codescore_description(ss, cs, cats, vals),
                "Joint": "Required" if cs.code.at_joint_required else "Allowed" if cs.code.at_joint_allowed else "",
                "Continuous": "Allowed" if cs.code.continuous_allowed else "",
                "Percentage": "Required" if cs.code.percent_field_required else "",
                "Start/End": "Start" if cs.code.is_start_code else "End" if cs.code.is_end_code else "",
                "Cat 1": cats[0].required_option.display_name if cats else None,
                "Cat 2": cats[1].required_option.display_name if len(cats) > 1 else None,
                "Val 1": create_numeric_req_string(vals[0]) if vals else None,
                "Val 2": create_numeric_req_string(vals[1]) if len(vals) > 1 else None,
                "STR Score": normalise_val(cs.structural_score),
                "SER Score": normalise_val(cs.service_score),
            })

    flattened_data.sort(key=lambda x: (x["Substandard"], x["Code"]))

    with open(OUTPUT_FOLDER / "flattened_code_scores.csv", "w", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=flattened_data[0].keys())
        writer.writeheader()
        writer.writerows(flattened_data)

    print(f"Output csv written to {OUTPUT_FOLDER}/flattened_code_scores.csv",)


def flatten_defect_scores(defect_scores: list[dict]):
    flattened_data = []
    for ds in tqdm(defect_scores, desc="Flattening DefectScores"):
        flattened_data.append({
            "Substandard": StandardSubcategory.objects.get(id=ds["fields"]["sub_standard"]).comment or "",
            "Code": ds["fields"]["defect_code"] or "",
            "Description": ds["fields"]["defect_description"],
            "Joint": "Allowed" if ds["fields"]["at_joint_required"] else "",
            "Continuous": "Allowed" if ds["fields"]["continuous_score"] else "",
            "Percentage": "Required" if ds["fields"]["percentage_required"] else "",
            "Start/End": "Start" if ds["fields"]["start_survey"] else "End" if ds["fields"]["end_survey"] else "",
            "Cat 1": ds["fields"]["characterisation_1"],
            "Cat 2": ds["fields"]["characterisation_2"],
            "Val 1": ds["fields"]["quantity_1_desc"],
            "Val 2": ds["fields"]["quantity_2_desc"],
            "STR Score": ds["fields"]["structural_score"],
            "SER Score": ds["fields"]["service_score"],
        })

    flattened_data.sort(key=lambda x: (x["Substandard"], x["Code"]))
    
    with open(OUTPUT_FOLDER / "flattened_defect_scores.csv", "w", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=flattened_data[0].keys())
        writer.writeheader()
        writer.writerows(flattened_data)

    print(f"Output csv written to {OUTPUT_FOLDER}/flattened_defect_scores.csv",)


class Command(BaseCommand):
    help = """
    Generates a flattened view of both the old DefectScores format and of the new defect schema for comparison of data.
    This format is based loosely on a combination of the AU standard scoring tables and the PACP code tables.
    """

    @transaction.atomic
    def handle(self, *args, **options):
        with open(FIXTURES_FOLDER / "defect_scores_prod.json") as f:
            defect_scores = json.load(f)
        code_scores = CodeScore.objects.select_related(
            "code"
        ).prefetch_related(
            "standard_subcategories",
            Prefetch("code__requirements", queryset=CodeRequirement.objects.select_related("sub_feature", "required_option")),
            Prefetch("requirements", queryset=CodeScoreRequirement.objects.select_related("sub_feature", "required_option")),
        )

        if (OUTPUT_FOLDER / "flattened_defect_scores.csv").exists():
            print("DefectScores output csv already exists, skipping...")
        else:
            flatten_defect_scores(defect_scores)

        if (OUTPUT_FOLDER / "flattened_code_scores.csv").exists():
            print("CodeScores output csv already exists, skipping...")
        else:
            flatten_code_scores(code_scores)

        ds_df = pd.read_csv(OUTPUT_FOLDER / "flattened_defect_scores.csv")
        cs_df = pd.read_csv(OUTPUT_FOLDER / "flattened_code_scores.csv")

        ds_tables = create_tables(ds_df)
        cs_tables = create_tables(cs_df)

        for dt in tqdm(ds_tables, desc="Exporting DefectScores PDFs"):
            export_to_pdf(dt[1], OUTPUT_FOLDER / f"defect_scores_{dt[0].replace(' ', '_').lower()}.pdf")
        for ct in tqdm(cs_tables, desc="Exporting CodeScore PDFs"):
            export_to_pdf(ct[1], OUTPUT_FOLDER / f"code_scores_{ct[0].replace(' ', '_').lower()}.pdf")

        print("Done!")
