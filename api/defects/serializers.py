from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
import vapar.core.coding

from .models import (
    DefectScores,
    Standard,
    StandardHeader,
    StandardSubcategory,
    Code,
    CodeRequirement,
    Feature,
    SubFeature,
    CodeScore,
    CodeScoreRequirement,
    SubFeatureOption,
    StandardSubFeature,
    StandardSubFeatureOption,
    SubFeatureNumericKind,
)
from .models import MLLabelFeatureMapping, MLLabelSubFeatureMapping
from .models.standards import SubFeatureValDetails
from ..base.models import Header


class DefectSerializer(serializers.ModelSerializer):
    defect_label = serializers.SerializerMethodField(read_only=True)

    def get_defect_label(self, obj) -> str | None:
        if obj.defect_description and obj.defect_code:
            if obj.defect_code not in obj.defect_description:
                return f"{obj.defect_code} - {obj.defect_description}"
            return obj.defect_description
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "defect_code",
            "defect_description",
            "defect_label",
            "clock_position_required",
            "clock_spread_possible",
            "at_joint_required",
            "percentage_required",
            "start_survey",
            "end_survey",
        ]


class StandardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Standard
        fields = ["id", "display_name", "name"]


class StandardSubcategorySerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True, source="standard_key")
    region = serializers.SerializerMethodField()

    def get_region(self, data) -> str:
        return data.region.code

    class Meta:
        model = StandardSubcategory
        fields = ["id", "material_type", "pipe_type_sewer", "comment", "standard", "region"]


class StandardHeaderSerializer(serializers.ModelSerializer):
    options_description = serializers.SerializerMethodField(read_only=True)
    type = serializers.SerializerMethodField(read_only=True)
    mapped_field_name = serializers.SerializerMethodField(read_only=True)
    display_name = serializers.SerializerMethodField(read_only=True)

    def get_options_description(self, obj) -> list[str]:
        if obj.options_description:
            return [x.strip() for x in obj.options_description.split(",")]
        return []

    def get_type(self, obj) -> Header.HeaderType:
        return obj.header.type

    def get_mapped_field_name(self, obj) -> str | None:
        if obj.header.mapped_mpl_field and obj.header.mapped_mpl_field != obj.header.name:
            return obj.header.mapped_mpl_field
        return None

    def get_display_name(self, obj) -> str | None:
        if obj.header.display_name:
            return obj.header.display_name
        return obj.header.name

    class Meta:
        model = StandardHeader
        fields = [
            "name",
            "required",
            "description",
            "type",
            "data_type",
            "options_selections",
            "options_description",
            "mapped_field_name",
            "display_name",
        ]


class AllDefectsSerializer(serializers.ModelSerializer):
    substandard = StandardSubcategorySerializer(read_only=True, source="sub_standard")
    defect_model_name = serializers.SerializerMethodField(read_only=True)
    defect_model_id = serializers.SerializerMethodField(read_only=True)

    def get_defect_model_name(self, obj) -> str | None:
        if obj.defect_key:
            if obj.defect_key.name:
                return obj.defect_key.name
        return None

    def get_defect_model_id(self, obj) -> int | None:
        if obj.defect_key:
            return obj.defect_key.id
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "substandard",
            "defect_description",
            "defect_model_name",
            "defect_model_id",
            "service_score",
            "structural_score",
            "defect_type",
            "quantity_1_default_val",
            "quantity_1_units",
            "quantity_2_default_val",
            "quantity_2_units",
            "defect_code",
            "at_joint_required",
            "material_applied",
            "characterisation_1",
            "characterisation_2",
            "clock_position_required",
            "clock_spread_possible",
            "percentage_required",
            "start_survey",
            "end_survey",
            "repair_priority",
            "repair_category",
            "fastpass_code",
            "continuous_score",
            "is_shown",
        ]


class MLLabelFeatureMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = MLLabelFeatureMapping
        fields = [
            "feature_id",
            "ml_label",
        ]


class MLLabelSubFeatureMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = MLLabelSubFeatureMapping
        fields = [
            "sub_feature_id",
            "ml_label",
            "ml_label_model_kind",
            "option_id",
            "numeric_unit",
            "numeric_value",
            "numeric_min",
            "numeric_max",
        ]


class StandardSubFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = StandardSubFeature
        fields = [
            "id",
            "standard_id",
            "sub_feature_id",
            "display_name",
            "display_order",
            "default_option_id",
            "numeric_min",
            "numeric_max",
            "numeric_unit",
            "numeric_default",
            "numeric_display_decimal_places",
            "quantity_field_number",
            "characteristic_field_number",
            "is_report_percent_field",
        ]


class StandardSubFeatureOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = StandardSubFeatureOption
        fields = [
            "id",
            "standard_id",
            "option_id",
            "display_code",
            "display_name",
            "display_order",
        ]


class SubFeatureOptionSerializer(serializers.ModelSerializer):
    standardised = serializers.SerializerMethodField()

    @extend_schema_field(StandardSubFeatureOptionSerializer(allow_null=True))
    def get_standardised(self, obj: SubFeatureOption) -> dict | None:
        std_id = self.context.get("with_standard_id")
        if not std_id:
            return None
        std_option = next((o for o in obj.standardised_set.all() if o.standard_id == std_id), None)
        if not std_option:
            return None
        return StandardSubFeatureOptionSerializer(instance=std_option).data

    class Meta:
        model = SubFeatureOption
        fields = [
            "id",
            "key",
            "display_name",
            "display_order",
            "sub_feature_id",
            "standardised",
        ]


class SubFeatureFullDetailSerializer(serializers.ModelSerializer):
    mapped_ml_labels = MLLabelSubFeatureMappingSerializer(many=True, source="mapped_sub_feature_ml_labels")
    options = SubFeatureOptionSerializer(many=True)
    standardised = serializers.SerializerMethodField()

    @extend_schema_field(StandardSubFeatureSerializer(allow_null=True))
    def get_standardised(self, obj):
        std_id = self.context.get("with_standard_id")
        if not std_id:
            return None
        std_sub_feature = next((s for s in obj.standardised_set.all() if s.standard_id == std_id), None)
        if not std_sub_feature:
            return None
        return StandardSubFeatureSerializer(instance=std_sub_feature).data

    class Meta:
        model = SubFeature
        fields = [
            "id",
            "feature_id",
            "key",
            "display_name",
            "display_order",
            "kind",
            "numeric_kind",
            "options",
            "mapped_ml_labels",
            "standardised",
        ]


class FeatureFullDetailSerializer(serializers.ModelSerializer):
    sub_features = SubFeatureFullDetailSerializer(many=True)
    mapped_ml_labels = MLLabelFeatureMappingSerializer(many=True)

    class Meta:
        model = Feature
        fields = [
            "id",
            "key",
            "display_name",
            "sub_features",
            "mapped_ml_labels",
        ]


class FeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feature
        fields = [
            "id",
            "key",
            "display_name",
        ]


class SubFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubFeature
        fields = [
            "id",
            "feature_id",
            "key",
            "display_name",
            "display_order",
            "kind",
            "numeric_kind",
        ]


class CodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Code
        fields = [
            "id",
            "standard_id",
            "feature_id",
            "code_type",
            "display_code",
            "display_name",
            "at_joint_required",
            "at_joint_allowed",
            "continuous_required",
            "continuous_allowed",
            "remarks_required",
            "percent_field_required",
            "percent_field_allowed",
            "clock_position_from_required",
            "clock_position_from_min",
            "clock_position_from_max",
            "clock_position_to_required",
            "clock_position_to_min",
            "clock_position_to_max",
            "is_start_code",
            "is_end_code",
        ]


class CodeRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeRequirement
        fields = [
            "id",
            "code_id",
            "sub_feature_id",
            "required_option_id",
            "numeric_option_unit",
            "numeric_option_range_max",
            "numeric_option_range_min",
        ]


class CodeScoreRequirementSerializer(serializers.ModelSerializer):
    required_option = SubFeatureOptionSerializer(read_only=True, allow_null=True)

    class Meta:
        model = CodeScoreRequirement
        fields = [
            "id",
            "code_score_id",
            "sub_feature_id",
            "required_option",
            "numeric_option_unit",
            "numeric_option_min_breakpoint",
            "numeric_option_max_breakpoint",
        ]


class CodeScoreFullDetailSerializer(serializers.ModelSerializer):
    """Details of a code applicable under a code, including its requirements."""

    requirements = CodeScoreRequirementSerializer(many=True, read_only=True)

    class Meta:
        model = CodeScore
        fields = [
            "id",
            "code_id",
            "service_score",
            "structural_score",
            "required_clock_position_min",
            "required_clock_position_max",
            "required_clock_position_min_spread",
            "required_clock_position_max_spread",
            "added_service_score_per_metre",
            "added_structural_score_per_metre",
            "requirements",
        ]


class CodeFullDetailSerializer(serializers.ModelSerializer):
    """Details of a code, including its requirements and possible scores that could be applied."""

    requirements = CodeRequirementSerializer(many=True, read_only=True)
    scores = CodeScoreFullDetailSerializer(many=True, read_only=True)

    class Meta:
        model = Code
        fields = [
            "id",
            "standard_id",
            "feature",
            "code_type",
            "display_code",
            "display_name",
            "at_joint_required",
            "at_joint_allowed",
            "continuous_required",
            "continuous_allowed",
            "remarks_required",
            "percent_field_required",
            "percent_field_allowed",
            "clock_position_from_required",
            "clock_position_from_min",
            "clock_position_from_max",
            "clock_position_to_required",
            "clock_position_to_min",
            "clock_position_to_max",
            "is_start_code",
            "is_end_code",
            "requirements",
            "scores",
        ]


def enumerate_code_variants(code: Code, substandard: StandardSubcategory) -> "CodeVariantsSerializer":
    """
    List combinations of values that correspond to a scored code under a sub-standard.

    The purpose of this is to list possible defect descriptions to the FE in a format similar to the previously
    used DefectScores.description field.

    :returns: A serializer populated with data
    """

    values_required_for_code = _build_example_values_from_code_requirements(code, substandard)
    variants = _build_code_variants_serializer_data(values_required_for_code, code, substandard)

    variants_data = {"code": code, "code_requirements": code.requirements.all(), "variants": variants}
    serializer = CodeVariantsSerializer(variants_data)
    return serializer


def _build_example_values_from_code_requirements(
    code: Code, substandard: StandardSubcategory
) -> list[SubFeatureValDetails]:
    values = []
    for req in code.requirements.all():
        if req.sub_feature is None:
            continue
            
        try:
            standardised_sf = next(
                sf for sf in req.sub_feature.standardised_set.all() if sf.standard_id == substandard.standard_key_id
            )
        except StopIteration:
            raise ValueError(
                f"The sub-feature {req.sub_feature} does not have a standard representation for the"
                f" substandard {substandard}."
            )
        numeric_unit = standardised_sf.get_default_numeric_unit()
        val = vapar.core.coding.SubFeatureValue(
            sub_feature_id=req.sub_feature_id,
            numeric_value=None,
            numeric_range_max=req.numeric_option_range_max,
            numeric_range_min=req.numeric_option_range_min,
            numeric_unit=numeric_unit,
            option_id=req.required_option_id,
        )
        values.append(SubFeatureValDetails(val, req.sub_feature, req.required_option))
    return values


def _build_code_variants_serializer_data(
    values_required_for_code: list[SubFeatureValDetails],
    code: Code,
    substd: StandardSubcategory,
) -> list[dict]:
    variants = []
    for score in code.scores.all():
        if substd not in score.standard_subcategories.all():
            continue  # This score is not applicable to the given substandard so ignore it

        values_required_for_score = []
        for score_req in score.requirements.all():
            if score_req.sub_feature is None:
                continue
                
            try:
                numeric_unit = next(
                    sf
                    for sf in score_req.sub_feature.standardised_set.all()
                    if sf.standard_id == substd.standard_key_id
                ).get_default_numeric_unit()
            except (StopIteration, ValueError):
                numeric_unit = SubFeatureNumericKind(score_req.sub_feature.numeric_kind).get_base_unit()

            val = vapar.core.coding.SubFeatureValue(
                sub_feature_id=score_req.sub_feature_id,
                numeric_value=None,
                numeric_range_max=score_req.numeric_option_max_breakpoint,
                numeric_range_min=score_req.numeric_option_min_breakpoint,
                numeric_unit=numeric_unit,
                option_id=score_req.required_option_id,
            )
            values_required_for_score.append(
                SubFeatureValDetails(val, score_req.sub_feature, score_req.required_option)
            )

        all_required_values = values_required_for_code + values_required_for_score

        description = code.build_description_from_values(substd.standard_key_id, all_required_values)
        if not description:
            continue  # Assume we want only codes that can generate a description

        variants.append(
            {
                "display_description": description,
                "scoring": score,
                "scoring_requirements": score.requirements,
            }
        )

    return variants


class CodeScoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeScore
        fields = [
            "id",
            "code_id",
            "service_score",
            "structural_score",
            "required_clock_position_min",
            "required_clock_position_max",
            "required_clock_position_min_spread",
            "required_clock_position_max_spread",
            "added_service_score_per_metre",
            "added_structural_score_per_metre",
        ]


class CodeScoredVariantSerializer(serializers.Serializer):
    display_description = serializers.CharField()
    scoring = CodeScoreSerializer(read_only=True)
    scoring_requirements = CodeScoreRequirementSerializer(many=True)


class CodeVariantsSerializer(serializers.Serializer):
    code = CodeSerializer(read_only=True)
    code_requirements = CodeRequirementSerializer(many=True, read_only=True)
    variants = CodeScoredVariantSerializer(many=True, read_only=True)
