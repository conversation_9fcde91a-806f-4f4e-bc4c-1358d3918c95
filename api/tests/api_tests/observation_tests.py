import pytest
from rest_framework import status

from api.defects.models import Feature, SubFeature, SubFeatureOption, SubFeatureUnit
from api.inspections.models import Inspection, Observation
from api.tests import factory
from api.tests.settings import ObservationSettings

pytestmark = pytest.mark.django_db


@pytest.fixture
def inspection_with_keyframes(single_inspection: Inspection) -> Inspection:
    footage = factory.create_footage(video_file=single_inspection.file, n_keyframes=10)
    single_inspection.footage = footage
    single_inspection.save()
    return single_inspection


@pytest.fixture
def abandonment_feature():
    return Feature.objects.get(key="abandonment")


@pytest.fixture
def abandonment_reason_subfeature():
    return SubFeature.objects.get(key="abandonment_reason")


@pytest.fixture
def abandonment_reason_option():
    return SubFeatureOption.objects.get(key="abandonment_reason_equipment_failure")


@pytest.fixture
def break_feature():
    return Feature.objects.get(key="break")


@pytest.fixture
def break_length_sub_feature():
    return SubFeature.objects.get(key="break_length")


@pytest.fixture
def break_type_sub_feature():
    return SubFeature.objects.get(key="break_type")


@pytest.fixture
def break_type_option():
    return SubFeatureOption.objects.get(key="break_type_missing")


@pytest.fixture
def break_type_alternate_option():
    return SubFeatureOption.objects.get(key="break_type_displaced")


class TestObservationCreation:
    def test_create_observation_with_features(
        self,
        client,
        service_user_key: str,
        asset_owner_org,
        inspection_with_keyframes: Inspection,
        abandonment_feature: Feature,
        abandonment_reason_option: SubFeature,
        abandonment_reason_subfeature: SubFeatureOption,
        observation_settings: ObservationSettings,
    ):
        keyframe_id = inspection_with_keyframes.footage.keyframes.get(sequence_number=1).id

        res = client.put(
            observation_settings.get_inspection_observations_url(inspection_with_keyframes.uuid),
            data=[
                {
                    "keyframes": [str(keyframe_id)],
                    "remarks": "some remarks",
                    "features": [
                        {
                            "feature": abandonment_feature.id,
                            "clockPositionFrom": 1,
                            "featureClassPredictionCertainty": 0.9,
                            "subFeatures": [
                                {
                                    "subFeature": abandonment_reason_subfeature.id,
                                    "selectedOption": abandonment_reason_option.id,
                                }
                            ],
                        },
                    ],
                },
            ],
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        )

        # Check response data
        assert res.status_code == status.HTTP_201_CREATED
        res_data = res.json()
        assert len(res_data) == 1
        obs_data = res_data[0]
        assert obs_data["inspectionId"] == str(inspection_with_keyframes.uuid)
        assert obs_data["remarks"] == "some remarks"
        assert obs_data["keyframes"] == [str(keyframe_id)]
        assert len(obs_data["features"]) == 1
        feature_data = obs_data["features"][0]
        assert feature_data["feature"] == abandonment_feature.id
        assert feature_data["clockPositionFrom"] == 1
        assert feature_data["clockPositionTo"] is None
        assert feature_data["featureClassPredictionCertainty"] == 0.9
        assert feature_data["code"] is None
        assert feature_data["scoring"] is None

        # Check that observation was created correctly
        obs = inspection_with_keyframes.observations.first()
        assert obs.remarks == "some remarks"
        assert obs.keyframes.count() == 1
        assert obs.keyframes.first().id == keyframe_id
        assert obs.features.count() == 1
        # Check feature
        obs_feature = obs.features.first()
        assert obs_feature.feature == abandonment_feature
        assert obs_feature.clock_position_from == 1
        assert obs_feature.clock_position_to is None
        assert obs_feature.feature_class_prediction_certainty == 0.9
        assert obs_feature.code is None
        assert obs_feature.scoring is None

        # Check the sub-feature
        assert obs_feature.sub_features.count() == 1
        obs_sub_feature = obs_feature.sub_features.first()
        assert obs_sub_feature.sub_feature == abandonment_reason_subfeature
        assert obs_sub_feature.selected_option == abandonment_reason_option
        assert obs_sub_feature.numeric_value is None
        assert obs_sub_feature.numeric_range_min is None
        assert obs_sub_feature.numeric_range_max is None
        assert obs_sub_feature.numeric_unit is None

    def test_create_observation_with_multiple_features_and_subfeatures(
        self,
        client,
        service_user_key: str,
        asset_owner_org,
        inspection_with_keyframes: Inspection,
        abandonment_feature: Feature,
        abandonment_reason_option: SubFeature,
        abandonment_reason_subfeature: SubFeatureOption,
        break_feature: Feature,
        break_type_sub_feature: SubFeature,
        break_length_sub_feature: SubFeature,
        break_type_option: SubFeatureOption,
        observation_settings: ObservationSettings,
    ):
        keyframe_ids = [str(kf.id) for kf in inspection_with_keyframes.footage.keyframes.all()]
        res = client.put(
            observation_settings.get_inspection_observations_url(inspection_with_keyframes.uuid),
            data=[
                {
                    "keyframes": keyframe_ids,
                    "remarks": "complex features",
                    "features": [
                        {
                            "feature": abandonment_feature.id,
                            "clockPositionFrom": 1,
                            "featureClassPredictionCertainty": 0.9,
                            "subFeatures": [
                                {
                                    "subFeature": abandonment_reason_subfeature.id,
                                    "selectedOption": abandonment_reason_option.id,
                                }
                            ],
                        },
                        {
                            "feature": break_feature.id,
                            "clockPositionFrom": 2,
                            "featureClassPredictionCertainty": 0.8,
                            "subFeatures": [
                                {
                                    "subFeature": break_type_sub_feature.id,
                                    "selectedOption": break_type_option.id,
                                },
                                {
                                    "subFeature": break_length_sub_feature.id,
                                    "numericValue": 0.15,
                                    "numericUnit": SubFeatureUnit.MILLIMETRES.value,
                                },
                            ],
                        },
                    ],
                },
            ],
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        )

        # Check response data
        assert res.status_code == status.HTTP_201_CREATED
        res_data = res.json()

        assert len(res_data) == 1
        obs_data = res_data[0]
        assert obs_data["inspectionId"] == str(inspection_with_keyframes.uuid)
        assert obs_data["remarks"] == "complex features"

        # Check keyframes
        assert sorted(obs_data["keyframes"]) == sorted(keyframe_ids)

        # Check the observation was created correctly
        assert inspection_with_keyframes.observations.count() == 1
        obs = inspection_with_keyframes.observations.first()
        assert obs.remarks == "complex features"
        assert obs.keyframes.count() == len(keyframe_ids)
        assert set(str(id) for id in obs.keyframes.values_list("id", flat=True)) == set(keyframe_ids)
        assert obs.features.count() == 2

        # Check the first feature abandonment)
        obs_feature_1 = obs.features.get(feature=abandonment_feature)
        assert obs_feature_1.clock_position_from == 1
        assert obs_feature_1.clock_position_to is None
        assert obs_feature_1.feature_class_prediction_certainty == 0.9
        assert obs_feature_1.code is None
        assert obs_feature_1.scoring is None
        assert obs_feature_1.sub_features.count() == 1
        obs_sub_feature_1 = obs_feature_1.sub_features.first()
        assert obs_sub_feature_1.sub_feature == abandonment_reason_subfeature
        assert obs_sub_feature_1.selected_option == abandonment_reason_option
        assert obs_sub_feature_1.numeric_value is None
        assert obs_sub_feature_1.numeric_range_min is None
        assert obs_sub_feature_1.numeric_range_max is None
        assert obs_sub_feature_1.numeric_unit is None

        # Check the second feature (break)
        obs_feature_2 = obs.features.get(feature=break_feature)
        assert obs_feature_2.clock_position_from == 2
        assert obs_feature_2.clock_position_to is None
        assert obs_feature_2.feature_class_prediction_certainty == 0.8
        assert obs_feature_2.code is None
        assert obs_feature_2.scoring is None
        assert obs_feature_2.sub_features.count() == 2
        obs_sub_feature_2_1 = obs_feature_2.sub_features.get(sub_feature=break_type_sub_feature)
        assert obs_sub_feature_2_1.selected_option == break_type_option
        assert obs_sub_feature_2_1.numeric_value is None
        assert obs_sub_feature_2_1.numeric_range_min is None
        assert obs_sub_feature_2_1.numeric_range_max is None
        assert obs_sub_feature_2_1.numeric_unit is None
        obs_sub_feature_2_2 = obs_feature_2.sub_features.get(sub_feature=break_length_sub_feature)
        assert obs_sub_feature_2_2.selected_option is None
        assert obs_sub_feature_2_2.numeric_value == 0.15
        assert obs_sub_feature_2_2.numeric_range_min is None
        assert obs_sub_feature_2_2.numeric_range_max is None
        assert obs_sub_feature_2_2.numeric_unit == SubFeatureUnit.MILLIMETRES.value


class TestObservationUpdate:
    @pytest.fixture
    def observation_with_features(
        self,
        single_inspection,
        abandonment_feature,
        abandonment_reason_subfeature,
        abandonment_reason_option,
        break_feature,
        break_length_sub_feature,
        break_type_sub_feature,
        break_type_option,
    ):
        """
        An observation with a feature 'Abandonment' and a feature 'Break'.
        The former has a single sub-feature 'Abandonment Reason' while the latter has 'Length' and 'Type' sub-features.
        """
        obs = factory.create_observation(single_inspection, n_keyframes=2, n_features=0)
        feat_1 = factory.create_observed_feature(
            observation=obs,
            feature=abandonment_feature,
            clock_position_from=1,
            prediction_certainty=0.9,
        )
        feat_1.sub_features.create(
            sub_feature=abandonment_reason_subfeature,
            selected_option=abandonment_reason_option,
        )

        feat_2 = factory.create_observed_feature(
            observation=obs,
            feature=break_feature,
            clock_position_from=2,
            prediction_certainty=0.8,
        )
        feat_2.sub_features.create(
            sub_feature=break_type_sub_feature,
            selected_option=break_type_option,
        )
        feat_2.sub_features.create(
            sub_feature=break_length_sub_feature,
            numeric_value=0.15,
            numeric_unit=SubFeatureUnit.METRES.value,
        )

        return obs

    @pytest.fixture
    def observation(self, single_inspection):
        """Create an observation with no features"""
        obs = factory.create_observation(single_inspection, n_keyframes=2, n_features=0)
        return obs

    @pytest.fixture
    def additional_keyframe(self, observation_with_features):
        """
        Create an additional keyframe for the observation.
        """
        return observation_with_features.inspection.footage.append_new_keyframe(
            time_reference_milliseconds=6000,
            chainage=0.3,
        )

    def test_modify_remarks(
        self,
        standard_user,
        client,
        observation_with_features,
        abandonment_feature,
        abandonment_reason_subfeature,
        abandonment_reason_option,
        observation_settings,
    ):
        """
        Test modifying only the remarks. Check that other fields and child entities remain unchanged.
        """

        client.force_login(user=standard_user)
        new_remarks = "Updated remarks"

        keyframes_before = set(kf.id for kf in observation_with_features.keyframes.all())

        res = client.patch(
            observation_settings.get_observation_url(observation_with_features.id),
            data={"remarks": new_remarks},
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        observation_with_features.refresh_from_db()
        assert observation_with_features.remarks == new_remarks

        # Check that other fields remain unchanged
        assert set(kf.id for kf in observation_with_features.keyframes.all()) == keyframes_before
        feat_1 = observation_with_features.features.get(feature=abandonment_feature)
        assert feat_1.clock_position_from == 1
        assert feat_1.feature_class_prediction_certainty == 0.9
        assert feat_1.code is None
        assert feat_1.sub_features.count() == 1
        sub_feat_1 = feat_1.sub_features.first()
        assert sub_feat_1.sub_feature == abandonment_reason_subfeature
        assert sub_feat_1.selected_option == abandonment_reason_option

    def test_add_keyframe(
        self,
        standard_user,
        client,
        observation_with_features,
        additional_keyframe,
        observation_settings,
    ):
        """
        Test adding a keyframe to an existing observation.
        """

        client.force_login(user=standard_user)

        old_keyframes = [str(kf.id) for kf in observation_with_features.keyframes.all()]

        res = client.patch(
            observation_settings.get_observation_url(observation_with_features.id),
            data={
                "keyframes": [*old_keyframes, str(additional_keyframe.id)],
            },
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        observation_with_features.refresh_from_db()

        new_keyframes = {str(kf.id) for kf in observation_with_features.keyframes.all()}
        assert new_keyframes == {*old_keyframes, str(additional_keyframe.id)}

    def test_remove_keyframe(
        self,
        standard_user,
        client,
        observation_with_features,
        observation_settings,
    ):
        client.force_login(user=standard_user)
        to_remove = observation_with_features.keyframes.first()
        to_keep = [str(kf.id) for kf in observation_with_features.keyframes.all() if kf != to_remove]
        res = client.patch(
            observation_settings.get_observation_url(observation_with_features.id),
            data={
                "keyframes": to_keep,
            },
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        observation_with_features.refresh_from_db()

        remaining_keyframes = {str(kf.id) for kf in observation_with_features.keyframes.all()}
        assert remaining_keyframes == set(to_keep)

    def test_add_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_length_sub_feature,
        break_type_sub_feature,
        break_type_option,
        observation_settings,
    ):
        """
        Test adding a new feature to an existing observation.
        """

        client.force_login(user=standard_user)

        # Remove the existing 'break' feature so we can add it back again
        observation_with_features.features.get(feature__key="break").delete()
        assert observation_with_features.features.count() == 1, "Just checking the setup is correct"

        new_feature_data = {
            "feature": break_feature.id,
            "clockPositionFrom": 3,
            "featureClassPredictionCertainty": 0.85,
            "subFeatures": [
                {
                    "subFeature": break_type_sub_feature.id,
                    "selectedOption": break_type_option.id,
                },
                {
                    "subFeature": break_length_sub_feature.id,
                    "numericValue": 0.2,
                    "numericUnit": SubFeatureUnit.MILLIMETRES.value,
                },
            ],
        }

        res = client.post(
            observation_settings.get_observation_features_url(observation_with_features.id),
            data=new_feature_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_201_CREATED
        observation_with_features.refresh_from_db()

        # Check the new feature was added correctly
        assert observation_with_features.features.count() == 2
        new_feature = observation_with_features.features.get(feature=break_feature)
        assert new_feature.clock_position_from == 3
        assert new_feature.feature_class_prediction_certainty == 0.85
        assert new_feature.code is None
        assert new_feature.scoring is None
        assert new_feature.sub_features.count() == 2

    def test_create_feature(
        self,
        standard_user,
        client,
        observation,
        abandonment_feature,
        abandonment_reason_subfeature,
        abandonment_reason_option,
        observation_settings,
    ):
        """Test creating a new feature for an existing observation"""
        client.force_login(user=standard_user)

        new_feature_data = {
            "feature": abandonment_feature.id,
            "clockPositionFrom": 3,
            "clockPositionTo": 9,
            "featureClassPredictionCertainty": 0.85,
            "subFeatures": [
                {
                    "subFeature": abandonment_reason_subfeature.id,
                    "selectedOption": abandonment_reason_option.id,
                }
            ],
        }

        res = client.post(
            observation_settings.get_observation_features_url(observation.id),
            data=new_feature_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_201_CREATED
        observation.refresh_from_db()

        # Check the new feature was added correctly
        assert observation.features.count() == 1
        new_feature = observation.features.first()
        assert new_feature.clock_position_from == 3
        assert new_feature.clock_position_to == 9
        assert new_feature.feature_class_prediction_certainty == 0.85
        assert new_feature.feature.id == abandonment_feature.id

        # Check sub-feature was created correctly
        assert new_feature.sub_features.count() == 1
        sub_feature = new_feature.sub_features.first()
        assert sub_feature.sub_feature.id == abandonment_reason_subfeature.id
        assert sub_feature.selected_option.id == abandonment_reason_option.id

    def test_update_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        observation_settings,
    ):
        """Test updating an existing feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)
        original_sub_features_count = feature.sub_features.count()

        update_data = {
            "clockPositionFrom": 4,
            "clockPositionTo": 8,
            "featureClassPredictionCertainty": 0.95,
        }

        res = client.patch(
            observation_settings.get_observed_feature_detail_url(feature.id),
            data=update_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        feature.refresh_from_db()

        # Check the feature was updated correctly
        assert feature.clock_position_from == 4
        assert feature.clock_position_to == 8
        assert feature.feature_class_prediction_certainty == 0.95

        # Ensure sub-features remained untouched
        assert feature.sub_features.count() == original_sub_features_count

    def test_update_feature_partial(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        observation_settings,
    ):
        """Test partially updating a feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)
        original_clock_position = feature.clock_position_from

        update_data = {
            "featureClassPredictionCertainty": 0.75,
        }

        res = client.patch(
            observation_settings.get_observed_feature_detail_url(feature.id),
            data=update_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        feature.refresh_from_db()

        # Check only the specified field was updated
        assert feature.clock_position_from == original_clock_position
        assert feature.feature_class_prediction_certainty == 0.75

    def test_create_sub_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_length_sub_feature,
        observation_settings,
    ):
        """Test adding a new sub-feature to an existing feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)
        feature.sub_features.filter(sub_feature=break_length_sub_feature).delete()  # Remove so we can add it back
        feature.save()
        original_sub_features_count = feature.sub_features.count()

        new_sub_feature_data = {
            "subFeature": break_length_sub_feature.id,
            "numericValue": 0.25,
            "numericUnit": "INCH",
        }

        res = client.post(
            observation_settings.get_observed_sub_features_url(feature.id),
            data=new_sub_feature_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_201_CREATED
        feature.refresh_from_db()

        # Check the new sub-feature was added correctly
        assert feature.sub_features.count() == original_sub_features_count + 1
        new_sub_feature = feature.sub_features.get(sub_feature=break_length_sub_feature)
        assert new_sub_feature.numeric_value == 0.25
        assert new_sub_feature.numeric_unit == "INCH"

    def test_update_sub_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_length_sub_feature,
        observation_settings,
    ):
        """Test updating an existing sub-feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)
        sub_feature = feature.sub_features.get(sub_feature=break_length_sub_feature)

        update_data = {
            "numericValue": 0.35,
            "numericUnit": SubFeatureUnit.MILLIMETRES.value,
        }

        res = client.patch(
            observation_settings.get_observed_sub_feature_detail_url(sub_feature.id),
            data=update_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        sub_feature.refresh_from_db()

        # Check the sub-feature was updated correctly
        assert sub_feature.numeric_value == 0.35
        assert sub_feature.numeric_unit == SubFeatureUnit.MILLIMETRES.value

    def test_update_categorical_sub_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_type_sub_feature,
        break_type_option,
        break_type_alternate_option,
        observation_settings,
    ):
        """Test updating a categorical sub-feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)
        sub_feature = feature.sub_features.get(sub_feature=break_type_sub_feature)

        update_data = {
            "selectedOption": break_type_alternate_option.id,
        }

        res = client.patch(
            observation_settings.get_observed_sub_feature_detail_url(sub_feature.id),
            data=update_data,
            content_type="application/json",
        )

        assert res.status_code == status.HTTP_200_OK
        sub_feature.refresh_from_db()

        # Check the sub-feature was updated correctly
        assert sub_feature.selected_option.id == break_type_alternate_option.id

    def test_delete_observation(
        self,
        standard_user,
        client,
        observation_with_features,
        observation_settings,
    ):
        """Test deleting an observation"""
        client.force_login(user=standard_user)

        # Get the inspection before deletion
        inspection = observation_with_features.inspection
        observation_count = inspection.observations.count()

        # Delete the observation
        res = client.delete(
            observation_settings.get_observation_url(observation_with_features.id),
        )

        assert res.status_code == status.HTTP_204_NO_CONTENT

        # Check the observation was deleted
        inspection.refresh_from_db()
        assert inspection.observations.count() == observation_count - 1
        with pytest.raises(Observation.DoesNotExist):
            observation_with_features.refresh_from_db()

    def test_delete_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        observation_settings,
    ):
        """Test deleting a feature from an observation"""
        client.force_login(user=standard_user)

        # Get the feature to delete
        feature = observation_with_features.features.get(feature=break_feature)
        feature_count = observation_with_features.features.count()

        # Delete the feature
        res = client.delete(
            observation_settings.get_observed_feature_detail_url(feature.id),
        )

        assert res.status_code == status.HTTP_204_NO_CONTENT

        # Check the feature was deleted
        observation_with_features.refresh_from_db()
        assert observation_with_features.features.count() == feature_count - 1
        assert not observation_with_features.features.filter(id=feature.id).exists()

    def test_delete_sub_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_length_sub_feature,
        observation_settings,
    ):
        """Test deleting a sub-feature from a feature"""
        client.force_login(user=standard_user)

        # Get the feature and its sub-feature to delete
        feature = observation_with_features.features.get(feature=break_feature)
        sub_feature = feature.sub_features.get(sub_feature=break_length_sub_feature)
        sub_feature_count = feature.sub_features.count()

        # Delete the sub-feature
        res = client.delete(
            observation_settings.get_observed_sub_feature_detail_url(sub_feature.id),
        )

        assert res.status_code == status.HTTP_204_NO_CONTENT

        # Check the sub-feature was deleted
        feature.refresh_from_db()
        assert feature.sub_features.count() == sub_feature_count - 1
        assert not feature.sub_features.filter(id=sub_feature.id).exists()

    def test_create_feature_with_wrong_subfeature(
        self,
        standard_user,
        client,
        observation,
        abandonment_feature,
        break_length_sub_feature,  # This sub-feature doesn't belong to 'abandonment' feature
        observation_settings,
    ):
        """Test that you cannot create a feature with a sub-feature that doesn't belong to that feature"""
        client.force_login(user=standard_user)

        new_feature_data = {
            "feature": abandonment_feature.id,
            "clockPositionFrom": 3,
            "clockPositionTo": 9,
            "featureClassPredictionCertainty": 0.85,
            "subFeatures": [
                {
                    "subFeature": break_length_sub_feature.id,  # Breaklength doesn't belong to Abandonment
                    "numericValue": 0.5,
                    "numericUnit": SubFeatureUnit.METRES.value,
                }
            ],
        }

        res = client.post(
            observation_settings.get_observation_features_url(observation.id),
            data=new_feature_data,
            content_type="application/json",
        )

        # Should return a validation error
        assert res.status_code == status.HTTP_400_BAD_REQUEST

        # Verify the feature was not created
        observation.refresh_from_db()
        assert observation.features.count() == 0

    def test_create_feature_with_duplicate_subfeatures(
        self,
        standard_user,
        client,
        observation,
        break_feature,
        break_length_sub_feature,
        observation_settings,
    ):
        """Test that you cannot create a feature with duplicate sub-features"""
        client.force_login(user=standard_user)

        new_feature_data = {
            "feature": break_feature.id,
            "clockPositionFrom": 3,
            "clockPositionTo": 9,
            "featureClassPredictionCertainty": 0.85,
            "subFeatures": [
                # Adding the same sub-feature twice
                {
                    "subFeature": break_length_sub_feature.id,
                    "numericValue": 0.5,
                    "numericUnit": SubFeatureUnit.METRES.value,
                },
                {
                    "subFeature": break_length_sub_feature.id,  # Duplicate sub-feature
                    "numericValue": 0.7,
                    "numericUnit": SubFeatureUnit.METRES.value,
                },
            ],
        }

        res = client.post(
            observation_settings.get_observation_features_url(observation.id),
            data=new_feature_data,
            content_type="application/json",
        )

        # Should return a validation error
        assert res.status_code == status.HTTP_400_BAD_REQUEST

        # Verify the feature was not created
        observation.refresh_from_db()
        assert observation.features.count() == 0

    def test_add_duplicate_subfeature_to_existing_feature(
        self,
        standard_user,
        client,
        observation_with_features,
        break_feature,
        break_length_sub_feature,
        observation_settings,
    ):
        """Test that you cannot add a duplicate sub-feature to an existing feature"""
        client.force_login(user=standard_user)

        feature = observation_with_features.features.get(feature=break_feature)

        # Verify the sub-feature already exists
        assert feature.sub_features.filter(sub_feature=break_length_sub_feature).exists()

        # Try to add the same sub-feature again
        duplicate_sub_feature_data = {
            "subFeature": break_length_sub_feature.id,
            "numericValue": 0.8,
            "numericUnit": SubFeatureUnit.METRES.value,
        }

        res = client.post(
            observation_settings.get_observed_sub_features_url(feature.id),
            data=duplicate_sub_feature_data,
            content_type="application/json",
        )

        # Should return a validation error
        assert res.status_code == status.HTTP_400_BAD_REQUEST

        # Verify no additional sub-feature was created
        feature.refresh_from_db()
        assert feature.sub_features.filter(sub_feature=break_length_sub_feature).count() == 1
