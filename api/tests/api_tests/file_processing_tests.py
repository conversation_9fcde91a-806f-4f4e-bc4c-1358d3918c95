from datetime import datetime, timezone

import pytest
from django.test import Client
from rest_framework import status
from vapar.constants.processing import ProcessingStatusEnum, ProcessingStatusReasonEnum

from api.common.enums import ProcessingRetryableEnum
from api.defects.models import Standard
from api.files.models import FileList, ProcessingList, JobsTree
from api.organisations.models import Organisations


pytestmark = pytest.mark.django_db


@pytest.fixture(autouse=True)
def patch_enqueue_message(monkeypatch):
    def mock_enqueue_message(*args, **kwargs):
        return ""

    monkeypatch.setattr("api.files.views.file_views.enqueue_message", mock_enqueue_message)


def test_processing_records_list(asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org
    client = Client()

    f1 = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f1,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.UPLOADING,
    )

    f2 = FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f2,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.WAITING_TO_PROCESS,
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_processing_records_url)
    assert res.status_code == status.HTTP_200_OK
    res_data = res.json()
    assert len(res_data["results"]) == 2

    assert res_data["results"][0]["status"] == ProcessingStatusEnum.WAITING_TO_PROCESS
    assert res_data["results"][0]["statusReason"] is None
    assert res_data["results"][0]["timesRetried"] == 0
    assert res_data["results"][0]["retryableState"] == ProcessingRetryableEnum.BAD_STATE

    assert res_data["results"][1]["status"] == ProcessingStatusEnum.UPLOADING
    assert res_data["results"][1]["statusReason"] is None
    assert res_data["results"][1]["timesRetried"] == 0
    assert res_data["results"][1]["retryableState"] == ProcessingRetryableEnum.BAD_STATE


def test_processing_records_create(asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org
    client = Client()

    root_folder = JobsTree.objects.filter(depth=1, primary_org=org).first()
    folder = root_folder.add_child(
        primary_org=org,
        created_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
        job_name="test-folder",
        pipe_type_sewer=org.sewer_data,
        standard_key=org.standard_key,
    )

    client.force_login(user=standard_user)
    res = client.post(
        inspection_settings.upload_file_url,
        data={
            "job_id": folder.id,
            "file_name": "test-file-name.mp4",
            "upload_file_name": "2024_1_1_test_file.mp4",
        },
    )
    assert res.status_code == status.HTTP_202_ACCEPTED
    res_data = res.json()
    assert "mock-sas-token" in res_data["blob_url_with_sas"]


def test_retry_uploads_file_not_processed(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    file = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.post(inspection_settings.get_retry_file_processing_url(file.id))
    assert res.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.parametrize(
    ("processing_status", "status_reason"),
    [
        (ProcessingStatusEnum.UPLOADING, None),
        (ProcessingStatusEnum.WAITING_TO_PROCESS, None),
        (ProcessingStatusEnum.ANALYSING_VIDEO, None),
        (ProcessingStatusEnum.STORING_RESULTS, None),
        (ProcessingStatusEnum.UPLOAD_FAILED, None),
        (ProcessingStatusEnum.FAILED_TO_PROCESS, ProcessingStatusReasonEnum.FILE_CORRUPTED),
    ],
)
def test_retry_uploads_file_has_wrong_status(
    asset_owner_org,
    standard_user,
    inspection_settings,
    processing_status: str,
    status_reason: str | None,
):
    client = Client()
    org = asset_owner_org

    file = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    record = ProcessingList.objects.create(
        associated_file=file,
        upload_user=standard_user.full_name,
        target_org=org,
        status=processing_status,
        status_reason=status_reason,
    )

    client.force_login(user=standard_user)
    res = client.post(inspection_settings.get_retry_file_processing_url(file.id))
    assert res.status_code == status.HTTP_400_BAD_REQUEST

    res_data = res.json()
    assert res_data == {
        "status": record.status,
        "statusReason": record.status_reason,
        "retryableState": ProcessingRetryableEnum.BAD_STATE,
        "timesRetried": 0,
    }


def test_retry_uploads_nonexistent_file(asset_owner_org, standard_user, inspection_settings):
    client = Client()

    client.force_login(user=standard_user)
    res = client.post(inspection_settings.get_retry_file_processing_url(999999))
    assert res.status_code == status.HTTP_404_NOT_FOUND


def test_retry_uploads_contractor_with_wrong_org(
    asset_owner_org, linked_contractor_org, linked_contractor_user, inspection_settings
):
    client = Client()
    target_org = asset_owner_org

    another_org = Organisations.objects.create(
        full_name="another_org",
        standard_key=Standard.objects.first(),
    )

    file = FileList.objects.create(
        filename="1",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=file,
        upload_user=linked_contractor_user.full_name,
        target_org=target_org,
        status=ProcessingStatusEnum.FAILED_TO_PROCESS,
        status_reason=ProcessingStatusReasonEnum.GENERIC_ERROR,
    )

    client.force_login(user=linked_contractor_user)
    res = client.post(inspection_settings.get_retry_file_processing_url(file.id) + f"?target_org_id={another_org.id}")
    assert res.status_code == status.HTTP_403_FORBIDDEN


def test_retry_uploads_successful(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    file = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=file,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.FAILED_TO_PROCESS,
        status_reason=ProcessingStatusReasonEnum.GENERIC_ERROR,
    )

    client.force_login(user=standard_user)
    res = client.post(inspection_settings.get_retry_file_processing_url(file.id))
    assert res.status_code == status.HTTP_200_OK

    file.refresh_from_db()
    assert file.processing_record.status == ProcessingStatusEnum.WAITING_TO_PROCESS
    assert file.processing_record.status_reason is None
    assert file.processing_record.times_retried == 1

    assert res.json() == {
        "status": ProcessingStatusEnum.WAITING_TO_PROCESS,
        "statusReason": None,
        "retryableState": ProcessingRetryableEnum.BAD_STATE,
        "timesRetried": 1,
    }


@pytest.mark.parametrize(
    ("processing_status"),
    [ProcessingStatusEnum.PROCESSING, ProcessingStatusEnum.STORING_RESULTS, ProcessingStatusEnum.FAILED_TO_PROCESS],
)
def test_processing_file_delete(client, asset_owner_org, standard_user, inspection_settings, processing_status):
    org = asset_owner_org

    file = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    processing_record = ProcessingList.objects.create(
        associated_file=file,
        upload_user=standard_user.full_name,
        target_org=org,
        status=processing_status,
    )

    client.force_login(user=standard_user)
    res = client.delete(inspection_settings.get_file_processing_url(processing_record.id))
    assert res.status_code == status.HTTP_200_OK

    try:
        processing_record.refresh_from_db()
    except ProcessingList.DoesNotExist:
        pass
    else:
        assert False, "ProcessingList record not deleted."

    try:
        file.refresh_from_db()
    except FileList.DoesNotExist:
        assert processing_status not in (ProcessingStatusEnum.PROCESSING, ProcessingStatusEnum.STORING_RESULTS)
    else:
        assert file.hidden


def test_processing_file_delete_completed(client, asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org

    file = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    processing_record = ProcessingList.objects.create(
        associated_file=file,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.STORING_RESULTS,
    )

    client.force_login(user=standard_user)
    res = client.delete(inspection_settings.get_file_processing_url(processing_record.id))
    assert res.status_code == status.HTTP_200_OK
    file.refresh_from_db()
    assert not file.hidden
