import os

import pytest
from django.core.files import File
from django.test.client import Client
from django.utils import timezone
from rest_framework import status

from api.tests import factory
from api.defects.models import Standard, StandardHeader
from api.exports.models import Export
from api.files.models import FileList, JobsTree
from api.inspections.models import Asset, Inspection
from api.tests.settings import ExternalSettings
from api.users.models import CustomUser
from api.inspections.pydantic_models.inspection_model import Inspection as InspectionModel
from vapar.constants.pipes import StandardEnum

pytestmark = [pytest.mark.django_db(databases=["default"])]


class ExternalTests:
    """
    Tests for the External API
    """

    client: Client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None
    root_folder: JobsTree = None
    inspections: list[InspectionModel] = []

    @pytest.fixture(autouse=True)
    def setup_method(self, product_owner, standard_user, asset_owner_org, root_folder_for_standard_user):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.root_folder = root_folder_for_standard_user
        self.client.force_login(user=self.standard_user)

        # Load an inspection for testing - ensure file and asset org match
        self.inspection = Inspection.objects.get(uuid="23896fe4-4baa-11ee-be56-0242ac120002")
        self.inspection.asset.organisation = self.standard_user.organisation
        self.inspection.asset.save()
        self.inspection.file.target_org = self.standard_user.organisation
        self.inspection.file.save()
        self.inspection.folder = self.inspection.file.job_tree
        self.inspection.folder.primary_org = self.standard_user.organisation
        self.inspection.folder.save()

    @pytest.fixture
    def expected_frame_keys(self):
        return [
            "id",
            "inspectionId",
            "imageLocation",
            "imageUrl",
            "frameId",
            "classLabel",
            "classCertainty",
            "chainage",
            "chainageNumber",
            "isHidden",
            "isAccepted",
            "allClassBreakdown",
            "atJoint",
            "atClock",
            "toClock",
            "contDefectStart",
            "contDefectEnd",
            "quantity1Value",
            "quantity1Units",
            "quantity2Value",
            "quantity2Units",
            "remarks",
            "isMatched",
            "parentVideo",
            "pipeTypeSewer",
            "material",
            "defectId",
            "defectClass",
            "defectCode",
            "defectStrScore",
            "defectSerScore",
            "defectScoreSeverity",
            "defectScoreIsShown",
            "timeReference",
        ]

    def test_update_inspection_with_xml(self, external_settings: ExternalSettings):
        """
        Test that a standard user can update an inspection with an xml file.
        """
        script_dir = os.path.dirname(__file__)
        xml_file_path = os.path.join(script_dir, "../resources", "propipe.xml")
        #
        # file = {"file": ("test_logo.png", open(image_file_path, "rb"), "image/png")}
        uploaded_file = File(open(file=xml_file_path, mode="rb"), name="propipe.xml")
        # file = ("propipe.xml", open(xml_file_path, "rb"), "text/xml")
        # file = (io.BytesIO(b"abcdef"), 'propipe.xml')
        # print('uploaded_file', file)
        response = self.client.post(
            path=external_settings.external_submit_xml_job_url,
            data={
                "file": uploaded_file,
                "file_id": self.inspection.file.id,
            },
            content_type="multipart/form-data",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST


class ExternalInspectionListTests:
    def test_get_external_inspections_as_standard_user(
        self, client, external_settings, standard_user, root_folder_for_standard_user
    ):
        """
        Test external user can get a list of inspections.
        """
        client.force_login(user=standard_user)
        folder = root_folder_for_standard_user
        factory.create_bulk_inspections(
            target_org=standard_user.organisation,
            folder=folder,
            n=10,
        )

        response = client.get(
            path=external_settings.external_inspection_list_url.format(organisation_id=standard_user.organisation_id)
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # check standard is a standard enum
        for inspection in response_data:
            assert inspection["Standard"] in [standard.value for standard in StandardEnum]

    def test_get_external_inspections_by_upload_date(
        self, client, external_settings, standard_user, root_folder_for_standard_user
    ):
        """
        Test external user can get a list of inspections.
        """
        client.force_login(user=standard_user)
        folder = root_folder_for_standard_user
        standard = Standard.objects.get(name="WSA-05 2020")

        sh_upstream_node = StandardHeader.objects.get(
            header__name="UpstreamNode", standard=standard, header__type="asset"
        )
        sh_downstream_node = StandardHeader.objects.get(
            header__name="DownstreamNode", standard=standard, header__type="asset"
        )
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")
        sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
        sh_chainage = StandardHeader.objects.get(
            header__name="LengthSurveyed", standard=standard, header__type="inspection"
        )
        sh_direction = StandardHeader.objects.get(
            header__name="Direction", standard=standard, header__type="inspection"
        )

        a1 = Asset.objects.create(organisation=standard_user.organisation)
        a1.assetvalue_set.create(
            standard_header=sh_upstream_node,
            value="1",
            original_value="1",
        )
        a1.assetvalue_set.create(
            standard_header=sh_downstream_node,
            value="2",
            original_value="2",
        )
        a1.assetvalue_set.create(
            standard_header=sh_asset_id,
            value="a1",
            original_value="a1",
        )
        a1.save()

        file_1 = FileList.objects.create(
            filename="test_file1.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder,
            uploaded_by=standard_user,
        )
        i1 = Inspection.objects.create(
            asset=a1, legacy_id=1500, folder=folder, file=file_1, structural_grade=4, service_grade=3
        )
        i1.inspectionvalue_set.create(standard_header=sh_date_captured, value="2024-01-01")
        i1.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i1.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        file_2 = FileList.objects.create(
            filename="test_file2.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder,
            uploaded_by=standard_user,
        )
        i2 = Inspection.objects.create(
            asset=a1, legacy_id=1501, folder=folder, file=file_2, structural_grade=4, service_grade=3
        )
        i2.inspectionvalue_set.create(standard_header=sh_date_captured, value="2023-01-01")
        i2.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i2.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        response = client.get(path=external_settings.external_inspection_list_url + "?date_captured=2024-01-01")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # check standard is a standard enum
        assert len(response_data) == 1

    def test_get_external_inspections_by_folder_details(
        self, client, external_settings, standard_user, root_folder_for_standard_user
    ):
        """
        Test external user can get a list of inspections.
        """
        client.force_login(user=standard_user)
        org = standard_user.organisation
        now = timezone.now()
        root_folder = root_folder_for_standard_user
        folder_1 = root_folder.add_child(
            job_name="F1",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )
        folder_2 = root_folder.add_child(
            job_name="F2",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )

        standard = Standard.objects.get(name="WSA-05 2020")

        sh_upstream_node = StandardHeader.objects.get(
            header__name="UpstreamNode", standard=standard, header__type="asset"
        )
        sh_downstream_node = StandardHeader.objects.get(
            header__name="DownstreamNode", standard=standard, header__type="asset"
        )
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")
        sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
        sh_chainage = StandardHeader.objects.get(
            header__name="LengthSurveyed", standard=standard, header__type="inspection"
        )
        sh_direction = StandardHeader.objects.get(
            header__name="Direction", standard=standard, header__type="inspection"
        )

        a1 = Asset.objects.create(organisation=standard_user.organisation)
        a1.assetvalue_set.create(
            standard_header=sh_upstream_node,
            value="1",
            original_value="1",
        )
        a1.assetvalue_set.create(
            standard_header=sh_downstream_node,
            value="2",
            original_value="2",
        )
        a1.assetvalue_set.create(
            standard_header=sh_asset_id,
            value="a1",
            original_value="a1",
        )
        a1.save()

        file_1 = FileList.objects.create(
            filename="test_file1.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder_1,
            uploaded_by=standard_user,
        )
        i1 = Inspection.objects.create(
            asset=a1, legacy_id=1500, folder=folder_1, file=file_1, structural_grade=4, service_grade=3
        )
        i1.inspectionvalue_set.create(standard_header=sh_date_captured, value="2024-01-01")
        i1.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i1.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        file_2 = FileList.objects.create(
            filename="test_file2.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder_2,
            uploaded_by=standard_user,
        )
        i2 = Inspection.objects.create(
            asset=a1, legacy_id=1501, folder=folder_2, file=file_2, structural_grade=4, service_grade=3
        )
        i2.inspectionvalue_set.create(standard_header=sh_date_captured, value="2023-01-01")
        i2.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i2.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        response = client.get(path=external_settings.external_inspection_list_url + f"?folder_id={folder_2.id}")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # check standard is a standard enum
        assert len(response_data) == 1
        assert response_data[0]["uuid"] == str(i2.uuid)

    def test_get_external_inspections_by_asset(
        self, client, external_settings, standard_user, root_folder_for_standard_user
    ):
        client.force_login(user=standard_user)
        org = standard_user.organisation
        now = timezone.now()
        root_folder = root_folder_for_standard_user
        folder_1 = root_folder.add_child(
            job_name="F1",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )
        folder_2 = root_folder.add_child(
            job_name="F2",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )

        standard = Standard.objects.get(name="WSA-05 2020")

        sh_upstream_node = StandardHeader.objects.get(
            header__name="UpstreamNode", standard=standard, header__type="asset"
        )
        sh_downstream_node = StandardHeader.objects.get(
            header__name="DownstreamNode", standard=standard, header__type="asset"
        )
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")
        sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
        sh_chainage = StandardHeader.objects.get(
            header__name="LengthSurveyed", standard=standard, header__type="inspection"
        )
        sh_direction = StandardHeader.objects.get(
            header__name="Direction", standard=standard, header__type="inspection"
        )

        a1 = Asset.objects.create(organisation=standard_user.organisation)
        a1.assetvalue_set.create(
            standard_header=sh_upstream_node,
            value="1",
            original_value="1",
        )
        a1.assetvalue_set.create(
            standard_header=sh_downstream_node,
            value="2",
            original_value="2",
        )
        a1.assetvalue_set.create(
            standard_header=sh_asset_id,
            value="a1",
            original_value="a1",
        )
        a1.save()

        a2 = Asset.objects.create(organisation=standard_user.organisation)
        a2.assetvalue_set.create(
            standard_header=sh_upstream_node,
            value="3",
            original_value="3",
        )
        a2.assetvalue_set.create(
            standard_header=sh_downstream_node,
            value="4",
            original_value="4",
        )
        a2.assetvalue_set.create(
            standard_header=sh_asset_id,
            value="a2",
            original_value="a2",
        )
        a2.save()

        file_1 = FileList.objects.create(
            filename="test_file1.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder_1,
            uploaded_by=standard_user,
        )
        i1 = Inspection.objects.create(
            asset=a1, legacy_id=1500, folder=folder_1, file=file_1, structural_grade=4, service_grade=3
        )
        i1.inspectionvalue_set.create(standard_header=sh_date_captured, value="2024-01-01")
        i1.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i1.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        file_2 = FileList.objects.create(
            filename="test_file2.jpg",
            target_org=standard_user.organisation,
            upload_org=standard_user.organisation,
            job_tree=folder_2,
            uploaded_by=standard_user,
        )
        i2 = Inspection.objects.create(
            asset=a2, legacy_id=1501, folder=folder_2, file=file_2, structural_grade=4, service_grade=3
        )
        i2.inspectionvalue_set.create(standard_header=sh_date_captured, value="2023-01-01")
        i2.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i2.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        response = client.get(path=external_settings.external_inspection_list_url + "?asset_id=a2")

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # check standard is a standard enum
        assert len(response_data) == 1
        assert response_data[0]["asset"]["uuid"] == str(a2.uuid)


class TestExternalExports:
    @pytest.fixture(autouse=True)
    def patch_enqueue_message(self, monkeypatch):
        def mock_enqueue_message(payload, org):
            pass

        monkeypatch.setattr("api.exports.views.enqueue_export_message", mock_enqueue_message)
        monkeypatch.setattr("api.exports.sync.enqueue_export_message", mock_enqueue_message)

    @pytest.fixture(autouse=True)
    def patch_get_blob_url_with_sas_token(self, monkeypatch):
        def mock_get_blob_url(blob_path, *args, **kwargs):
            return f"https://mock-blob-url.com/{blob_path}"

        monkeypatch.setattr("api.external.views.get_platform_blob_url_with_sas", mock_get_blob_url)

    @pytest.fixture(autouse=True)
    def patch_get_storage_sas_token(self, monkeypatch):
        def mock_get_storage_sas_token(*args, **kwargs):
            return "mock-sas-token"

        monkeypatch.setattr("api.exports.serializers.get_platform_blob_url_with_sas", mock_get_storage_sas_token)

    @pytest.fixture
    def single_inspection(self, asset_owner_org):
        org = asset_owner_org
        insp_id = factory.create_bulk_inspections(target_org=org)[0]["inspection_id"]
        return Inspection.objects.get(uuid=insp_id)

    def test_full_flow(self, single_inspection, client, external_settings, standard_user):
        client.force_login(user=standard_user)

        # Create the export request
        resp = client.post(
            external_settings.external_exports_create_url,
            data={
                "payload": {
                    "inspectionIds": [str(single_inspection.uuid)],
                    "type": "BI",
                    "format": "PDF",
                    "runValidation": False,
                }
            },
            content_type="application/json",
        )
        assert resp.status_code == status.HTTP_201_CREATED

        export_id = resp.json()["id"]
        exp = Export.objects.get(id=export_id)
        assert exp.status == "PE"
        assert exp.type == "BI"
        assert exp.format == "PDF"
        assert exp.created_by == standard_user
        assert exp.is_initiated_by_user is True

        # Get the export
        resp = client.get(external_settings.get_external_exports_get_url(export_id))
        assert resp.status_code == status.HTTP_200_OK
        data = resp.json()
        assert data["id"] == export_id
        assert data["status"] == "PE"
        assert data["type"] == "BI"
        assert data["format"] == "PDF"

        # Mock the export completion
        exp.status = "CO"
        exp.save()
        exp.outputs.create(
            filename="test.pdf",
            blob_url="mock_container_name/test.pdf",
            extension=".pdf",
            mime_type="application/pdf",
            file_size=1234,
        )

        # Get the export outputs
        resp = client.get(external_settings.get_external_exports_outputs_list_url(export_id))
        assert resp.status_code == status.HTTP_200_OK

        data = resp.json()
        assert len(data["results"]) == 1

        output = data["results"][0]
        assert output["filename"] == "test.pdf"
        assert output["blobUrl"] == "mock_container_name/test.pdf"
        assert output["extension"] == ".pdf"
        assert output["mimeType"] == "application/pdf"
        assert output["fileSize"] == 1234
        assert "sasUrl" in output and "mock-sas-token" in output["sasUrl"]
