import json
from datetime import datetime

import pytest

from api.common.enums import StatusEnum
from api.defects.models import Standard, DefectModelList, DefectScores
from api.files.models import VideoFrames
from api.inspections import analytics
from api.organisations.models import Organisations

pytestmark = pytest.mark.django_db

_EXPECTED_FRAME_STATE = {
    "is_hidden": False,
    "chainage_number": 15.0,
    "at_clock": 1,
    "to_clock": 2,
    "quantity_1_value": 1.0,
    "quantity_2_value": None,
    "quantity_1_units": "m",
    "quantity_2_units": None,
    "is_continuous_defect": False,
    "continuous_defect_end_chainage": None,
    "at_joint": False,
}


@pytest.fixture
def other_org():
    return Organisations.objects.create(
        org_type="Asset_Owner",
        full_name="Other Org",
        country="AU",
        standard_key=Standard.objects.first(),
    )


@pytest.fixture
def video_frame(file_obj):
    return VideoFrames.objects.create(
        parent_video=file_obj,
        at_clock=1,
        to_clock=2,
        frame_id=123,
        quantity1_value=1.0,
        quantity1_units="m",
        quantity2_value=None,
        quantity2_units=None,
        is_hidden=False,
        chainage_number=15,
        chainage="15",
        class_label="",
        class_certainty=0.0,
        at_joint=False,
    )


def test_defect_updated_from_none_to_none(event_client, standard_user, other_org, video_frame):
    analytics.send_frame_defect_updated_event(
        frame=video_frame,
        user=standard_user,
        owner_org=other_org,
        updater_org=standard_user.organisation,
        updated_at=datetime(2024, 1, 1),
        previous_defect=None,
        new_defect=None,
        inspection_status=StatusEnum.REVIEWED,
        client=event_client,
    )

    assert len(event_client.events) == 1
    event = json.loads(event_client.events[0])

    assert event == {
        "frame_id": 123,
        "video_id": video_frame.parent_video.id,
        "user_id": standard_user.id,
        "updated_at": "2024-01-01T00:00:00",
        "previous_defect": None,
        "new_defect": None,
        "inspection_status": "Reviewed",
        "frame_state": _EXPECTED_FRAME_STATE,
        "frames_page_view_mode": None,
        "owner_org": {
            "org_id": other_org.id,
            "org_type": "Asset_Owner",
            "full_name": "Other Org",
            "country_code": "AU",
        },
        "updater_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
    }


def test_defect_updated_with_no_defect_model(event_client, standard_user, other_org, video_frame):
    new_defect = DefectScores(
        standard_key=Standard(name="Standard 1", display_name="Standard 1"),
        defect_description="Defect 1",
    )

    analytics.send_frame_defect_updated_event(
        frame=video_frame,
        user=standard_user,
        owner_org=other_org,
        updater_org=standard_user.organisation,
        updated_at=datetime(2024, 1, 1),
        previous_defect=None,
        new_defect=new_defect,
        inspection_status=StatusEnum.COMPLETE,
        client=event_client,
        frames_page_view_mode=analytics.FramesPageViewModeEnum.ALL,
    )

    assert len(event_client.events) == 1
    event = json.loads(event_client.events[0])

    assert event == {
        "frame_id": 123,
        "video_id": video_frame.parent_video.id,
        "user_id": standard_user.id,
        "updated_at": "2024-01-01T00:00:00",
        "previous_defect": None,
        "frame_state": _EXPECTED_FRAME_STATE,
        "frames_page_view_mode": "all",
        "new_defect": {
            "defect_model_name": None,
            "defect_description": "Defect 1",
            "standard_name": "Standard 1",
        },
        "inspection_status": "Complete",
        "owner_org": {
            "org_id": other_org.id,
            "org_type": "Asset_Owner",
            "full_name": "Other Org",
            "country_code": "AU",
        },
        "updater_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country,
        },
    }


def test_defect_updated_with_all_info(event_client, standard_user, other_org, video_frame):
    prev_defect = DefectScores(
        defect_key=DefectModelList(name="Defect model 1"),
        standard_key=Standard(name="Standard 1", display_name="Standard 1"),
        defect_description="Defect 1",
    )
    new_defect = DefectScores(
        defect_key=DefectModelList(name="Defect model 2"),
        standard_key=Standard(name="Standard 2", display_name="Standard 2"),
        defect_description="Defect 2",
    )

    analytics.send_frame_defect_updated_event(
        frame=video_frame,
        user=standard_user,
        owner_org=other_org,
        updater_org=standard_user.organisation,
        updated_at=datetime(2024, 1, 1),
        previous_defect=prev_defect,
        new_defect=new_defect,
        inspection_status=StatusEnum.REVIEWED,
        frames_page_view_mode=analytics.FramesPageViewModeEnum.REPORTED,
        client=event_client,
    )

    assert len(event_client.events) == 1
    event = json.loads(event_client.events[0])
    assert event == {
        "frame_id": 123,
        "video_id": video_frame.parent_video.id,
        "user_id": standard_user.id,
        "updated_at": "2024-01-01T00:00:00",
        "frame_state": _EXPECTED_FRAME_STATE,
        "frames_page_view_mode": "reported",
        "previous_defect": {
            "defect_model_name": "Defect model 1",
            "defect_description": "Defect 1",
            "standard_name": "Standard 1",
        },
        "new_defect": {
            "defect_model_name": "Defect model 2",
            "defect_description": "Defect 2",
            "standard_name": "Standard 2",
        },
        "inspection_status": "Reviewed",
        "owner_org": {
            "org_id": other_org.id,
            "org_type": "Asset_Owner",
            "full_name": "Other Org",
            "country_code": "AU",
        },
        "updater_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country,
        },
    }
