"""
Pytest fixtures accessible to any tests under this directory
"""

from pathlib import Path
from urllib.parse import urljoin

import pytest

from django.core.management import call_command
from django.test.client import Client
from typing import List

from django.utils import timezone

from api.tests.settings import (
    AuthSettings,
    CoreSettings,
    HeaderSettings,
    InspectionSettings,
    OrganisationSettings,
    UserSettings,
    AssetSettings,
    RepairRecommendationSettings,
    DefectSettings,
    GenericImportSettings,
    ExportSettings,
    ExternalSettings,
    FootageSettings,
    GenericExportSettings,
)
from api.users.models import CustomUser
from api.organisations.models import Organisations, AssetOwners, Contractors
from api.defects.models import Standard
from api.files.models import JobsTree, FileList
from api.inspections.models import Asset, Inspection, InspectionFilter
from api.recommendations.models import RepairRecommendation
from api.tests.settings import ObservationSettings
from api.tests import factory

pytestmark = [pytest.mark.django_db(databases=["default"])]

TESTS_MODULE = Path(__file__).parent
API_MODULE = TESTS_MODULE.parent


@pytest.fixture(scope="session")
def django_db_setup(django_db_setup, django_db_blocker):
    with django_db_blocker.unblock():
        call_command(
            "loaddata",
            str(API_MODULE / "base" / "fixtures" / "headers.json"),
            str(API_MODULE / "defects" / "fixtures" / "defect_modellist.json"),
            str(API_MODULE / "defects" / "fixtures" / "defect_scores.json"),
            str(API_MODULE / "defects" / "fixtures" / "standard_headers.json"),
            str(API_MODULE / "defects" / "fixtures" / "standard_subcategories.json"),
            str(API_MODULE / "recommendations" / "fixtures" / "risk_consequence.json"),
            str(API_MODULE / "recommendations" / "fixtures" / "risk_likelihood.json"),
            str(API_MODULE / "recommendations" / "fixtures" / "repairrecommendations.json"),
            str(API_MODULE / "organisations" / "fixtures" / "organisations.json"),
            str(API_MODULE / "users" / "fixtures" / "customusers.json"),
            str(API_MODULE / "inspections" / "fixtures" / "jobs.json"),
            str(API_MODULE / "inspections" / "fixtures" / "filelist.json"),
            str(API_MODULE / "inspections" / "fixtures" / "jobstree.json"),
            str(API_MODULE / "inspections" / "fixtures" / "assets.json"),
            str(API_MODULE / "inspections" / "fixtures" / "inspections.json"),
            str(API_MODULE / "inspections" / "fixtures" / "mappointlists.json"),
            str(API_MODULE / "inspections" / "fixtures" / "frames.json"),
        )

        call_command("create_new_standard_pacp8")
        call_command("create_new_standard_japan")
        call_command("set_codetypes")
        call_command("fix_standardsubfeature_units")


@pytest.fixture(scope="session")
def core_settings() -> CoreSettings:
    """
    Fixture to return the core settings for the pytest suite.

    :return: CoreSettings object
    """
    return CoreSettings()


@pytest.fixture(scope="session")
def auth_settings() -> AuthSettings:
    """
    Fixture to return the authentication settings for the pytest suite.

    :return: AuthSettings object
    """
    return AuthSettings()


@pytest.fixture(scope="session")
def organisation_settings() -> OrganisationSettings:
    """
    Fixture to return the organisation settings for the pytest suite.

    :return: OrganisationSettings object
    """
    return OrganisationSettings()


@pytest.fixture(scope="session")
def inspection_settings() -> InspectionSettings:
    """
    Fixture to return the inspection settings for the pytest suite.

    :return: InspectionSettings object
    """
    return InspectionSettings()


@pytest.fixture(scope="session")
def observation_settings() -> ObservationSettings:
    """
    Fixture to return the observation settings for the pytest suite.

    :return: ObservationSettings object
    """
    return ObservationSettings()


@pytest.fixture(scope="session")
def footage_settings() -> FootageSettings:
    """
    Fixture to return the footage settings for the pytest suite.

    :return: FootageSettings object
    """
    return FootageSettings()


@pytest.fixture(scope="session")
def repairrecommendation_settings() -> RepairRecommendationSettings:
    """
    Fixture to return the repair recommendation settings for the pytest suite.

    :return: RepairRecommendationSettings object
    """
    return RepairRecommendationSettings()


@pytest.fixture(scope="session")
def user_settings() -> UserSettings:
    """
    Fixture to return the user settings for the pytest suite.

    :return: UserSettings object
    """
    return UserSettings()


@pytest.fixture(scope="session")
def header_settings() -> HeaderSettings:
    """
    Fixture to return the header settings for the pytest suite.

    :return: HeaderSettings object
    """
    return HeaderSettings()


@pytest.fixture(scope="session")
def defect_settings() -> DefectSettings:
    """
    Fixture to return the defect settings for the pytest suite.

    :return: HeaderSettings object
    """
    return DefectSettings()


@pytest.fixture(scope="session")
def asset_settings() -> AssetSettings:
    """
    Fixture to return the asset settings for the pytest suite

    :return: AssetSettings
    """

    return AssetSettings()


@pytest.fixture(scope="session")
def export_settings() -> ExportSettings:
    """
    Fixture to return the export settings for the pytest suite

    :return: ExportSettings
    """

    return ExportSettings()


@pytest.fixture(scope="session")
def import_settings() -> GenericImportSettings:
    """
    Fixture to return the import settings for the pytest suite

    :return: GenericImportSettings
    """

    return GenericImportSettings()


@pytest.fixture(scope="session")
def external_settings() -> ExternalSettings:
    """
    Fixture to return the external settings for the pytest suite

    :return: ExternalSettings
    """

    return ExternalSettings()


@pytest.fixture(scope="function")
def generic_export_settings():
    """
    Fixture to return the generic export settings for the pytest suite.
    """
    return GenericExportSettings()


@pytest.fixture
def all_standards() -> List[Standard]:
    return list(Standard.objects.all())


@pytest.fixture
def asset_owner_org(all_standards) -> Organisations:
    standard = all_standards[1]
    data = {
        "org_type": "Asset_Owner",
        "full_name": "Test Asset Owner Organisation",
        "email_domain": "vapar.co",
        "sewer_data": True,
        "standard_key": standard,
    }

    organisation = Organisations.objects.create(**data)
    factory.create_initial_folders(organisation)
    AssetOwners.objects.create(org=organisation)
    return organisation


@pytest.fixture
def contractor_org(all_standards) -> Organisations:
    """
    A contractor organisation (which is not yet linked to an asset owner organisation).
    """
    standard = all_standards[1]
    data = {
        "org_type": "Contractor",
        "full_name": "Test Contractor Organisation",
        "email_domain": "vapar.co",
        "sewer_data": True,
        "standard_key": standard,
    }

    organisation = Organisations.objects.create(**data)
    factory.create_initial_folders(organisation)
    Contractors.objects.create(org=organisation)
    return organisation


@pytest.fixture
def linked_contractor_org(contractor_org, asset_owner_org) -> Organisations:
    """
    A contractor organisation that has been linked to an asset owner organisation.
    """
    asset_owner_org.assetowners.contractor.add(contractor_org.contractors)
    asset_owner_org.save()

    return contractor_org


@pytest.fixture
def linked_contractor_user(linked_contractor_org) -> CustomUser:
    """
    A standard user that belongs to a contractor organisation which has been linked to an asset owner organisation.
    """
    data = {
        "email": "<EMAIL>",
        "first_name": "contractor",
        "last_name": "user",
        "group": 2,
        "is_active": True,
        "is_staff": False,
        "user_level": "standard",
        "organisation": linked_contractor_org,
    }
    user = CustomUser.objects.create(**data)
    user.set_password("Test123!@#")
    user.save()

    return user


@pytest.fixture
def product_owner(asset_owner_org) -> CustomUser:
    """
    A 'product owner' i.e. staff user
    """

    data = {
        "email": "<EMAIL>",
        "first_name": "product",
        "last_name": "owner",
        "group": 2,
        "is_active": True,
        "is_staff": True,
        "user_level": "standard",
    }
    user = CustomUser.objects.create(**data)
    user.set_password("Test123!@#")
    user.save()

    return user


@pytest.fixture
def standard_user(asset_owner_org) -> CustomUser:
    """
    A standard (as opposed to upload-only) user that belongs to an asset owner organisation.
    """

    data = {
        "email": "<EMAIL>",
        "first_name": "standard",
        "last_name": "user",
        "group": 2,
        "is_active": True,
        "is_staff": False,
        "user_level": "standard",
        "organisation": asset_owner_org,
    }

    user = CustomUser.objects.create(**data)
    user.set_password("Test123!@#")
    user.save()

    # delete filter record - to be removed when we have a better way to skip filtering on inspection list.
    InspectionFilter.objects.filter(user=user, organisation=asset_owner_org.id).delete()

    return user


@pytest.fixture
def root_folder_for_standard_user(standard_user) -> JobsTree:
    return JobsTree.objects.get(primary_org=standard_user.organisation, depth=1)


@pytest.fixture
def linked_contractor_folder(linked_contractor_org, asset_owner_org) -> JobsTree:
    """
    A contractor upload folder within an asset owner organisation.
    """

    root = JobsTree.objects.filter(primary_org=asset_owner_org).first()
    contractor_folder_name = linked_contractor_org.full_name

    if root.get_children().filter(job_name=contractor_folder_name).exists():
        contractor_folder_name = contractor_folder_name + "_"

    sub_root = root.add_child(
        job_name=contractor_folder_name,
        created_date=timezone.now(),
        pipe_type_sewer=asset_owner_org.sewer_data,
        standard_key=root.standard_key,
        secondary_org=linked_contractor_org,
    )

    linked_folder = sub_root.add_child(
        job_name="Job Folder",
        created_date=timezone.now(),
        pipe_type_sewer=asset_owner_org.sewer_data,
        standard_key=root.standard_key,
    )

    return linked_folder


@pytest.fixture
def asset(asset_owner_org) -> Asset:
    return Asset.objects.create(organisation=asset_owner_org)


@pytest.fixture
def file_obj(asset_owner_org, linked_contractor_folder, linked_contractor_user, linked_contractor_org) -> FileList:
    return FileList.objects.create(
        filename="test_file",
        target_org=asset_owner_org,
        job_tree=linked_contractor_folder,
        uploaded_by=linked_contractor_user,
        upload_org=linked_contractor_org,
    )


@pytest.fixture
def folder(asset_owner_org) -> JobsTree:
    return factory.create_folder(org=asset_owner_org)


@pytest.fixture
def linked_contractor_inspection(
    linked_contractor_user, asset, file_obj, linked_contractor_folder, inspection_settings
) -> dict:
    client = Client()
    client.force_login(user=linked_contractor_user)

    folder = linked_contractor_folder

    inspections = factory.create_bulk_inspections(asset=asset, folder=folder, n=1)
    return inspections[0]


@pytest.fixture(scope="function")
def service_user() -> CustomUser:
    return CustomUser.objects.create_service_user(name="test-service-user", email="<EMAIL>")


@pytest.fixture(scope="function")
def service_user_key(service_user: CustomUser) -> str:
    key = service_user.create_service_api_key()
    return key


@pytest.fixture(autouse=True)
def patch_get_storage_sas_token(monkeypatch):
    def mock_get_storage_sas_token(*args, **kwargs):
        return "mock-sas-token"

    def mock_get_storage_url(*args, **kwargs):
        return "https://mock-url.com"

    def mock_get_platform_blob_url_with_sas(*args, **kwargs):
        url = urljoin(mock_get_storage_url(), kwargs["blob_path"])
        sas_token = mock_get_storage_sas_token()
        return f"{url}?{sas_token}"

    monkeypatch.setattr("api.inspections.views.frame_views.get_platform_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr("api.inspections.views.file_views.get_platform_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr("api.inspections.views.file_views.get_processing_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr(
        "api.inspections.serializers.inspection_serializers.get_platform_storage_sas_token", mock_get_storage_sas_token
    )
    monkeypatch.setattr(
        "api.inspections.serializers.inspection_serializers.get_platform_blob_url_with_sas",
        mock_get_platform_blob_url_with_sas,
    )
    monkeypatch.setattr("api.common.storage.get_platform_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr("api.inspections.views.file_views.get_processing_storage_region_base_url", mock_get_storage_url)


@pytest.fixture
def single_inspection(asset_owner_org) -> Inspection:
    insp_id = factory.create_bulk_inspections(target_org=asset_owner_org)[0]["inspection_id"]
    return Inspection.objects.get(uuid=insp_id)


@pytest.fixture
def repair_recommendation(asset_owner_org, standard_user, single_inspection) -> RepairRecommendation:
    return RepairRecommendation.objects.create(
        target=single_inspection.mappointlist,
        inspection_frequency="Monthly",
        action_summary="A",
        c_action_summary="B",
        o_action_summary="C",
    )
