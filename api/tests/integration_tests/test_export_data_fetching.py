from uuid import UUID

import pytest
from rest_framework import status

from api.inspections.models import Inspection, Observation
from api.tests import factory

pytestmark = pytest.mark.django_db


@pytest.fixture
def many_inspections(asset_owner_org) -> list[Inspection]:
    inspections = factory.create_inspections(
        org=asset_owner_org,
        n=10,
        n_observations_each=10,
        n_keyframes_per_observation=1,
        create_vid_frames_for_keyframes=True,
    )
    return inspections


@pytest.fixture
def single_inspection(asset_owner_org) -> Inspection:
    insp = factory.create_inspections(
        org=asset_owner_org,
        n=1,
        n_observations_each=3,
        n_keyframes_per_observation=1,
        create_vid_frames_for_keyframes=True,
    )[0]
    # Set some flags on one of the keyframes
    kf = insp.observations.first().keyframes.first()
    kf.at_joint = True
    kf.has_loss_of_vision = True
    kf.save()

    return insp


def test_bulk_fetch_observations(client, observation_settings, service_user_key, asset_owner_org, many_inspections):
    """
    Test fetching observations across multiple inspections in bulk - scaled down in size so the data creation isn't
    very slow.
    """

    inspections_ids_to_fetch = [inspection.uuid for inspection in many_inspections][:5]  # Fetch first 5 inspections
    ids_param = ",".join(str(id) for id in inspections_ids_to_fetch)
    res_page_1 = client.get(
        observation_settings.observations_global_list_url + f"?inspection__in={ids_param}&page_size=30",
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
    )
    assert res_page_1.status_code == status.HTTP_200_OK

    data_page_1 = res_page_1.json()
    assert data_page_1["count"] == 50, "5 inspections * 10 observations each"
    assert len(data_page_1["results"]) == 30, "The first page should contain 30 observations, the provided page size"
    assert data_page_1["next"] is not None, "There should be a next page"

    res_page_2 = client.get(
        observation_settings.observations_global_list_url + f"?inspection__in={ids_param}&page_size=30&page=2",
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
    )
    assert res_page_2.status_code == status.HTTP_200_OK
    data_page_2 = res_page_2.json()

    assert data_page_2["count"] == 50
    assert len(data_page_2["results"]) == 20, "The remaining observations should be in the second page"
    assert data_page_2["next"] is None, "The 50 records should have fit into 2 pages"

    observations = data_page_1["results"] + data_page_2["results"]

    expected_inspection_ids = set(inspections_ids_to_fetch)
    res_inspection_ids = set(UUID(observation["inspectionId"]) for observation in observations)
    assert res_inspection_ids == expected_inspection_ids, "Mismatch in fetched inspection IDs"

    expected_obs_ids = set(obs.id for obs in Observation.objects.filter(inspection__in=inspections_ids_to_fetch))
    res_obs_ids = set(UUID(observation["id"]) for observation in observations)
    assert res_obs_ids == expected_obs_ids, "Mismatch in IDs of fetched observations"

    assert observations[0]["aggregatedFlags"] is None, "Should only be present if 'with_aggregated_flags' param is set"
    assert observations[0]["keyframeDetails"] is None, "Should only be present if 'with_keyframe_details' param is set"
    assert (
        observations[0]["features"][0]["standardisedVals"] is None
    ), "Should only be present if 'with_standardised_vals' param is set"


def test_fetch_observations_with_all_details_included(
    client, observation_settings, service_user_key, asset_owner_org, single_inspection
):
    """
    Test that enabling query param flags includes the expected details in the response.
    """

    params = (
        f"?inspection__in={single_inspection.uuid}"
        "&with_aggregated_flags=true"
        "&with_keyframe_details=true"
        "&with_standardised_vals=true"
        "&with_video_frame_details=true"
    )
    res = client.get(
        observation_settings.observations_global_list_url + params,
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
    )
    assert res.status_code == status.HTTP_200_OK
    data = res.json()
    assert data["count"] == 3  # 3 observations for the single inspection
    assert len(data["results"]) == 3

    assert all(
        obs["aggregatedFlags"] is not None for obs in data["results"]
    ), "Aggregated flags should be present on all frames when 'with_aggregated_flags' param is set"
    assert all(
        obs["keyframeDetails"] is not None for obs in data["results"]
    ), "Keyframe details should be present on all frames when 'with_keyframe_details' param is set"
    assert all(obs["keyframeDetails"][0]["videoFrameDetails"] is not None for obs in data["results"]), (
        "Video frame details be present on all keyframes when both 'with_keyframe_details' and "
        "'with_video_frame_details' params are set"
    )

    with_flags_set = [obs for obs in data["results"] if obs["aggregatedFlags"]["atJoint"] is True]
    assert len(with_flags_set) == 1, "Only one observation should have 'atJoint' flag set to True"
    assert with_flags_set[0]["aggregatedFlags"]["hasLossOfVision"] is True
    assert with_flags_set[0]["aggregatedFlags"]["hasTextbox"] is False
    assert with_flags_set[0]["aggregatedFlags"]["hasTitle"] is False

    assert all(
        {kf["id"] for kf in obs["keyframeDetails"]} == set(obs["keyframes"]) for obs in data["results"]
    ), "The keyframes included in the keyframe details should match the observation's keyframes by ID"
