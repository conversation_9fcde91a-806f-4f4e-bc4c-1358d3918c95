import uuid

import pytest
from rest_framework import status

from api.defects.models import StandardSubcategory, SubFeatureUnit, Standard, SubFeatureKind
from api.inspections.models import Inspection
from api.organisations.models import Organisations
from api.tests import factory

pytestmark = pytest.mark.django_db


@pytest.fixture
def inspection_and_file_ids(asset_owner_org) -> tuple[uuid.UUID, int]:
    insp = factory.create_inspections(org=asset_owner_org, n=1)[0]
    return insp.uuid, insp.file_id


@pytest.fixture
def org_id(asset_owner_org: Organisations) -> int:
    return asset_owner_org.id


@pytest.fixture
def standard_subcategory_id() -> int:
    substd = StandardSubcategory.objects.get(
        pipe_type_sewer=True,
        material_type="Rigid",
        standard_key__name="WSA-05 2013",
    )
    return substd.id


@pytest.fixture
def standard_id() -> int:
    return Standard.objects.get(
        name="WSA-05 2013",
    ).id


@pytest.fixture
def asset_id(asset_owner_org: Organisations) -> uuid.UUID:
    asset = factory.create_assets(org=asset_owner_org, n=1)[0]
    return asset.uuid


@pytest.fixture
def folder_id(folder) -> int:
    return folder.id


def test_write_frame_data_workflow(
    client,
    inspection_settings,
    footage_settings,
    defect_settings,
    observation_settings,
    service_user_key: str,
    org_id: int,
    inspection_and_file_ids: tuple[uuid.UUID, int],
    standard_subcategory_id: int,
):
    """
    Test the end-to-end workflow of creating frames, keyframes and observations, starting from a state where we have a
    bare inspection and a video file.
    """

    # Assume this is the input data we have to work with:
    inspection_id, video_id = inspection_and_file_ids
    keyframe_data = [
        {
            "parent_video": video_id,
            "frame_id": 1,
            "file_name": "frame_1.jpg",
            "chainage_number": 0.2,
            "class_certainty": 0.95,
            "time_reference": 0,
            "at_joint": True,
            "loss_of_vision": False,
            "has_title": True,
            "has_textbox": False,
            "is_hidden": False,
        },
        {
            "parent_video": video_id,
            "frame_id": 2,
            "file_name": "frame_2.jpg",
            "chainage_number": 0.5,
            "class_certainty": 0.85,
            "time_reference": 1350,
            "at_joint": False,
            "loss_of_vision": False,
            "has_title": False,
            "has_textbox": True,
            "is_hidden": False,
        },
        {
            "parent_video": video_id,
            "frame_id": 3,
            "file_name": "frame_3.jpg",
            "chainage_number": 0.8,
            "class_certainty": 0.90,
            "time_reference": 2000,
            "at_joint": False,
            "loss_of_vision": True,
            "has_title": False,
            "has_textbox": False,
            "is_hidden": True,
        },
    ]

    # Create the physical video frames
    frame_create_resp = client.post(
        inspection_settings.get_post_video_frames_url(video_id),
        data=[
            {
                "parentVideo": frame["parent_video"],
                "frameId": frame["frame_id"],
                "imageLocation": frame["file_name"],
                "chainageNumber": frame["chainage_number"],
                "classCertainty": frame["class_certainty"],
                "isHidden": frame["is_hidden"],
            }
            for frame in keyframe_data
        ],
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert frame_create_resp.status_code == status.HTTP_201_CREATED
    frame_create_resp_data = frame_create_resp.json()

    vid_frame_ids = [frame["id"] for frame in frame_create_resp_data]

    # Create the footage record
    footage_create_resp = client.post(
        footage_settings.footage_base_url,
        data={
            "videoFileId": video_id,
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert footage_create_resp.status_code == status.HTTP_201_CREATED
    footage_id = footage_create_resp.json()["id"]

    # Link the inspection to the footage - this can be done when creating the inspection, or after like here.
    insp_footage_link_resp = client.patch(
        inspection_settings.inspection_url.format(inspection_uuid=inspection_id),
        data={
            "footage": footage_id,
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert insp_footage_link_resp.status_code == status.HTTP_200_OK

    # Create the corresponding keyframes
    keyframes_create_resp = client.put(
        footage_settings.get_footage_keyframes_url(footage_id),
        data=[
            {
                "timeReferenceMilliseconds": frame["time_reference"],
                "isHidden": frame["is_hidden"],
                "atJoint": frame["at_joint"],
                "hasLossOfVision": frame["loss_of_vision"],
                "hasTextbox": frame["has_textbox"],
                "hasTitle": frame["has_title"],
                "videoFrameId": vid_frame_id,
            }
            for frame, vid_frame_id in zip(keyframe_data, vid_frame_ids)
        ],
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert keyframes_create_resp.status_code == status.HTTP_201_CREATED
    keyframe_ids = [kf["id"] for kf in keyframes_create_resp.json()]

    # Fetch features data
    features_data_resp = client.get(
        defect_settings.features_url,
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
    )
    assert features_data_resp.status_code == status.HTTP_200_OK
    features_data = features_data_resp.json()

    # Assume we want to create a crack and deformation observation. In practise we will have to use the mapped ml labels
    # to look up the correct features and sub-features for each keyframe.
    cracking_feature = next(feat for feat in features_data if feat["key"] == "crack_fracture")
    crack_width_sf = next(sf for sf in cracking_feature["subFeatures"] if sf["key"] == "crack_fracture_width")
    crack_type_sf = next(sf for sf in cracking_feature["subFeatures"] if sf["key"] == "crack_fracture_type")
    crack_type_option_longitudinal = next(
        opt for opt in crack_type_sf["options"] if opt["key"] == "crack_fracture_type_longitudinal"
    )

    deformation_feature = next(feat for feat in features_data if feat["key"] == "deformation")
    deformation_amt_sf = next(sf for sf in deformation_feature["subFeatures"] if sf["key"] == "deformation_magnitude")
    deformation_length_sf = next(sf for sf in deformation_feature["subFeatures"] if sf["key"] == "deformation_length")

    # Fetch codes data
    codes_data_resp = client.get(
        defect_settings.get_code_variants_url(standard_subcategory_id),
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
    )
    assert codes_data_resp.status_code == status.HTTP_200_OK
    codes_data = codes_data_resp.json()

    # Here we just grab the first code and scoring for each feature, as an example.
    # In practise we will need to filter to the right one based on the sub-features

    crack_code = next(c for c in codes_data if c["code"]["featureId"] == cracking_feature["id"])
    crack_scoring = next(v["scoring"] for v in crack_code["variants"])

    deformation_code = next(c for c in codes_data if c["code"]["featureId"] == deformation_feature["id"])
    deformation_scoring = next(v["scoring"] for v in deformation_code["variants"])

    observations_to_create = [
        {
            "keyframes": [keyframe_ids[0]],
            "remarks": "",
            "features": [
                {
                    "feature": cracking_feature["id"],
                    "featureClassPredictionCertainty": 0.95,
                    "code": crack_code["code"]["id"],
                    "scoring": crack_scoring["id"],
                    "subFeatures": [
                        {
                            "subFeature": crack_width_sf["id"],
                            "numericValue": 20.0,
                            "numericUnit": SubFeatureUnit.MILLIMETRES.value,
                        },
                        {
                            "subFeature": crack_type_sf["id"],
                            "selectedOption": crack_type_option_longitudinal["id"],
                        },
                    ],
                },
            ],
        },
        {
            "keyframes": [keyframe_ids[1]],
            "remarks": "",
            "features": [
                {
                    "feature": deformation_feature["id"],
                    "featureClassPredictionCertainty": 0.85,
                    "code": deformation_code["code"]["id"],
                    "scoring": deformation_scoring["id"],
                    "subFeatures": [
                        {
                            "subFeature": deformation_amt_sf["id"],
                            "numericValue": 0.3,  # 30%
                            "numericUnit": SubFeatureUnit.PERCENTAGE.value,
                        },
                        {
                            "subFeature": deformation_length_sf["id"],
                            "numericValue": 1.2,
                            "numericUnit": SubFeatureUnit.MILLIMETRES.value,
                        },
                    ],
                },
            ],
        },
    ]

    observations_resp = client.put(
        observation_settings.get_inspection_observations_url(inspection_id),
        data=observations_to_create,
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert observations_resp.status_code == status.HTTP_201_CREATED

    ### VERIFY CREATED DATA ###

    inspection = Inspection.objects.get(pk=inspection_id)
    assert str(inspection.footage.id) == footage_id, "Inspection should be linked to the created footage"

    footage = inspection.footage
    assert footage.video_file.id == video_id, "Video file should be linked to the created footage"
    assert footage.keyframes.count() == 3, "Footage should have 3 keyframes created"

    assert footage.video_file.videoframes_set.count() == 3, "Video file should have 3 video frames created"

    keyframes = list(footage.keyframes.all().order_by("sequence_number"))

    assert keyframes[0].time_reference_milliseconds == 0
    assert keyframes[1].time_reference_milliseconds == 1350
    assert keyframes[2].time_reference_milliseconds == 2000

    # Check the keyframes are linked to the correct video frames
    assert keyframes[0].video_frame.image_location == "frame_1.jpg"
    assert keyframes[1].video_frame.image_location == "frame_2.jpg"
    assert keyframes[2].video_frame.image_location == "frame_3.jpg"

    assert inspection.observations.count() == 2, "There should be 2 observations created"
    observations = list(inspection.observations.all())
    # Check the observations are linked to the correct keyframes
    assert observations[0].keyframes.count() == 1
    assert observations[0].keyframes.first().video_frame.image_location == "frame_1.jpg"
    assert observations[1].keyframes.count() == 1
    assert observations[1].keyframes.first().video_frame.image_location == "frame_2.jpg"

    # Check the features and sub-features are correctly set
    obs_1, obs_2 = observations

    assert obs_1.features.count() == 1
    obs_1_feature = obs_1.features.first()
    assert obs_1_feature.feature.key == "crack_fracture", "Observation 1 should have the crack feature"
    assert obs_1_feature.feature_class_prediction_certainty == 0.95
    assert obs_1_feature.code.id == crack_code["code"]["id"]
    assert obs_1_feature.scoring.id == crack_scoring["id"]
    assert obs_1_feature.sub_features.count() == 2
    crack_width_sf = obs_1_feature.sub_features.get(sub_feature__key="crack_fracture_width")
    assert crack_width_sf.numeric_value == 20.0
    assert crack_width_sf.numeric_unit == SubFeatureUnit.MILLIMETRES.value
    crack_type_sf = obs_1_feature.sub_features.get(sub_feature__key="crack_fracture_type")
    assert crack_type_sf.selected_option.id == crack_type_option_longitudinal["id"]

    assert obs_2.features.count() == 1
    obs_2_feature = obs_2.features.first()
    assert obs_2_feature.feature.key == "deformation", "Observation 2 should have the deformation feature"
    assert obs_2_feature.feature_class_prediction_certainty == 0.85
    assert obs_2_feature.code.id == deformation_code["code"]["id"]
    assert obs_2_feature.scoring.id == deformation_scoring["id"]
    assert obs_2_feature.sub_features.count() == 2
    deformation_amt_sf = obs_2_feature.sub_features.get(sub_feature__key="deformation_magnitude")
    assert deformation_amt_sf.numeric_value == 0.3
    assert deformation_amt_sf.numeric_unit == SubFeatureUnit.PERCENTAGE.value
    deformation_length_sf = obs_2_feature.sub_features.get(sub_feature__key="deformation_length")
    assert deformation_length_sf.numeric_value == 1.2
    assert deformation_length_sf.numeric_unit == SubFeatureUnit.MILLIMETRES.value


def test_import_inspection_data_workflow(
    client,
    inspection_settings,
    footage_settings,
    defect_settings,
    observation_settings,
    service_user_key: str,
    org_id: int,
    standard_subcategory_id: int,
    standard_id: int,
    asset_id,
    folder_id: int,
):
    """
    Tests the entire inspection import workflow, replicating how we will have to lookup the features and sub-features
    to apply
    """
    parsed_import_data = {
        "Direction": "Upstream",
        "Date": "2023-10-01",
        "LengthSurveyed": 100.0,
        "GeneralRemarks": "Test import inspection",
        "Observations": [
            {
                "VideoRef": "0:00:00",
                "Distance": 3.5,
                "Code": "WLF",
                "Remarks": "Water level frame",
                "Dimension1": 20.0,  # Depth in MM
            },
            {
                "VideoRef": "0:02:10",
                "Distance": 5.0,
                "Code": "CL",
                "Remarks": "Crack",
                "ClockRefFrom": 2,
                "ClockRefTo": 3,
                "Dimension1": 20.0,  # Width in MM
                "ContinuousDefect": "S01",
            },
            {
                "VideoRef": "0:02:20",
                "Distance": 12.0,
                "Code": "CL",
                "Remarks": "Crack",
                "ClockRefFrom": 2,
                "ClockRefTo": 3,
                "ContinuousDefect": "F01",
            },
        ],
    }

    # Create blank inspection
    create_insp_resp = client.post(
        inspection_settings.get_inspections_post_url(asset_id, None, folder_id),
        data={
            "Direction": parsed_import_data["Direction"],
            "Date": parsed_import_data["Date"],
            "LengthSurveyed": parsed_import_data["LengthSurveyed"],
            "GeneralRemarks": parsed_import_data["GeneralRemarks"],
        },
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
    )
    assert create_insp_resp.status_code == status.HTTP_201_CREATED
    create_insp_data = create_insp_resp.json()
    inspection_id = create_insp_data["uuid"]

    # Create the footage record
    footage_create_resp = client.post(
        footage_settings.footage_base_url,
        data={},
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert footage_create_resp.status_code == status.HTTP_201_CREATED
    footage_id = footage_create_resp.json()["id"]

    # Link the inspection to the footage
    insp_footage_link_resp = client.patch(
        inspection_settings.inspection_url.format(inspection_uuid=inspection_id),
        data={
            "footage": footage_id,
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert insp_footage_link_resp.status_code == status.HTTP_200_OK

    keyframes_create_resp = client.put(
        footage_settings.get_footage_keyframes_url(footage_id),
        data=[
            {
                "timeReferenceMilliseconds": 0,
            },
            {
                "timeReferenceMilliseconds": 13000,
            },
            {
                "timeReferenceMilliseconds": 30000,
            },
        ],
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert keyframes_create_resp.status_code == status.HTTP_201_CREATED
    keyframe_ids = [kf["id"] for kf in keyframes_create_resp.json()]

    # Fetch features data
    features_data_resp = client.get(
        defect_settings.features_url + f"?with_standard_id={standard_id}",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
    )
    assert features_data_resp.status_code == status.HTTP_200_OK
    features_data = features_data_resp.json()

    # Fetch codes data
    codes_data_resp = client.get(
        defect_settings.get_code_variants_url(standard_subcategory_id),
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
    )
    assert codes_data_resp.status_code == status.HTTP_200_OK
    codes_data = codes_data_resp.json()

    codes_by_display_code = {c["code"]["displayCode"]: c for c in codes_data}
    features_by_id = {f["id"]: f for f in features_data}

    water_level_feature = next(feat for feat in features_data if feat["key"] == "water_level")
    crack_feature = next(feat for feat in features_data if feat["key"] == "crack_fracture")

    grouped_observations = []
    for kf_id, obs in zip(keyframe_ids, parsed_import_data["Observations"]):
        if cont_tag := obs.get("ContinuousDefect"):
            is_start_tag = cont_tag[0] == "S"
            number = cont_tag[1:]
            if is_start_tag:
                grouped_observations.append({"is_continuous": True, "defects": [{"kf_id": kf_id, "observation": obs}]})
            else:
                # Find the defect that matches the start tag of this continuous tag
                matched = next(
                    (
                        g
                        for g in grouped_observations
                        if g["is_continuous"] and g["defects"][0]["observation"]["ContinuousDefect"] == f"S{number}"
                    ),
                    None,
                )
                if not matched:
                    raise Exception("No matching start tag found")
                matched["defects"].append({"kf_id": kf_id, "observation": obs})
        else:
            grouped_observations.append({"is_continuous": False, "defects": [{"kf_id": kf_id, "observation": obs}]})

    # Find the subfeatures, code, and scoring to apply for each observation
    for grouped in grouped_observations:
        obs = grouped["defects"][0]["observation"]
        code = codes_by_display_code.get(obs["Code"])
        if not code:
            continue  # We can't create an observation since this doesn't map to anything we recognise

        feature = features_by_id.get(code["code"]["featureId"])
        if not feature:
            continue  # No feature for this code?

        quantities = {}  # Map of subfeature id to subfeatures that were included in the source data
        if "Dimension1" in obs:
            sf = next((
                sf for sf in feature.get("subFeatures", [])
                if (
                    sf
                    and isinstance(sf.get("standardised"), dict)
                    and sf["standardised"].get("quantityFieldNumber") == 1
                )
            ), None)
            if sf:
                quantities[sf["id"]] = {"sf": sf, "value": obs["Dimension1"]}
        if "Dimension2" in obs:
            sf = next((
                sf for sf in feature.get("subFeatures", [])
                if (
                    sf
                    and isinstance(sf.get("standardised"), dict)
                    and sf["standardised"].get("quantityFieldNumber") == 2
                )
            ), None)
            if sf:
                quantities[sf["id"]] = {"sf": sf, "value": obs["Dimension2"]}
        if "Percentage" in obs:
            sf = next((
                sf for sf in feature.get("subFeatures", [])
                if (
                    sf
                    and isinstance(sf.get("standardised"), dict)
                    and sf["standardised"].get("isReportPercentField")
                )
            ), None)
            if sf:
                quantities[sf["id"]] = {"sf": sf, "value": obs["Percentage"]}

        subfeatures = []
        for req in code["codeRequirements"]:
            if req["subFeatureId"] not in quantities:
                # Add this subfeature
                sf = next((sf for sf in feature["subFeatures"] if sf["id"] == req["subFeatureId"]), None)
                if not sf:
                    continue  # No such subfeature?

                if sf["kind"] == SubFeatureKind.CATEGORICAL:
                    subfeatures.append(
                        {
                            "subFeature": sf["id"],
                            "selectedOption": req["requiredOptionId"],
                        }
                    )
                else:  # NUMERIC
                    subfeatures.append(
                        {
                            "subFeature": sf["id"],
                            "numericMin": req["numericOptionRangeMin"],
                            "numericMax": req["numericOptionRangeMax"],
                            "numericUnit": sf["numericUnit"]["standardised"]["numericUnit"],
                        }
                    )

        for quant in quantities.values():
            subfeatures.append(
                {
                    "subFeature": quant["sf"]["id"],
                    "numericUnit": quant["sf"]["standardised"]["numericUnit"],
                    "numericValue": quant["value"],
                }
            )

        grouped["subfeatures"] = subfeatures

    observations_to_create = []
    for grouped in grouped_observations:
        obs = grouped["defects"][0]["observation"]
        code = codes_by_display_code.get(obs["Code"])

        feature = features_by_id.get(code["code"]["featureId"])

        observations_to_create.append(
            {
                "keyframes": [defect["kf_id"] for defect in grouped["defects"]],
                "remarks": obs["Remarks"],
                "features": [
                    {
                        "feature": feature["id"],
                        "featureClassPredictionCertainty": None,
                        "code": code["code"]["id"],
                        "subFeatures": [
                            sf for sf in grouped.get("subfeatures", [])
                            if "numericValue" not in sf or sf.get("numericUnit") in [u.value for u in SubFeatureUnit]
                        ]
                        # TODO: Scoring
                    }
                ],
            }
        )

    obs_create_resp = client.put(
        observation_settings.get_inspection_observations_url(inspection_id),
        data=observations_to_create,
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org_id,
        content_type="application/json",
    )
    assert obs_create_resp.status_code == status.HTTP_201_CREATED

    ### VERIFY CREATED DATA ###

    # Refresh inspection and related objects from the database
    inspection = Inspection.objects.get(pk=inspection_id)
    footage = inspection.footage
    assert footage is not None, "Footage should be linked to the inspection"
    assert footage.video_file is None, "No physical video file"

    # Verify keyframes
    keyframes = list(footage.keyframes.all().order_by("time_reference_milliseconds"))
    assert len(keyframes) == 3, "Should have 3 keyframes"
    assert keyframes[0].time_reference_milliseconds == 0
    assert keyframes[1].time_reference_milliseconds == 13000
    assert keyframes[2].time_reference_milliseconds == 30000

    # Verify observations
    assert inspection.observations.count() == 2, "Should have 2 observations"

    # Verify water level observation
    water_level_obs = inspection.observations.get(features__feature__key=water_level_feature["key"])
    assert water_level_obs.remarks == parsed_import_data["Observations"][0]["Remarks"]
    assert water_level_obs.keyframes.count() == 1
    assert water_level_obs.keyframes.first().time_reference_milliseconds == 0

    assert water_level_obs.features.count() == 1
    assert water_level_obs.features.first().feature_id == water_level_feature["id"]
    water_level_feat = water_level_obs.features.first()

    assert water_level_feat.sub_features.count() == 2
    wl_depth = water_level_feat.sub_features.get(sub_feature__key="water_level_depth")
    assert wl_depth.numeric_value == parsed_import_data["Observations"][0]["Dimension1"]
    assert wl_depth.numeric_unit == SubFeatureUnit.MILLIMETRES.value

    # Verify crack observation
    crack_obs = inspection.observations.get(features__feature__key=crack_feature["key"])
    assert crack_obs.remarks == parsed_import_data["Observations"][1]["Remarks"]
    assert crack_obs.keyframes.count() == 2
    keyframe_times = [kf.time_reference_milliseconds for kf in crack_obs.keyframes.all()]
    assert set(keyframe_times) == {13000, 30000}, "Crack observation should be linked to keyframes at 13s and 30s"

    assert crack_obs.features.count() == 1
    crack_feat = crack_obs.features.first()
    assert crack_feat.feature_id == crack_feature["id"]

    assert crack_feat.sub_features.count() == 2, "Crack feature should have 2 subfeatures"

    # Verify crack width sub-feature
    crack_width = crack_feat.sub_features.get(sub_feature__key="crack_fracture_width")
    assert crack_width.numeric_value == 20.0
    assert crack_width.numeric_unit == SubFeatureUnit.MILLIMETRES.value

    # Verify crack type sub-feature
    crack_type = crack_feat.sub_features.get(sub_feature__key="crack_fracture_type")
    assert crack_type.selected_option.key == "crack_fracture_type_longitudinal"
