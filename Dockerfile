FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

ARG DB_HOST
ARG DB_NAME
ARG DB_USER
ARG DB_PORT
ARG BITBUCKET_VAPAR_REPO_TOKEN

# Set work directory
WORKDIR /app


# Install system dependencies in one layer
RUN set -ex && \
    apt-get update -y && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    gdal-bin \
    binutils \
    libproj-dev \
    postgresql-client \
    python3-pip \
    python3-cffi \
    python3-brotli \
    git && \
    apt-get update --fix-missing && \
    rm -rf /var/lib/apt/lists/*

# Copy just requirements first so it can be cached
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Copy the whole project over
COPY . .

EXPOSE 8000

# Add any static environment variables needed by Djan<PERSON> or your settings file here:
ENV DJANGO_SETTINGS_MODULE=config.settings

CMD ["sh", "-e", "entrypoint.sh"]